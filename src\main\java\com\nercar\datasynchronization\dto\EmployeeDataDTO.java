package com.nercar.datasynchronization.dto;

import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * 员工数据传输对象
 * 用于返回解析后的员工数据给外部系统
 */
@Data
public class EmployeeDataDTO {

    /**
     * MDM系统中的员工唯一标识
     */
    private String mdmId;

    /**
     * 所属组织MDM ID
     */
    private String mdmHrdwnm;

    /**
     * 员工编号
     */
    private String employeeCode;

    /**
     * 员工姓名
     */
    private String employeeName;

    /**
     * 性别：1=男，2=女
     */
    private String gender;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 证件类型
     */
    private String status;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 账号
     */
    private String account;

    /**
     * 出生日期
     */
    private Date birthDate;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 组织类型
     */
    private String orgType;

    /**
     * 组织级别1
     */
    private String orgLevel1;

    /**
     * 组织级别2
     */
    private String orgLevel2;

    /**
     * 组织级别3
     */
    private String orgLevel3;

    /**
     * 微信号
     */
    private String wechat;

    /**
     * 电话
     */
    private String tel;

    /**
     * 备注
     */
    private String note;

    /**
     * 是否停用：0=启用，1=禁用
     */
    private String isDisabled;

    /**
     * 操作标识：N=新增，U=修改，D=删除
     */
    private String userType;

    /**
     * 工作状态
     */
    private String orgCode;

    /**
     * 判重项目值域
     */
    private String idName;

    /**
     * 用户分类
     */
    private String userCategory;

    /**
     * 用户级别
     */
    private String userLevel;

    /**
     * 用户状态
     */
    private String userStatus;

    /**
     * 员工职位信息列表
     */
    private List<EmployeePositionDTO> positions;

    /**
     * 员工职称信息列表
     */
    private List<EmployeeTitleDTO> titles;

    /**
     * 员工系统信息列表
     */
    private List<EmployeeSystemDTO> systems;
}
