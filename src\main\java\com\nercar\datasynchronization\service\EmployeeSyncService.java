package com.nercar.datasynchronization.service;

import java.util.Date;

public interface EmployeeSyncService {
    
    /**
     * 同步员工数据
     *
     * @param xmlData XML数据
     */
    void syncEmployees(String xmlData);
    
    /**
     * 同步员工数据，并指定数据的时间范围
     *
     * @param xmlData XML数据
     * @param startDate 数据开始时间
     * @param endDate 数据结束时间
     */
    void syncEmployees(String xmlData, Date startDate, Date endDate);
}