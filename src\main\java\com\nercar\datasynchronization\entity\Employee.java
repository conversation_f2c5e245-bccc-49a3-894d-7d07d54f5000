package com.nercar.datasynchronization.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.util.Date;

/**
 * 员工实体类 - 存储员工基本信息
 * 对应外部MDM系统中的员工主数据，文档1.5.3人员信息下发接口的主表
 */
@Data
@Entity
@Table(name = "employee")
@DynamicInsert
@DynamicUpdate
public class Employee {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "mdm_id")
    private String mdmId;

    @Column(name = "mdm_hrdwnm")
    private String mdmHrdwnm;

    @Column(name = "employee_code")
    private String employeeCode;

    @Column(name = "employee_name")
    private String employeeName;

    @Column(name = "gender")
    private String gender;

    @Column(name = "mobile")
    private String mobile;

    @Column(name = "status")
    private String status;

    @Column(name = "id_card")
    private String idCard;

    @Column(name = "account")
    private String account;

    @Column(name = "birth_date")
    @Temporal(TemporalType.DATE)
    private Date birthDate;

    @Column(name = "email")
    private String email;

    @Column(name = "org_type")
    private String orgType;

    @Column(name = "org_level1")
    private String orgLevel1;

    @Column(name = "org_level2")
    private String orgLevel2;

    @Column(name = "org_level3")
    private String orgLevel3;

    @Column(name = "wechat")
    private String wechat;

    @Column(name = "tel")
    private String tel;

    @Column(name = "note")
    private String note;

    @Column(name = "is_disabled")
    private String isDisabled;

    @Column(name = "user_type")
    private String userType;

    @Column(name = "org_code")
    private String orgCode;

    @Column(name = "id_name")
    private String idName;

    @Column(name = "created_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdTime;

    @Column(name = "updated_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedTime;

    @Column(name = "user_category")
    private String userCategory;

    @Column(name = "user_level")
    private String userLevel;

    @Column(name = "user_status")
    private String userStatus;
}