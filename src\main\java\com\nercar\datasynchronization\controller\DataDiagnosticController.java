package com.nercar.datasynchronization.controller;

import com.nercar.datasynchronization.service.DataDiagnosticService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 数据诊断控制器
 * 用于分析部门数据同步不全的问题
 */
@RestController
@RequestMapping("/api/diagnostic")
@RequiredArgsConstructor
@Slf4j
public class DataDiagnosticController {

    private final DataDiagnosticService diagnosticService;

    /**
     * 执行完整的数据诊断分析
     */
    @GetMapping("/full-analysis")
    public ResponseEntity<Map<String, Object>> performFullDiagnosis() {
        try {
            log.info("开始执行完整数据诊断分析");
            Map<String, Object> result = diagnosticService.performFullDiagnosis();
            log.info("完整数据诊断分析完成");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("执行完整数据诊断分析失败", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "诊断分析失败: " + e.getMessage()));
        }
    }

    /**
     * 获取基础统计信息
     */
    @GetMapping("/basic-stats")
    public ResponseEntity<Map<String, Object>> getBasicStatistics() {
        try {
            Map<String, Object> stats = diagnosticService.getBasicStatistics();
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            log.error("获取基础统计信息失败", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "获取统计信息失败: " + e.getMessage()));
        }
    }

    /**
     * 获取状态分析
     */
    @GetMapping("/status-analysis")
    public ResponseEntity<Map<String, Object>> getStatusAnalysis() {
        try {
            Map<String, Object> analysis = diagnosticService.getStatusAnalysis();
            return ResponseEntity.ok(analysis);
        } catch (Exception e) {
            log.error("获取状态分析失败", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "状态分析失败: " + e.getMessage()));
        }
    }

    /**
     * 获取层级关系分析
     */
    @GetMapping("/hierarchy-analysis")
    public ResponseEntity<Map<String, Object>> getHierarchyAnalysis() {
        try {
            Map<String, Object> analysis = diagnosticService.getHierarchyAnalysis();
            return ResponseEntity.ok(analysis);
        } catch (Exception e) {
            log.error("获取层级关系分析失败", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "层级关系分析失败: " + e.getMessage()));
        }
    }

    /**
     * 获取数据质量问题
     */
    @GetMapping("/data-quality-issues")
    public ResponseEntity<Map<String, Object>> getDataQualityIssues() {
        try {
            Map<String, Object> issues = diagnosticService.getDataQualityIssues();
            return ResponseEntity.ok(issues);
        } catch (Exception e) {
            log.error("获取数据质量问题失败", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "数据质量分析失败: " + e.getMessage()));
        }
    }

    /**
     * 获取可能遗漏的数据
     */
    @GetMapping("/possibly-missing-data")
    public ResponseEntity<Map<String, Object>> getPossiblyMissingData() {
        try {
            Map<String, Object> missing = diagnosticService.getPossiblyMissingData();
            return ResponseEntity.ok(missing);
        } catch (Exception e) {
            log.error("获取可能遗漏数据失败", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "遗漏数据分析失败: " + e.getMessage()));
        }
    }

    /**
     * 分析特定组织代码的历史变更
     */
    @GetMapping("/org-code-history/{orgCode}")
    public ResponseEntity<Map<String, Object>> analyzeOrgCodeHistory(@PathVariable String orgCode) {
        try {
            Map<String, Object> analysis = diagnosticService.analyzeOrgCodeHistory(orgCode);
            return ResponseEntity.ok(analysis);
        } catch (Exception e) {
            log.error("分析组织代码历史失败: {}", orgCode, e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "组织代码历史分析失败: " + e.getMessage()));
        }
    }

    /**
     * 生成诊断报告（简化版）
     */
    @GetMapping("/summary-report")
    public ResponseEntity<Map<String, Object>> generateSummaryReport() {
        try {
            Map<String, Object> basicStats = diagnosticService.getBasicStatistics();
            Map<String, Object> statusAnalysis = diagnosticService.getStatusAnalysis();
            Map<String, Object> hierarchyAnalysis = diagnosticService.getHierarchyAnalysis();
            Map<String, Object> possiblyMissing = diagnosticService.getPossiblyMissingData();

            Map<String, Object> summary = Map.of(
                    "总部门数", basicStats.get("totalDepartments"),
                    "当前迁移数量", basicStats.get("activeDepartments"),
                    "覆盖率", basicStats.get("coverageRate"),
                    "可能遗漏数量", possiblyMissing.get("potentialAdditionalData"),
                    "孤儿部门数量", hierarchyAnalysis.get("orphanDepartmentsCount"),
                    "可能误过滤数量", statusAnalysis.get("possiblyMissingCount"),
                    "建议", generateRecommendations(basicStats, statusAnalysis, hierarchyAnalysis, possiblyMissing)
            );

            return ResponseEntity.ok(summary);
        } catch (Exception e) {
            log.error("生成诊断报告失败", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "生成报告失败: " + e.getMessage()));
        }
    }

    /**
     * 生成建议
     */
    private String generateRecommendations(Map<String, Object> basicStats, 
                                         Map<String, Object> statusAnalysis,
                                         Map<String, Object> hierarchyAnalysis,
                                         Map<String, Object> possiblyMissing) {
        StringBuilder recommendations = new StringBuilder();

        // 检查覆盖率
        String coverageRate = (String) basicStats.get("coverageRate");
        double coverage = Double.parseDouble(coverageRate.replace("%", ""));
        if (coverage < 90) {
            recommendations.append("1. 覆盖率偏低(").append(coverageRate).append(")，建议检查过滤条件；");
        }

        // 检查可能遗漏的数据
        Long potentialAdditional = (Long) possiblyMissing.get("potentialAdditionalData");
        if (potentialAdditional > 0) {
            recommendations.append("2. 发现").append(potentialAdditional).append("个可能被误过滤的部门，建议放宽user_predef_14条件；");
        }

        // 检查孤儿部门
        Integer orphanCount = (Integer) hierarchyAnalysis.get("orphanDepartmentsCount");
        if (orphanCount > 0) {
            recommendations.append("3. 发现").append(orphanCount).append("个孤儿部门，需要修复父子关系；");
        }

        // 检查误过滤
        Integer possiblyMissingCount = (Integer) statusAnalysis.get("possiblyMissingCount");
        if (possiblyMissingCount > 0) {
            recommendations.append("4. 发现").append(possiblyMissingCount).append("个可能误过滤的部门(isHistory=0但userPredef14=D)；");
        }

        if (recommendations.length() == 0) {
            recommendations.append("数据质量良好，当前过滤条件合理。");
        }

        return recommendations.toString();
    }
}
