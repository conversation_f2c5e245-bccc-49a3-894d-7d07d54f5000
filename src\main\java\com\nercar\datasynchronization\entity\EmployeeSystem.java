package com.nercar.datasynchronization.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.util.Date;

/**
 * 员工系统标识实体 - 存储员工在其他系统中的标识信息
 * 对应文档1.5.3人员信息下发接口中的从表3（映射表）
 */
@Data
@Entity
@Table(name = "employee_system")
@DynamicInsert
@DynamicUpdate
public class EmployeeSystem {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "guid")
    private String guid;

    @Column(name = "employee_mdm_id")
    private String employeeMdmId;

    @Column(name = "system_code")
    private String systemCode;

    @Column(name = "system_data_id")
    private String systemDataId;

    @Column(name = "employee_code")
    private String employeeCode;

    @Column(name = "org_code")
    private String orgCode;

    @Column(name = "department_code")
    private String departmentCode;

    @Column(name = "login_account")
    private String loginAccount;

    @Column(name = "created_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdTime;

    @Column(name = "updated_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedTime;

    @Column(name = "company_code")
    private String companyCode;
}