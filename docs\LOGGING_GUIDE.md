# 日志使用指南

## 概述

系统现在提供了详细的日志记录功能，让您能够清楚地了解数据同步和获取的详细过程。

## 日志文件结构

### 主要日志文件

1. **data-retrieval-api.log** - 数据获取API专用日志
   - 记录API请求和响应详情
   - 包含数据解析过程和统计信息
   - 记录各表的数据条数

2. **sync-service.log** - 数据同步服务日志
   - 记录数据同步过程
   - 包含数据库操作详情

3. **error.log** - 错误日志
   - 记录所有ERROR级别的日志
   - 便于快速定位问题

4. **data-synchronization.log** - 主日志文件
   - 记录所有系统日志

## 数据获取API日志详情

### 部门数据获取日志示例

```
2024-01-01 10:00:00.123 [http-nio-8080-exec-1] INFO  DataRetrievalController - 🔍 [API请求] 获取部门数据 - 原始参数: startDate=2024-01-01 00:00:00, endDate=2024-01-02 00:00:00
2024-01-01 10:00:00.124 [http-nio-8080-exec-1] INFO  DataRetrievalController - 📅 [日期解析] 成功解析日期参数 - 开始时间: Mon Jan 01 00:00:00 CST 2024, 结束时间: Tue Jan 02 00:00:00 CST 2024
2024-01-01 10:00:00.125 [http-nio-8080-exec-1] INFO  DataRetrievalController - 🚀 [服务调用] 开始调用数据获取服务...
2024-01-01 10:00:00.126 [http-nio-8080-exec-1] INFO  DataRetrievalServiceImpl - === 开始获取部门数据 ===
2024-01-01 10:00:00.127 [http-nio-8080-exec-1] INFO  DataRetrievalServiceImpl - 查询时间范围: Mon Jan 01 00:00:00 CST 2024 至 Tue Jan 02 00:00:00 CST 2024
2024-01-01 10:00:00.128 [http-nio-8080-exec-1] INFO  DataRetrievalServiceImpl - 正在调用远程SOAP接口获取部门数据...
2024-01-01 10:00:02.456 [http-nio-8080-exec-1] INFO  DataRetrievalServiceImpl - 远程SOAP接口调用成功，响应数据长度: 15234 字符
2024-01-01 10:00:02.457 [http-nio-8080-exec-1] INFO  DataRetrievalServiceImpl - 正在解析SOAP响应，提取XML数据...
2024-01-01 10:00:02.458 [http-nio-8080-exec-1] INFO  DataRetrievalServiceImpl - XML数据提取成功，数据长度: 12456 字符
2024-01-01 10:00:02.459 [http-nio-8080-exec-1] INFO  DataRetrievalServiceImpl - 正在解析XML数据为部门DTO对象...
2024-01-01 10:00:02.567 [http-nio-8080-exec-1] INFO  DataRetrievalServiceImpl - ✅ 部门数据解析完成:
2024-01-01 10:00:02.568 [http-nio-8080-exec-1] INFO  DataRetrievalServiceImpl -   - 主部门记录: 25 条 (对应 department 表)
2024-01-01 10:00:02.569 [http-nio-8080-exec-1] INFO  DataRetrievalServiceImpl -   - 子部门记录: 78 条 (对应 department_child 表)
2024-01-01 10:00:02.570 [http-nio-8080-exec-1] INFO  DataRetrievalServiceImpl -   - 总计: 103 条记录
2024-01-01 10:00:02.571 [http-nio-8080-exec-1] INFO  DataRetrievalServiceImpl - 部门数据示例:
2024-01-01 10:00:02.572 [http-nio-8080-exec-1] INFO  DataRetrievalServiceImpl -   - 部门: 技术部 | 代码: X50070000 | UUID: *************-4f25-815b-18339c8d76f4 | 层级: 3
2024-01-01 10:00:02.573 [http-nio-8080-exec-1] INFO  DataRetrievalServiceImpl -   - 部门: 财务部 | 代码: X50070001 | UUID: *************-4f25-815b-18339c8d76f5 | 层级: 3
2024-01-01 10:00:02.574 [http-nio-8080-exec-1] INFO  DataRetrievalServiceImpl -   - 部门: 人事部 | 代码: X50070002 | UUID: *************-4f25-815b-18339c8d76f6 | 层级: 3
2024-01-01 10:00:02.575 [http-nio-8080-exec-1] INFO  DataRetrievalServiceImpl -   - ... 还有 22 个部门
2024-01-01 10:00:02.576 [http-nio-8080-exec-1] INFO  DataRetrievalServiceImpl - === 部门数据获取完成，总计: 25条记录 ===
2024-01-01 10:00:02.577 [http-nio-8080-exec-1] INFO  DataRetrievalController - ✅ [API响应] 部门数据获取成功:
2024-01-01 10:00:02.578 [http-nio-8080-exec-1] INFO  DataRetrievalController -    - 主部门记录: 25 条
2024-01-01 10:00:02.579 [http-nio-8080-exec-1] INFO  DataRetrievalController -    - 子部门记录: 78 条
2024-01-01 10:00:02.580 [http-nio-8080-exec-1] INFO  DataRetrievalController -    - 响应总记录数: 25 条
```

### 员工数据获取日志示例

```
2024-01-01 10:05:00.123 [http-nio-8080-exec-2] INFO  DataRetrievalController - 🔍 [API请求] 获取员工数据 - 原始参数: startDate=2024-01-01 00:00:00, endDate=2024-01-02 00:00:00
2024-01-01 10:05:00.124 [http-nio-8080-exec-2] INFO  DataRetrievalController - 📅 [日期解析] 成功解析日期参数 - 开始时间: Mon Jan 01 00:00:00 CST 2024, 结束时间: Tue Jan 02 00:00:00 CST 2024
2024-01-01 10:05:00.125 [http-nio-8080-exec-2] INFO  DataRetrievalController - 🚀 [服务调用] 开始调用数据获取服务...
2024-01-01 10:05:00.126 [http-nio-8080-exec-2] INFO  DataRetrievalServiceImpl - === 开始获取员工数据 ===
2024-01-01 10:05:00.127 [http-nio-8080-exec-2] INFO  DataRetrievalServiceImpl - 查询时间范围: Mon Jan 01 00:00:00 CST 2024 至 Tue Jan 02 00:00:00 CST 2024
2024-01-01 10:05:00.128 [http-nio-8080-exec-2] INFO  DataRetrievalServiceImpl - 正在调用远程SOAP接口获取员工数据...
2024-01-01 10:05:05.456 [http-nio-8080-exec-2] INFO  DataRetrievalServiceImpl - 远程SOAP接口调用成功，响应数据长度: 256789 字符
2024-01-01 10:05:05.457 [http-nio-8080-exec-2] INFO  DataRetrievalServiceImpl - 正在解析SOAP响应，提取XML数据...
2024-01-01 10:05:05.458 [http-nio-8080-exec-2] INFO  DataRetrievalServiceImpl - XML数据提取成功，数据长度: 234567 字符
2024-01-01 10:05:05.459 [http-nio-8080-exec-2] INFO  DataRetrievalServiceImpl - 正在解析XML数据为员工DTO对象...
2024-01-01 10:05:07.567 [http-nio-8080-exec-2] INFO  DataRetrievalServiceImpl - ✅ 员工数据解析完成:
2024-01-01 10:05:07.568 [http-nio-8080-exec-2] INFO  DataRetrievalServiceImpl -   - 员工主记录: 150 条 (对应 employee 表)
2024-01-01 10:05:07.569 [http-nio-8080-exec-2] INFO  DataRetrievalServiceImpl -   - 员工岗位记录: 180 条 (对应 employee_position 表)
2024-01-01 10:05:07.570 [http-nio-8080-exec-2] INFO  DataRetrievalServiceImpl -   - 员工职称记录: 95 条 (对应 employee_title 表)
2024-01-01 10:05:07.571 [http-nio-8080-exec-2] INFO  DataRetrievalServiceImpl -   - 员工系统记录: 220 条 (对应 employee_system 表)
2024-01-01 10:05:07.572 [http-nio-8080-exec-2] INFO  DataRetrievalServiceImpl -   - 总计: 645 条记录
2024-01-01 10:05:07.573 [http-nio-8080-exec-2] INFO  DataRetrievalServiceImpl - 员工数据示例:
2024-01-01 10:05:07.574 [http-nio-8080-exec-2] INFO  DataRetrievalServiceImpl -   - 员工: 张三 | 工号: 001001 | MDM ID: 81ee8ed6-edab-4a1d-832b-812dcaf55aee | 状态: A
2024-01-01 10:05:07.575 [http-nio-8080-exec-2] INFO  DataRetrievalServiceImpl -     岗位: 2 个
2024-01-01 10:05:07.576 [http-nio-8080-exec-2] INFO  DataRetrievalServiceImpl -     职称: 1 个
2024-01-01 10:05:07.577 [http-nio-8080-exec-2] INFO  DataRetrievalServiceImpl -     系统: 3 个
2024-01-01 10:05:07.578 [http-nio-8080-exec-2] INFO  DataRetrievalServiceImpl -   - 员工: 李四 | 工号: 001002 | MDM ID: 92ee8ed6-edab-4a1d-832b-812dcaf55aef | 状态: A
2024-01-01 10:05:07.579 [http-nio-8080-exec-2] INFO  DataRetrievalServiceImpl -     岗位: 1 个
2024-01-01 10:05:07.580 [http-nio-8080-exec-2] INFO  DataRetrievalServiceImpl -     职称: 1 个
2024-01-01 10:05:07.581 [http-nio-8080-exec-2] INFO  DataRetrievalServiceImpl -     系统: 2 个
2024-01-01 10:05:07.582 [http-nio-8080-exec-2] INFO  DataRetrievalServiceImpl -   - ... 还有 148 个员工
2024-01-01 10:05:07.583 [http-nio-8080-exec-2] INFO  DataRetrievalServiceImpl - === 员工数据获取完成，总计: 150条记录 ===
2024-01-01 10:05:07.584 [http-nio-8080-exec-2] INFO  DataRetrievalController - ✅ [API响应] 员工数据获取成功:
2024-01-01 10:05:07.585 [http-nio-8080-exec-2] INFO  DataRetrievalController -    - 员工主记录: 150 条
2024-01-01 10:05:07.586 [http-nio-8080-exec-2] INFO  DataRetrievalController -    - 岗位记录: 180 条
2024-01-01 10:05:07.587 [http-nio-8080-exec-2] INFO  DataRetrievalController -    - 职称记录: 95 条
2024-01-01 10:05:07.588 [http-nio-8080-exec-2] INFO  DataRetrievalController -    - 系统记录: 220 条
2024-01-01 10:05:07.589 [http-nio-8080-exec-2] INFO  DataRetrievalController -    - 响应总记录数: 150 条
```

## 日志级别说明

- **INFO**: 主要业务流程和统计信息
- **DEBUG**: 详细的解析过程和字段映射
- **TRACE**: 最详细的调试信息
- **WARN**: 警告信息，如数据格式异常但不影响处理
- **ERROR**: 错误信息，处理失败的情况

## 日志图标说明

- 🔍 **[API请求]**: API接口请求
- 📅 **[日期解析]**: 日期参数解析
- 🚀 **[服务调用]**: 服务方法调用
- ✅ **[成功]**: 操作成功完成
- ❌ **[错误]**: 操作失败
- ⚠️ **[警告]**: 警告信息

## 如何查看日志

### 1. 实时查看日志
```bash
# 查看数据获取API日志
tail -f logs/data-retrieval-api.log

# 查看错误日志
tail -f logs/error.log

# 查看所有日志
tail -f logs/data-synchronization.log
```

### 2. 搜索特定内容
```bash
# 搜索部门数据相关日志
grep "部门数据" logs/data-retrieval-api.log

# 搜索员工数据相关日志
grep "员工数据" logs/data-retrieval-api.log

# 搜索错误信息
grep "ERROR" logs/data-synchronization.log
```

### 3. 按时间查看日志
```bash
# 查看今天的日志
grep "2024-01-01" logs/data-retrieval-api.log

# 查看特定时间段的日志
grep "2024-01-01 10:" logs/data-retrieval-api.log
```

## 日志配置

可以通过修改 `src/main/resources/logback-spring.xml` 来调整日志配置：

- 调整日志级别
- 修改日志文件路径
- 设置日志文件大小和保留天数
- 添加新的日志分类

## 故障排查

通过日志可以快速定位问题：

1. **API调用失败**: 查看 `data-retrieval-api.log` 中的错误信息
2. **日期解析失败**: 查看日期解析相关的日志
3. **远程服务调用失败**: 查看SOAP接口调用的日志
4. **数据解析失败**: 查看XML解析相关的日志

现在您可以通过详细的日志清楚地了解每次数据获取操作的完整过程和结果！
