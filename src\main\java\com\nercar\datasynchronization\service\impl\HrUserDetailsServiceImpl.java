package com.nercar.datasynchronization.service.impl;

import com.nercar.datasynchronization.entity.HrUserDetails;
import com.nercar.datasynchronization.mapper.CommonMapper;
import com.nercar.datasynchronization.service.HrUserDetailsService;
import com.nercar.datasynchronization.utils.HttpUtil;
import com.nercar.datasynchronization.utils.IDUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.Year;
import java.util.*;

/**
 * 员工岗位详情服务实现类
 * 基于RelationExtractServiceImpl的实现方式，去掉关系处理，只保留hr_user_details存储
 * 增强了所有字段的空值预防处理
 */
@Slf4j
@Service
public class HrUserDetailsServiceImpl implements HrUserDetailsService {

    @Autowired
    private CommonMapper commonMapper;

    @Value("${sync.http.position-url}")
    private String positionServiceUrl;

    @Value("${sync.http.timeout:30000}")
    private int timeout;

    /**
     * 同步所有员工岗位详情数据
     */
    @Override
    @Transactional
    public void updateAllUserDetailFromErp() {
        log.info("开始同步员工岗位详情数据，URL: {}", positionServiceUrl);

        try {
            // 清空现有数据
            int deletedCount = commonMapper.deleteHrDetails();
            log.info("清空现有数据，删除 {} 条记录", deletedCount);

            // 记录处理数据的计数器
            int totalCount = 0;
            int savedCount = 0;

            // 参数设置
            Map<String, String> paramMap = new HashMap<>(2);
            Long limit = 1000L;
            Long offset = 0L;

            // 循环获取数据
            while (true) {
                paramMap.put("limit", String.valueOf(limit));
                paramMap.put("offset", String.valueOf(offset));

                log.info("请求员工岗位详情数据: URL={}, 参数: offset={}, limit={}",
                        positionServiceUrl, offset, limit);

                String jsonString;
                try {
                    jsonString = HttpUtil.get(positionServiceUrl, paramMap, timeout);
                } catch (Exception e) {
                    log.error("发送HTTP GET请求异常: {}", e.getMessage(), e);
                    if (totalCount > 0) {
                        log.warn("已同步部分数据，跳过剩余同步");
                        break;
                    } else {
                        throw new RuntimeException("获取员工岗位详情数据失败，未能同步任何数据", e);
                    }
                }

                if (jsonString == null || jsonString.trim().isEmpty()) {
                    log.error("响应数据为空");
                    break;
                }

                // 解析JSON数据
                JSONObject jsonObject;
                try {
                    jsonObject = JSONUtil.parseObj(jsonString);
                } catch (Exception e) {
                    log.error("解析JSON数据异常: {}", e.getMessage(), e);
                    log.error("原始数据: {}", jsonString);
                    break;
                }

                JSONArray jsonArray = jsonObject.getJSONArray("data");
                if (jsonArray == null || jsonArray.size() == 0) {
                    log.info("没有更多数据，结束同步");
                    break;
                }

                totalCount += jsonArray.size();

                // 处理当前批次数据
                List<HrUserDetails> hrUserDetailsList = new ArrayList<>();

                for (int i = 0; i < jsonArray.size(); i++) {
                    try {
                        JSONObject item = jsonArray.getJSONObject(i);

                        // 为所有字段添加完善的空值预防处理
                        HrUserDetails hud = new HrUserDetails();
                        hud.setId(IDUtil.generateId());
                        
                        // 基础字段 - 都添加默认值预防空值
                        hud.setUserNo(item.getStr("userNo", ""));
                        hud.setName(item.getStr("name", ""));
                        hud.setPost(item.getStr("post", ""));
                        hud.setBirthday(item.getStr("birthday", ""));
                        hud.setEducation(item.getStr("education", ""));
                        hud.setSex(item.getStr("sex", ""));
                        hud.setSkillLevel(item.getStr("skillLevel", ""));
                        hud.setTeamsGroups(item.getStr("teamsGroups", ""));

                        // 工作经验字段 - 特殊处理Integer转String，默认为0年
                        Integer workExpInt = item.getInt("workExp");
                        if (workExpInt != null) {
                            hud.setWorkExp(String.valueOf(workExpInt));
                        } else {
                            hud.setWorkExp("0"); // 默认为0年工作经验
                        }

                        // 计算年龄 - 增强空值处理和异常处理
                        String birthday = hud.getBirthday();
                        if (birthday != null && !birthday.trim().isEmpty()) {
                            try {
                                String[] dateParts = birthday.split("-");
                                if (dateParts.length >= 1 && !dateParts[0].trim().isEmpty()) {
                                    String birthYear = dateParts[0].trim();
                                    Year currentYear = Year.now();
                                    int age = currentYear.getValue() - Integer.parseInt(birthYear);
                                    hud.setAge(String.valueOf(Math.max(0, age))); // 确保年龄不为负数
                                } else {
                                    hud.setAge("0"); // 生日格式不正确时默认年龄
                                }
                            } catch (Exception e) {
                                log.warn("计算用户 [{}] 年龄失败，使用默认值: {}", hud.getUserNo(), e.getMessage());
                                hud.setAge("0"); // 计算失败时默认年龄
                            }
                        } else {
                            hud.setAge("0"); // 生日为空时默认年龄
                        }

                        // 检查用户工号是否为空（这是必须字段）
                        if (hud.getUserNo() == null || hud.getUserNo().trim().isEmpty()) {
                            log.warn("用户工号为空，跳过处理");
                            continue;
                        }

                        hrUserDetailsList.add(hud);

                    } catch (Exception e) {
                        log.error("处理员工岗位详情数据异常: {}", e.getMessage(), e);
                        log.error("异常数据: {}", jsonArray.get(i).toString());
                        // 继续处理下一条记录，不中断整个同步过程
                    }
                }

                // 批量保存数据
                if (!hrUserDetailsList.isEmpty()) {
                    try {
                        int insertedCount = commonMapper.insertBatchHrUserDetails(hrUserDetailsList);
                        savedCount += insertedCount;
                        log.info("批量插入 {} 条记录，累计保存 {} 条，当前第一个用户工号: {}",
                                insertedCount, savedCount, hrUserDetailsList.get(0).getUserNo());
                    } catch (Exception e) {
                        log.error("批量插入失败: {}", e.getMessage(), e);
                        throw e;
                    }
                }

                // 更新偏移量
                offset += limit;

                // 如果返回的数据少于limit，说明已经是最后一页
                if (jsonArray.size() < limit) {
                    log.info("已获取所有数据，结束同步");
                    break;
                }
            }

            log.info("员工岗位详情数据同步完成，共处理 {} 条记录，成功保存 {} 条", totalCount, savedCount);

        } catch (Exception e) {
            log.error("同步员工岗位详情数据异常", e);
            throw e;
        }
    }

    /**
     * 按时间范围同步（实际上不支持时间参数，调用全量同步）
     */
    @Override
    @Transactional
    public void updateUserDetailFromErp(Date startDate, Date endDate) {
        log.warn("第三个接口不支持时间参数，将执行全量同步");
        updateAllUserDetailFromErp();
    }

    /**
     * 从本地文件同步员工岗位详情数据
     * 用于在无内网环境下，读取之前保存的JSON数据进行同步测试
     *
     * @param filePath 包含JSON数据的txt文件路径
     */
    @Override
    @Transactional
    public void updateUserDetailFromFile(String filePath) {
        log.info("开始从文件同步员工岗位详情数据，文件路径: {}", filePath);

        try {
            // 读取文件内容
            String fileContent;
            try {
                fileContent = new String(Files.readAllBytes(Paths.get(filePath)), "UTF-8");
                log.info("成功读取文件，文件大小: {} 字符", fileContent.length());
            } catch (IOException e) {
                log.error("读取文件失败: {}", e.getMessage(), e);
                throw new RuntimeException("读取文件失败: " + filePath, e);
            }

            if (fileContent == null || fileContent.trim().isEmpty()) {
                log.error("文件内容为空");
                throw new RuntimeException("文件内容为空: " + filePath);
            }

            // 预处理文件内容，跳过URL行，找到JSON开始位置
            String jsonContent = preprocessFileContent(fileContent);
            if (jsonContent == null || jsonContent.trim().isEmpty()) {
                log.error("文件中没有找到有效的JSON内容");
                throw new RuntimeException("文件中没有找到有效的JSON内容: " + filePath);
            }

            // 清空现有数据
            int deletedCount = commonMapper.deleteHrDetails();
            log.info("清空现有数据，删除 {} 条记录", deletedCount);

            // 记录处理数据的计数器
            int totalCount = 0;
            int savedCount = 0;

            // 解析JSON数据
            JSONObject jsonObject;
            try {
                jsonObject = JSONUtil.parseObj(jsonContent);
                log.info("成功解析JSON数据");
            } catch (Exception e) {
                log.error("解析JSON数据异常: {}", e.getMessage(), e);
                log.error("JSON内容前500字符: {}", jsonContent.substring(0, Math.min(500, jsonContent.length())));
                throw new RuntimeException("解析JSON数据失败", e);
            }

            JSONArray jsonArray = jsonObject.getJSONArray("data");
            if (jsonArray == null || jsonArray.size() == 0) {
                log.warn("JSON数据中没有找到data数组或数组为空");
                return;
            }

            totalCount = jsonArray.size();
            log.info("找到 {} 条员工岗位详情数据", totalCount);

            // 处理数据
            List<HrUserDetails> hrUserDetailsList = new ArrayList<>();

            for (int i = 0; i < jsonArray.size(); i++) {
                try {
                    JSONObject item = jsonArray.getJSONObject(i);

                    // 为所有字段添加完善的空值预防处理
                    HrUserDetails hud = new HrUserDetails();
                    hud.setId(IDUtil.generateId());
                    
                    // 基础字段 - 都添加默认值预防空值
                    hud.setUserNo(item.getStr("userNo", ""));
                    hud.setName(item.getStr("name", ""));
                    hud.setPost(item.getStr("post", ""));
                    hud.setBirthday(item.getStr("birthday", ""));
                    hud.setEducation(item.getStr("education", ""));
                    hud.setSex(item.getStr("sex", ""));
                    hud.setSkillLevel(item.getStr("skillLevel", ""));
                    hud.setTeamsGroups(item.getStr("teamsGroups", ""));

                    // 工作经验字段 - 特殊处理Integer转String，默认为0年
                    Integer workExpInt = item.getInt("workExp");
                    if (workExpInt != null) {
                        hud.setWorkExp(String.valueOf(workExpInt));
                    } else {
                        hud.setWorkExp("0"); // 默认为0年工作经验
                    }

                    // 计算年龄 - 增强空值处理和异常处理
                    String birthday = hud.getBirthday();
                    if (birthday != null && !birthday.trim().isEmpty()) {
                        try {
                            String[] dateParts = birthday.split("-");
                            if (dateParts.length >= 1 && !dateParts[0].trim().isEmpty()) {
                                String birthYear = dateParts[0].trim();
                                Year currentYear = Year.now();
                                int age = currentYear.getValue() - Integer.parseInt(birthYear);
                                hud.setAge(String.valueOf(Math.max(0, age))); // 确保年龄不为负数
                            } else {
                                hud.setAge("0"); // 生日格式不正确时默认年龄
                            }
                        } catch (Exception e) {
                            log.warn("计算用户 [{}] 年龄失败，使用默认值: {}", hud.getUserNo(), e.getMessage());
                            hud.setAge("0"); // 计算失败时默认年龄
                        }
                    } else {
                        hud.setAge("0"); // 生日为空时默认年龄
                    }

                    // 检查用户工号是否为空（这是必须字段）
                    if (hud.getUserNo() == null || hud.getUserNo().trim().isEmpty()) {
                        log.warn("第 {} 条记录用户工号为空，跳过处理", i + 1);
                        continue;
                    }

                    hrUserDetailsList.add(hud);

                    // 每1000条批量插入一次
                    if (hrUserDetailsList.size() >= 1000) {
                        try {
                            int insertedCount = commonMapper.insertBatchHrUserDetails(hrUserDetailsList);
                            savedCount += insertedCount;
                            log.info("批量插入 {} 条记录，累计保存 {} 条", insertedCount, savedCount);
                            hrUserDetailsList.clear();
                        } catch (Exception e) {
                            log.error("批量插入失败: {}", e.getMessage(), e);
                            throw e;
                        }
                    }

                } catch (Exception e) {
                    log.error("处理第 {} 条员工岗位详情数据异常: {}", i + 1, e.getMessage(), e);
                    log.error("异常数据: {}", jsonArray.get(i).toString());
                    // 继续处理下一条记录，不中断整个同步过程
                }
            }

            // 处理剩余数据
            if (!hrUserDetailsList.isEmpty()) {
                try {
                    int insertedCount = commonMapper.insertBatchHrUserDetails(hrUserDetailsList);
                    savedCount += insertedCount;
                    log.info("最后批量插入 {} 条记录，累计保存 {} 条", insertedCount, savedCount);
                } catch (Exception e) {
                    log.error("最后批量插入失败: {}", e.getMessage(), e);
                    throw e;
                }
            }

            log.info("从文件同步员工岗位详情数据完成，共处理 {} 条记录，成功保存 {} 条", totalCount, savedCount);

        } catch (Exception e) {
            log.error("从文件同步员工岗位详情数据异常", e);
            throw e;
        }
    }

    /**
     * 预处理文件内容，跳过URL行，提取JSON部分
     * 
     * @param fileContent 原始文件内容
     * @return 处理后的JSON字符串
     */
    private String preprocessFileContent(String fileContent) {
        if (fileContent == null || fileContent.trim().isEmpty()) {
            return null;
        }

        // 按行分割文件内容
        String[] lines = fileContent.split("\\r?\\n");
        StringBuilder jsonBuilder = new StringBuilder();
        boolean foundJsonStart = false;

        for (String line : lines) {
            String trimmedLine = line.trim();
            
            // 跳过空行
            if (trimmedLine.isEmpty()) {
                continue;
            }
            
            // 跳过URL行（以http开头的行）
            if (trimmedLine.startsWith("http://") || trimmedLine.startsWith("https://")) {
                log.info("跳过URL行: {}", trimmedLine);
                continue;
            }
            
            // 找到JSON开始标记
            if (trimmedLine.startsWith("{")) {
                foundJsonStart = true;
            }
            
            // 如果已经找到JSON开始，就添加到结果中
            if (foundJsonStart) {
                jsonBuilder.append(line).append("\n");
            }
        }

        String result = jsonBuilder.toString().trim();
        log.info("预处理完成，提取的JSON长度: {} 字符", result.length());
        
        return result;
    }
}