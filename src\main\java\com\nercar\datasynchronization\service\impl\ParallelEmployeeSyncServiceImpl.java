//package com.nercar.datasynchronization.service.impl;
//
//import com.nercar.datasynchronization.entity.Employee;
//import com.nercar.datasynchronization.entity.EmployeePosition;
//import com.nercar.datasynchronization.entity.EmployeeSystem;
//import com.nercar.datasynchronization.entity.EmployeeTitle;
//import com.nercar.datasynchronization.repository.EmployeePositionRepository;
//import com.nercar.datasynchronization.repository.EmployeeRepository;
//import com.nercar.datasynchronization.repository.EmployeeSystemRepository;
//import com.nercar.datasynchronization.repository.EmployeeTitleRepository;
//import com.nercar.datasynchronization.service.EmployeeSyncService;
//import com.nercar.datasynchronization.utils.XmlUtils;
//import lombok.extern.slf4j.Slf4j;
//import org.dom4j.Document;
//import org.dom4j.DocumentHelper;
//import org.dom4j.Element;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.context.annotation.Primary;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Propagation;
//import org.springframework.transaction.annotation.Transactional;
//import org.springframework.util.StringUtils;
//import org.xml.sax.InputSource;
//
//import javax.xml.parsers.SAXParser;
//import javax.xml.parsers.SAXParserFactory;
//import java.io.StringReader;
//import java.text.ParseException;
//import java.text.SimpleDateFormat;
//import java.util.ArrayList;
//import java.util.Date;
//import java.util.List;
//import java.util.concurrent.*;
//import java.util.concurrent.atomic.AtomicInteger;
//
//@Slf4j
//@Service
//@Primary  // 使用这个实现作为主要实现
//public class ParallelEmployeeSyncServiceImpl implements EmployeeSyncService {
//
//
//    @Autowired
//    private EmployeeRepository employeeRepository;
//
//    @Autowired
//    private EmployeePositionRepository positionRepository;
//
//    @Autowired
//    private EmployeeTitleRepository titleRepository;
//
//    @Autowired
//    private EmployeeSystemRepository systemRepository;
//
//    @Autowired
//    @Qualifier("processingExecutor")
//    private Executor processingExecutor;
//
//    private static final ThreadLocal<SimpleDateFormat> DATE_FORMAT =
//            ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy/MM/dd HH:mm:ss"));
//
//    // 减小批处理大小，避免过大事务
//    private static final int BATCH_SIZE = 500;
//
//    // 并行处理的段数，调小并行段数
//    private static final int PARALLEL_SEGMENTS = 4;
//
//    // 添加缓存减少重复查询
//    private final ConcurrentMap<String, Long> employeeIdCache = new ConcurrentHashMap<>();
//    private final ConcurrentMap<String, Long> positionIdCache = new ConcurrentHashMap<>();
//    private final ConcurrentMap<String, Long> titleIdCache = new ConcurrentHashMap<>();
//    private final ConcurrentMap<String, Long> systemIdCache = new ConcurrentHashMap<>();
//
//    @Override
//    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
//    public void syncEmployees(String xmlData) {
//        try {
//            log.info("开始并行同步员工数据，数据长度: {}", xmlData.length());
//            long startTime = System.currentTimeMillis();
//
//            // 使用DOM4J解析XML以分段处理
//            Document document = DocumentHelper.parseText(xmlData);
//            Element rootElement = document.getRootElement();
//            List<Element> allDataElements = rootElement.elements("O_DATA");
//
//            int totalElements = allDataElements.size();
//            log.info("员工数据总条数: {}", totalElements);
//
//            if (totalElements == 0) {
//                log.warn("未找到任何员工数据元素");
//                return;
//            }
//
//            // 减少并行度，每个段处理较少的数据
//            int actualParallelSegments = Math.min(PARALLEL_SEGMENTS,
//                    Math.max(1, totalElements / 100));
//
//            // 分段并行处理
//            List<CompletableFuture<Void>> futures = new ArrayList<>();
//            int segmentSize = Math.max(1, totalElements / actualParallelSegments);
//
//            for (int i = 0; i < actualParallelSegments; i++) {
//                final int startIndex = i * segmentSize;
//                final int endIndex = (i == actualParallelSegments - 1)
//                        ? totalElements
//                        : Math.min(totalElements, (i + 1) * segmentSize);
//
//                if (startIndex >= totalElements) {
//                    break;
//                }
//
//                List<Element> segmentElements = allDataElements.subList(startIndex, endIndex);
//
//                // 异步处理每个段，但添加间隔启动
//                final int segmentNumber = i;
//                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
//                    try {
//                        // 添加启动间隔，错开数据库访问时间
//                        if (segmentNumber > 0) {
//                            Thread.sleep(segmentNumber * 250); // 每段错开250毫秒启动
//                        }
//
//                        log.info("开始处理员工数据段 [{}-{}]，共{}条",
//                                startIndex, endIndex - 1, segmentElements.size());
//                        processEmployeeSegment(segmentElements);
//                        log.info("员工数据段 [{}-{}] 处理完成", startIndex, endIndex - 1);
//                    } catch (InterruptedException e) {
//                        Thread.currentThread().interrupt();
//                    }
//                }, processingExecutor);
//
//                futures.add(future);
//            }
//
//            // 等待所有段处理完成
//            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
//
//            long endTime = System.currentTimeMillis();
//            log.info("员工数据并行同步完成，耗时: {}ms，处理{}条记录", (endTime - startTime), totalElements);
//        } catch (Exception e) {
//            log.error("并行同步员工数据失败", e);
//            throw new RuntimeException("并行同步员工数据失败", e);
//        } finally {
//            // 清理ThreadLocal资源
//            DATE_FORMAT.remove();
//        }
//    }
//
//    // 为每个批处理添加独立事务
//    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
//    public void processBatch(List<Employee> employees, List<EmployeePosition> positions,
//                             List<EmployeeTitle> titles, List<EmployeeSystem> systems) {
//        if (!employees.isEmpty()) {
//            employeeRepository.saveAll(employees);
//        }
//
//        if (!positions.isEmpty()) {
//            positionRepository.saveAll(positions);
//        }
//
//        if (!titles.isEmpty()) {
//            titleRepository.saveAll(titles);
//        }
//
//        if (!systems.isEmpty()) {
//            systemRepository.saveAll(systems);
//        }
//    }
//
//    private void processEmployeeSegment(List<Element> dataElements) {
//        List<Employee> employeeBatch = new ArrayList<>(BATCH_SIZE);
//        List<EmployeePosition> positionBatch = new ArrayList<>(BATCH_SIZE);
//        List<EmployeeTitle> titleBatch = new ArrayList<>(BATCH_SIZE);
//        List<EmployeeSystem> systemBatch = new ArrayList<>(BATCH_SIZE);
//
//        try {
//            for (Element dataElement : dataElements) {
//                processEmployee(dataElement, employeeBatch, positionBatch, titleBatch, systemBatch);
//
//                // 当任何一个批次达到批处理大小时就进行保存
//                if (employeeBatch.size() >= BATCH_SIZE ||
//                        positionBatch.size() >= BATCH_SIZE ||
//                        titleBatch.size() >= BATCH_SIZE ||
//                        systemBatch.size() >= BATCH_SIZE) {
//
//                    processBatch(new ArrayList<>(employeeBatch),
//                            new ArrayList<>(positionBatch),
//                            new ArrayList<>(titleBatch),
//                            new ArrayList<>(systemBatch));
//
//                    employeeBatch.clear();
//                    positionBatch.clear();
//                    titleBatch.clear();
//                    systemBatch.clear();
//                }
//            }
//
//            // 保存剩余数据
//            if (!employeeBatch.isEmpty() || !positionBatch.isEmpty() ||
//                    !titleBatch.isEmpty() || !systemBatch.isEmpty()) {
//
//                processBatch(employeeBatch, positionBatch, titleBatch, systemBatch);
//            }
//        } catch (Exception e) {
//            log.error("处理员工数据段失败", e);
//            throw e;
//        }
//    }
//
//    private void processEmployee(Element dataElement,
//                                 List<Employee> employeeBatch,
//                                 List<EmployeePosition> positionBatch,
//                                 List<EmployeeTitle> titleBatch,
//                                 List<EmployeeSystem> systemBatch) {
//        try {
//            // 解析员工基本信息
//            Employee employee = new Employee();
//            employee.setMdmId(XmlUtils.getElementText(dataElement, "MDMZGZD_NM"));
//
//            // 检查是否已存在
//            Employee existingEmployee = employeeRepository.findByMdmId(employee.getMdmId());
//            if (existingEmployee != null) {
//                employee.setId(existingEmployee.getId());
//            }
//
//            employee.setMdmHrdwnm(XmlUtils.getElementText(dataElement, "MDMZGZD_HRDWNM"));
//            employee.setEmployeeCode(XmlUtils.getElementText(dataElement, "MDMZGZD_ZGBH"));
//            employee.setEmployeeName(XmlUtils.getElementText(dataElement, "MDMZGZD_ZGXM"));
//            employee.setGender(XmlUtils.getElementText(dataElement, "MDMZGZD_ZGXB"));
//            employee.setMobile(XmlUtils.getElementText(dataElement, "MDMZGZD_MOBILE"));
//            employee.setStatus(XmlUtils.getElementText(dataElement, "USERPREDEF_16"));
//            employee.setIdCard(XmlUtils.getElementText(dataElement, "USERPREDEF_21"));
//            employee.setAccount(XmlUtils.getElementText(dataElement, "MDMZGZD_ZH"));
//
//            String birthDateStr = XmlUtils.getElementText(dataElement, "MDMZGZD_CSRQ");
//            if (birthDateStr != null && !birthDateStr.isEmpty()) {
//                employee.setBirthDate(DATE_FORMAT.get().parse(birthDateStr));
//            }
//
//            employee.setEmail(XmlUtils.getElementText(dataElement, "MDMZGZD_EMAIL"));
//            employee.setOrgType(XmlUtils.getElementText(dataElement, "USERPREDEF_17"));
//            employee.setOrgLevel1(XmlUtils.getElementText(dataElement, "USERPREDEF_18"));
//            employee.setOrgLevel2(XmlUtils.getElementText(dataElement, "USERPREDEF_19"));
//            employee.setOrgLevel3(XmlUtils.getElementText(dataElement, "USERPREDEF_20"));
//            employee.setWechat(XmlUtils.getElementText(dataElement, "MDMZGZD_WECHAT"));
//            employee.setTel(XmlUtils.getElementText(dataElement, "MDMZGZD_TEL"));
//            employee.setNote(XmlUtils.getElementText(dataElement, "MDMZGZD_NOTE"));
//            employee.setIsDisabled(XmlUtils.getElementText(dataElement, "MDMZGZD_TYBZ"));
//            employee.setUserType(XmlUtils.getElementText(dataElement, "USERPREDEF_3"));
//            employee.setOrgCode(XmlUtils.getElementText(dataElement, "USERPREDEF_4"));
//            employee.setIdName(XmlUtils.getElementText(dataElement, "USERPREDEF_5"));
//            employee.setCreatedTime(new Date());
//            employee.setUpdatedTime(new Date());
//
//            // 添加到批处理列表
//            synchronized (employeeBatch) {
//                employeeBatch.add(employee);
//            }
//
//            // 处理岗位信息
//            Element positions = dataElement.element("O_CHILDS1");
//            if (positions != null) {
//                List<Element> positionElements = positions.elements("O_CHILD");
//                for (Element posElement : positionElements) {
//                    processPosition(posElement, employee.getMdmId(), positionBatch);
//                }
//            }
//
//            // 处理职称信息
//            Element titles = dataElement.element("O_CHILDS2");
//            if (titles != null) {
//                List<Element> titleElements = titles.elements("O_CHILD");
//                for (Element titleElement : titleElements) {
//                    processTitle(titleElement, employee.getMdmId(), titleBatch);
//                }
//            }
//
//            // 处理系统标识
//            Element systems = dataElement.element("O_CHILDS3");
//            if (systems != null) {
//                List<Element> systemElements = systems.elements("O_CHILD");
//                for (Element sysElement : systemElements) {
//                    processSystem(sysElement, employee.getMdmId(), systemBatch);
//                }
//            }
//        } catch (ParseException e) {
//            log.error("解析员工数据失败", e);
//        }
//    }
//
//    private void processPosition(Element element, String employeeMdmId, List<EmployeePosition> positionBatch) {
//        String guid = XmlUtils.getElementText(element, "MDMRYZZGW_V4_GUID");
//        if (guid == null || guid.isEmpty()) {
//            return;
//        }
//
//        EmployeePosition position = new EmployeePosition();
//        position.setGuid(guid);
//
//        // 检查是否已存在
//        EmployeePosition existingPosition = positionRepository.findByGuid(position.getGuid());
//        if (existingPosition != null) {
//            position.setId(existingPosition.getId());
//        }
//
//        position.setEmployeeMdmId(employeeMdmId);
//        position.setPositionCode(XmlUtils.getElementText(element, "MDMRYZZGW_V4_PCXMZY"));
//        position.setOrgCode(XmlUtils.getElementText(element, "MDMRYZZGW_V4_UDEF1"));
//        position.setDepartmentCode(XmlUtils.getElementText(element, "MDMRYZZGW_V4_UDEF3"));
//        position.setIsPrimary(XmlUtils.getElementText(element, "MDMRYZZGW_V4_UDEF7"));
//        position.setStatus(XmlUtils.getElementText(element, "MDMRYZZGW_V4_UDEF8"));
//        position.setCreatedTime(new Date());
//        position.setUpdatedTime(new Date());
//
//        // 添加到批处理列表
//        synchronized (positionBatch) {
//            positionBatch.add(position);
//        }
//    }
//
//    private void processTitle(Element element, String employeeMdmId, List<EmployeeTitle> titleBatch) {
//        String guid = XmlUtils.getElementText(element, "MDMRYGJ_V4_GUID");
//        if (guid == null || guid.isEmpty()) {
//            return;
//        }
//
//        EmployeeTitle title = new EmployeeTitle();
//        title.setGuid(guid);
//
//        // 检查是否已存在
//        EmployeeTitle existingTitle = titleRepository.findByGuid(title.getGuid());
//        if (existingTitle != null) {
//            title.setId(existingTitle.getId());
//        }
//
//        title.setEmployeeMdmId(employeeMdmId);
//        title.setTitleCode(XmlUtils.getElementText(element, "MDMRYGJ_V4_PCXMZY"));
//        title.setTitleType(XmlUtils.getElementText(element, "MDMRYGJ_V4_UDEF1"));
//        title.setTitleLevel(XmlUtils.getElementText(element, "MDMRYGJ_V4_UDEF3"));
//        title.setTitleName(XmlUtils.getElementText(element, "MDMRYGJ_V4_UDEF8"));
//        title.setStatus(XmlUtils.getElementText(element, "MDMRYGJ_V4_UDEF5"));
//        title.setCreatedTime(new Date());
//        title.setUpdatedTime(new Date());
//
//        // 添加到批处理列表
//        synchronized (titleBatch) {
//            titleBatch.add(title);
//        }
//    }
//
//    private void processSystem(Element element, String employeeMdmId, List<EmployeeSystem> systemBatch) {
//        String guid = XmlUtils.getElementText(element, "NGTYYSB_GUID");
//        if (guid == null || guid.isEmpty()) {
//            return;
//        }
//
//        EmployeeSystem system = new EmployeeSystem();
//        system.setGuid(guid);
//
//        // 检查是否已存在
//        EmployeeSystem existingSystem = systemRepository.findByGuid(system.getGuid());
//        if (existingSystem != null) {
//            system.setId(existingSystem.getId());
//        }
//
//        system.setEmployeeMdmId(employeeMdmId);
//        system.setSystemCode(XmlUtils.getElementText(element, "NGTYYSB_LYXTBH"));
//        system.setSystemDataId(XmlUtils.getElementText(element, "NGTYYSB_LYXTDATANM"));
//        system.setEmployeeCode(XmlUtils.getElementText(element, "NGTYYSB_UDEF1"));
//        system.setOrgCode(XmlUtils.getElementText(element, "NGTYYSB_UDEF3"));
//        system.setDepartmentCode(XmlUtils.getElementText(element, "NGTYYSB_UDEF4"));
//        system.setCreatedTime(new Date());
//        system.setUpdatedTime(new Date());
//
//        // 添加到批处理列表
//        synchronized (systemBatch) {
//            systemBatch.add(system);
//        }
//    }
//}