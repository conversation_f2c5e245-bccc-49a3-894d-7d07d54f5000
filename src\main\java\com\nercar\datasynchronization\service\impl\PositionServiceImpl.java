//package com.nercar.datasynchronization.service.impl;
//
//import com.nercar.datasynchronization.entity.DepartmentPosition;
//import com.nercar.datasynchronization.entity.EmployeePositionRelation;
//import com.nercar.datasynchronization.entity.PositionInfo;
//import com.nercar.datasynchronization.repository.DepartmentPositionRepository;
//import com.nercar.datasynchronization.repository.EmployeePositionRelationRepository;
//import com.nercar.datasynchronization.repository.PositionInfoRepository;
//import com.nercar.datasynchronization.service.PositionService;
//import com.nercar.datasynchronization.utils.IDUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Optional;
//import java.util.stream.Collectors;
//
///**
// * 岗位服务实现
// */
//@Slf4j
//@Service
//public class PositionServiceImpl implements PositionService {
//
//    @Autowired
//    private PositionInfoRepository positionInfoRepository;
//
//    @Autowired
//    private DepartmentPositionRepository departmentPositionRepository;
//
//    @Autowired
//    private EmployeePositionRelationRepository employeePositionRelationRepository;
//
//    @Override
//    public List<PositionInfo> getAllPositions() {
//        return positionInfoRepository.findAll();
//    }
//
//    @Override
//    public PositionInfo getPositionById(String id) {
//        Optional<PositionInfo> optionalPosition = positionInfoRepository.findById(id);
//        return optionalPosition.orElse(null);
//    }
//
//    @Override
//    @Transactional
//    public PositionInfo createPosition(PositionInfo position) {
//        // 检查岗位编码是否已存在
//        if (position.getPositionCode() != null &&
//            positionInfoRepository.existsByPositionCode(position.getPositionCode())) {
//            log.warn("岗位编码[{}]已存在", position.getPositionCode());
//            throw new IllegalArgumentException("岗位编码已存在");
//        }
//
//        // 如果没有指定ID，生成一个新ID
//        if (position.getId() == null || position.getId().isEmpty()) {
//            position.setId(IDUtil.generateId());
//        }
//
//        // 如果没有指定岗位编码，生成一个基于名称的编码
//        if (position.getPositionCode() == null || position.getPositionCode().isEmpty()) {
//            String code = "POS_" + Math.abs(position.getPositionName().hashCode());
//            position.setPositionCode(code);
//        }
//
//        // 如果没有指定状态，设为活动状态
//        if (position.getStatus() == null || position.getStatus().isEmpty()) {
//            position.setStatus("ACTIVE");
//        }
//
//        return positionInfoRepository.save(position);
//    }
//
//    @Override
//    @Transactional
//    public PositionInfo updatePosition(PositionInfo position) {
//        // 检查岗位是否存在
//        if (!positionInfoRepository.existsById(position.getId())) {
//            log.warn("岗位[{}]不存在", position.getId());
//            return null;
//        }
//
//        // 如果要更改岗位编码，检查新编码是否与其他岗位冲突
//        Optional<PositionInfo> existingPosition = positionInfoRepository.findById(position.getId());
//        if (existingPosition.isPresent() && position.getPositionCode() != null &&
//            !position.getPositionCode().equals(existingPosition.get().getPositionCode()) &&
//            positionInfoRepository.existsByPositionCode(position.getPositionCode())) {
//            log.warn("岗位编码[{}]已被其他岗位使用", position.getPositionCode());
//            throw new IllegalArgumentException("岗位编码已被其他岗位使用");
//        }
//
//        return positionInfoRepository.save(position);
//    }
//
//    @Override
//    @Transactional
//    public boolean deletePosition(String id) {
//        // 检查岗位是否存在
//        if (!positionInfoRepository.existsById(id)) {
//            log.warn("岗位[{}]不存在", id);
//            return false;
//        }
//
//        // 检查是否有员工关联到此岗位
//        List<EmployeePositionRelation> employeePositions = employeePositionRelationRepository.findByPositionId(id);
//        if (!employeePositions.isEmpty()) {
//            log.warn("岗位[{}]有{}名员工关联，无法删除", id, employeePositions.size());
//            throw new IllegalStateException("岗位有员工关联，无法删除");
//        }
//
//        // 检查是否有部门关联到此岗位
//        List<DepartmentPosition> departmentPositions = departmentPositionRepository.findByPositionId(id);
//        if (!departmentPositions.isEmpty()) {
//            log.warn("岗位[{}]有{}个部门关联，无法删除", id, departmentPositions.size());
//            throw new IllegalStateException("岗位有部门关联，无法删除");
//        }
//
//        // 执行删除
//        positionInfoRepository.deleteById(id);
//        log.info("岗位[{}]删除成功", id);
//        return true;
//    }
//
//    @Override
//    public List<PositionInfo> getPositionsByDepartmentId(Integer departmentId) {
//        // 查找部门关联的所有岗位ID
//        List<DepartmentPosition> departmentPositions =
//            departmentPositionRepository.findByDepartmentId(departmentId);
//
//        if (departmentPositions.isEmpty()) {
//            return new ArrayList<>();
//        }
//
//        // 提取岗位ID列表
//        List<String> positionIds = departmentPositions.stream()
//            .map(DepartmentPosition::getPositionId)
//            .collect(Collectors.toList());
//
//        // 批量查询岗位信息
//        return positionInfoRepository.findAllById(positionIds);
//    }
//
//    @Override
//    public List<PositionInfo> getPositionsByEmployeeId(Long employeeId) {
//        // 查找员工关联的所有岗位ID
//        List<EmployeePositionRelation> employeePositions =
//            employeePositionRelationRepository.findByEmployeeId(employeeId);
//
//        if (employeePositions.isEmpty()) {
//            return new ArrayList<>();
//        }
//
//        // 提取岗位ID列表
//        List<String> positionIds = employeePositions.stream()
//            .map(EmployeePositionRelation::getPositionId)
//            .collect(Collectors.toList());
//
//        // 批量查询岗位信息
//        return positionInfoRepository.findAllById(positionIds);
//    }
//}