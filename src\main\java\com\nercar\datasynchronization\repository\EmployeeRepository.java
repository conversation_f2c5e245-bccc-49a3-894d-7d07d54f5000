package com.nercar.datasynchronization.repository;

import com.nercar.datasynchronization.entity.Employee;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface EmployeeRepository extends JpaRepository<Employee, Long> {
    
    /**
     * 根据员工编号查询员工
     */
    Employee findByEmployeeCode(String employeeCode);
    
    /**
     * 根据MDM ID查询员工
     */
    Employee findByMdmId(String mdmId);
    
    /**
     * 查询指定创建时间范围内的员工
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 时间范围内创建的员工列表
     */
    List<Employee> findByCreatedTimeGreaterThanEqualAndCreatedTimeLessThanEqual(
            Date startTime, Date endTime);
    
    /**
     * 查询指定更新时间范围内的员工
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 时间范围内更新的员工列表
     */
    List<Employee> findByUpdatedTimeGreaterThanEqualAndUpdatedTimeLessThanEqual(
            Date startTime, Date endTime);
}