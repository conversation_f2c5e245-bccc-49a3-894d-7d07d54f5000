//package com.nercar.datasynchronization.service;
//
//import com.nercar.datasynchronization.entity.HrUserDetails;
//import com.nercar.datasynchronization.entity.PositionInfo;
//
//import java.util.Date;
//import java.util.List;
//import java.util.Map;
//
///**
// * 数据迁移服务接口 - 定义数据迁移和关联关系创建的方法
// */
//public interface DataMigrationService {
//
//    /**
//     * 创建员工与部门的关联关系
//     */
//    void createEmployeeDepartmentRelations();
//
//    /**
//     * 创建员工与部门的关联关系，只处理指定时间范围内的员工
//     * @param startTime 开始时间
//     * @param endTime 结束时间
//     */
//    void createEmployeeDepartmentRelations(Date startTime, Date endTime);
//
//    /**
//     * 从HR用户详情中提取岗位信息并创建关联关系
//     */
//    void createPositionRelations();
//
//    /**
//     * 从HR用户详情中提取岗位信息并创建关联关系，处理指定时间范围内的数据
//     * @param startTime 开始时间
//     * @param endTime 结束时间
//     */
//    void createPositionRelations(Date startTime, Date endTime);
//
//    /**
//     * 初始化新表结构数据
//     */
//    void initializeNewTables();
//
//    /**
//     * 从hr_user_details表中提取岗位信息，并保存到position_info表
//     */
//    void migratePositionInfo();
//
//    /**
//     * 创建部门-岗位关联数据
//     */
//    void createDepartmentPositionRelations();
//
//    /**
//     * 创建员工-岗位关联数据
//     */
//    void createEmployeePositionRelations();
//
//    /**
//     * 从HR用户详情中提取唯一岗位信息
//     */
//    Map<String, PositionInfo> extractPositionInfo(List<HrUserDetails> details);
//
//
//    /**
//     * 并行创建岗位关联关系 - 处理所有数据
//     */
//    void createPositionRelationsParallel();
//
//
//}