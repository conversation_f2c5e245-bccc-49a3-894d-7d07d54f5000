package com.nercar.datasynchronization.controller;

import com.nercar.datasynchronization.service.DepartmentToOrgStructureMigrationService;
import com.nercar.datasynchronization.service.OfflineMigrationService;
import com.nercar.datasynchronization.service.DepartmentHierarchyReconstructionService;
import com.nercar.datasynchronization.service.DepartmentFullNameAnalysisService;
import com.nercar.datasynchronization.service.DepartmentHierarchyFixService;
import com.nercar.datasynchronization.service.DepartmentToOrgStructureConverterService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 数据迁移控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/migration")
@RequiredArgsConstructor
public class MigrationController {

    private final DepartmentToOrgStructureMigrationService migrationService;
    private final OfflineMigrationService offlineMigrationService;
    private final DepartmentHierarchyReconstructionService hierarchyReconstructionService;
    private final DepartmentFullNameAnalysisService fullNameAnalysisService;
    private final DepartmentHierarchyFixService hierarchyFixService;
    private final DepartmentToOrgStructureConverterService converterService;

    /**
     * 执行部门数据迁移
     */
    @PostMapping("/department-to-org-structure")
    public ResponseEntity<Map<String, Object>> migrateDepartmentToOrgStructure(
            @RequestParam(defaultValue = "department_to_org_structure_migration.sql") String fileName) {
        try {
            log.info("开始执行部门数据迁移，目标文件: {}", fileName);
            
            String result = migrationService.migrateToOrgStructure(fileName);
            
            return ResponseEntity.ok(Map.of(
                    "success", true,
                    "message", result,
                    "fileName", fileName
            ));
        } catch (Exception e) {
            log.error("数据迁移失败", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of(
                            "success", false,
                            "error", "迁移失败: " + e.getMessage()
                    ));
        }
    }

    /**
     * 离线数据迁移（从SQL文件）
     */
    @PostMapping("/offline-migration")
    public ResponseEntity<Map<String, Object>> offlineMigration(
            @RequestParam(defaultValue = "department_sync_test.sql") String inputFile,
            @RequestParam(defaultValue = "department_to_org_structure_offline.sql") String outputFile) {
        try {
            log.info("开始离线数据迁移，输入文件: {}, 输出文件: {}", inputFile, outputFile);

            String result = offlineMigrationService.migrateFromSqlFile(inputFile, outputFile);

            return ResponseEntity.ok(Map.of(
                    "success", true,
                    "message", result,
                    "inputFile", inputFile,
                    "outputFile", outputFile
            ));
        } catch (Exception e) {
            log.error("离线数据迁移失败", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of(
                            "success", false,
                            "error", "离线迁移失败: " + e.getMessage()
                    ));
        }
    }

    /**
     * 专门处理department_sync_test2.sql文件的迁移
     */
    @PostMapping("/migrate-test2")
    public ResponseEntity<Map<String, Object>> migrateTest2ToOrgStructure(
            @RequestParam(defaultValue = "department_sync_test2_to_org_structure_complete.sql") String outputFile) {
        try {
            log.info("开始迁移department_sync_test2.sql到t_org_structure，输出文件: {}", outputFile);

            String result = offlineMigrationService.migrateFromTest2SqlFile(outputFile);

            return ResponseEntity.ok(Map.of(
                    "success", true,
                    "message", result,
                    "inputFile", "department_sync_test2.sql",
                    "outputFile", outputFile,
                    "description", "已矫正层级结构的完整1399条记录迁移"
            ));
        } catch (Exception e) {
            log.error("department_sync_test2.sql迁移失败", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of(
                            "success", false,
                            "error", "迁移失败: " + e.getMessage()
                    ));
        }
    }

    /**
     * 获取迁移统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getMigrationStatistics() {
        try {
            String statistics = migrationService.getMigrationStatistics();
            return ResponseEntity.ok(Map.of(
                    "success", true,
                    "statistics", statistics
            ));
        } catch (Exception e) {
            log.error("获取统计信息失败", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of(
                            "success", false,
                            "error", "获取统计信息失败: " + e.getMessage()
                    ));
        }
    }

    /**
     * 基于full_name重构部门层级关系
     */
    @PostMapping("/reconstruct-hierarchy")
    public ResponseEntity<Map<String, Object>> reconstructDepartmentHierarchy(
            @RequestParam(defaultValue = "department_sync_test.sql") String inputFileName,
            @RequestParam(defaultValue = "department_hierarchy_reconstructed.sql") String outputFileName) {
        try {
            log.info("开始基于full_name重构部门层级，输入文件: {}, 输出文件: {}", inputFileName, outputFileName);

            String inputFilePath = "src/main/resources/sql/" + inputFileName;
            String outputFilePath = "src/main/resources/sql/" + outputFileName;

            String result = hierarchyReconstructionService.reconstructHierarchyFromFullName(inputFilePath, outputFilePath);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "部门层级重构成功");
            response.put("result", result);
            response.put("inputFile", inputFilePath);
            response.put("outputFile", outputFilePath);
            response.put("timestamp", new Date());

            log.info("部门层级重构完成: {}", result);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("部门层级重构失败", e);

            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "部门层级重构失败: " + e.getMessage());
            errorResponse.put("error", e.getClass().getSimpleName());
            errorResponse.put("timestamp", new Date());

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 基于full_name智能分析并生成t_org_structure结构
     */
    @PostMapping("/analyze-fullname-to-org-structure")
    public ResponseEntity<Map<String, Object>> analyzeFullNameToOrgStructure(
            @RequestParam(defaultValue = "department_sync_test2.sql") String inputFileName,
            @RequestParam(defaultValue = "t_org_structure_from_fullname.sql") String outputFileName) {
        try {
            log.info("开始基于full_name分析部门层级，输入文件: {}, 输出文件: {}", inputFileName, outputFileName);

            String inputFilePath = "src/main/resources/sql/" + inputFileName;
            String outputFilePath = "src/main/resources/sql/" + outputFileName;

            String result = fullNameAnalysisService.analyzeAndGenerateOrgStructure(inputFilePath, outputFilePath);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "基于full_name的部门层级分析成功");
            response.put("result", result);
            response.put("inputFile", inputFilePath);
            response.put("outputFile", outputFilePath);
            response.put("timestamp", new Date());

            log.info("部门层级分析完成: {}", result);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("部门层级分析失败", e);

            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "部门层级分析失败: " + e.getMessage());
            errorResponse.put("error", e.getClass().getSimpleName());
            errorResponse.put("timestamp", new Date());

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 执行部门层级关系修正
     */
    @PostMapping("/fix-hierarchy")
    public ResponseEntity<Map<String, Object>> fixDepartmentHierarchy() {
        try {
            log.info("开始执行部门层级关系修正");

            DepartmentHierarchyFixService.FixReport report = hierarchyFixService.executeHierarchyFix();

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "部门层级关系修正完成");
            response.put("report", Map.of(
                "totalDepartments", report.totalDepartments,
                "fixedDepartments", report.fixedDepartments,
                "createdDepartments", report.createdDepartments,
                "skippedDepartments", report.skippedDepartments,
                "errorDepartments", report.errorDepartments,
                "maxHierarchyDepth", report.maxHierarchyDepth,
                "orphanNodesCount", report.orphanNodes.size(),
                "orphanNodes", report.orphanNodes
            ));
            response.put("timestamp", new Date());

            log.info("部门层级关系修正完成: {}", report);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("部门层级关系修正失败", e);

            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "部门层级关系修正失败: " + e.getMessage());
            errorResponse.put("error", e.getClass().getSimpleName());
            errorResponse.put("timestamp", new Date());

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 将 department_sync_test 数据转换为 t_org_structure 格式
     */
    @PostMapping("/convert-to-org-structure")
    public ResponseEntity<Map<String, Object>> convertToOrgStructure(
            @RequestParam(defaultValue = "t_org_structure_converted.sql") String outputFileName) {
        try {
            log.info("开始转换部门数据为 t_org_structure 格式，输出文件: {}", outputFileName);

            String outputFilePath = "src/main/resources/sql/" + outputFileName;
            String result = converterService.convertToOrgStructureSQL(outputFilePath);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "部门数据转换完成");
            response.put("result", result);
            response.put("outputFile", outputFilePath);
            response.put("timestamp", new Date());

            log.info("部门数据转换完成: {}", result);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("部门数据转换失败", e);

            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "部门数据转换失败: " + e.getMessage());
            errorResponse.put("error", e.getClass().getSimpleName());
            errorResponse.put("timestamp", new Date());

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
}
