package com.nercar.datasynchronization.controller;

import com.nercar.datasynchronization.service.OrgStructureMigrationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 组织架构迁移控制器
 * 提供组织架构数据迁移的HTTP API接口
 */
@Slf4j
@RestController
@RequestMapping("/api/migration/org-structure")
@Tag(name = "组织架构迁移", description = "组织架构数据迁移相关接口")
public class OrgStructureMigrationController {

    @Autowired
    private OrgStructureMigrationService migrationService;

    /**
     * 执行组织架构数据迁移
     */
    @PostMapping("/migrate")
    @Operation(summary = "执行组织架构数据迁移", description = "将MySQL中的department表数据迁移到PostgreSQL中的t_org_structure表")
    public ResponseEntity<Map<String, Object>> migrateOrgStructure(
            @Parameter(description = "是否清理现有同步数据")
            @RequestParam(defaultValue = "false") boolean clearExisting) {
        
        log.info("接收到组织架构数据迁移请求，clearExisting: {}", clearExisting);
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            migrationService.migrateToOrgStructure(clearExisting);
            
            response.put("success", true);
            response.put("message", "组织架构数据迁移成功");
            response.put("statistics", migrationService.getMigrationStatistics());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("组织架构数据迁移失败", e);
            
            response.put("success", false);
            response.put("message", "组织架构数据迁移失败: " + e.getMessage());
            
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 清理组织架构同步数据
     */
    @DeleteMapping("/clear")
    @Operation(summary = "清理组织架构同步数据", description = "删除data_source=2的所有记录，保留手工录入的数据")
    public ResponseEntity<Map<String, Object>> clearOrgStructureData() {
        
        log.info("接收到清理组织架构同步数据请求");
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            migrationService.clearOrgStructureData();
            
            response.put("success", true);
            response.put("message", "组织架构同步数据清理成功");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("清理组织架构同步数据失败", e);
            
            response.put("success", false);
            response.put("message", "清理组织架构同步数据失败: " + e.getMessage());
            
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 验证迁移结果
     */
    @GetMapping("/validate")
    @Operation(summary = "验证迁移结果", description = "检查数据完整性和层级关系正确性")
    public ResponseEntity<Map<String, Object>> validateMigrationResult() {
        
        log.info("接收到验证迁移结果请求");
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            int result = migrationService.validateMigrationResult();
            
            if (result > 0) {
                response.put("success", true);
                response.put("message", "迁移结果验证通过");
                response.put("migratedCount", result);
                response.put("statistics", migrationService.getMigrationStatistics());
            } else {
                response.put("success", false);
                response.put("message", "迁移结果验证失败，错误代码: " + result);
                response.put("errorCode", result);
            }
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("验证迁移结果失败", e);
            
            response.put("success", false);
            response.put("message", "验证迁移结果失败: " + e.getMessage());
            
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取迁移统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取迁移统计信息", description = "获取当前的迁移统计信息")
    public ResponseEntity<Map<String, Object>> getMigrationStatistics() {
        
        log.info("接收到获取迁移统计信息请求");
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            String statistics = migrationService.getMigrationStatistics();
            
            response.put("success", true);
            response.put("statistics", statistics);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取迁移统计信息失败", e);
            
            response.put("success", false);
            response.put("message", "获取迁移统计信息失败: " + e.getMessage());
            
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 检查PostgreSQL连接状态
     */
    @GetMapping("/check-connection")
    @Operation(summary = "检查PostgreSQL连接状态", description = "检查PostgreSQL数据库连接是否正常")
    public ResponseEntity<Map<String, Object>> checkPostgreSQLConnection() {
        
        log.info("接收到检查PostgreSQL连接状态请求");
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            boolean connected = migrationService.checkPostgreSQLConnection();
            
            response.put("success", true);
            response.put("connected", connected);
            response.put("message", connected ? "PostgreSQL连接正常" : "PostgreSQL连接异常");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("检查PostgreSQL连接状态失败", e);
            
            response.put("success", false);
            response.put("connected", false);
            response.put("message", "检查PostgreSQL连接状态失败: " + e.getMessage());
            
            return ResponseEntity.badRequest().body(response);
        }
    }
}
