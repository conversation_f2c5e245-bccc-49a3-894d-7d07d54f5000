package com.nercar.datasynchronization.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.util.Date;

@Data
@Entity
@Table(name = "employee_system")
@DynamicInsert
@DynamicUpdate
public class Employee_system {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "employee_mdm_id", length = 50)
    private String employee_mdm_id;

    @Column(name = "ngtyysb_udef9", length = 255)
    private String ngtyysb_udef9;

    @Column(name = "ngtyysb_lyxtdatanm", length = 255)
    private String ngtyysb_lyxtdatanm;

    @Column(name = "ngtyysb_udef6", length = 255)
    private String ngtyysb_udef6;

    @Column(name = "ngtyysb_guid", length = 255)
    private String ngtyysb_guid;

    @Column(name = "ngtyysb_udef5", length = 255)
    private String ngtyysb_udef5;

    @Column(name = "ngtyysb_udef8", length = 255)
    private String ngtyysb_udef8;

    @Column(name = "ngtyysb_udef7", length = 255)
    private String ngtyysb_udef7;

    @Column(name = "ngtyysb_lyxtbh", length = 255)
    private String ngtyysb_lyxtbh;

    @Column(name = "ngtyysb_udef13", length = 255)
    private String ngtyysb_udef13;

    @Column(name = "ngtyysb_udef12", length = 255)
    private String ngtyysb_udef12;

    @Column(name = "ngtyysb_udef11", length = 255)
    private String ngtyysb_udef11;

    @Column(name = "ngtyysb_udef10", length = 255)
    private String ngtyysb_udef10;

    @Column(name = "ngtyysb_udef2", length = 255)
    private String ngtyysb_udef2;

    @Column(name = "ngtyysb_udef1", length = 255)
    private String ngtyysb_udef1;

    @Column(name = "ngtyysb_udef4", length = 255)
    private String ngtyysb_udef4;

    @Column(name = "ngtyysb_udef3", length = 255)
    private String ngtyysb_udef3;

    @Column(name = "ngtyysb_udef15", length = 255)
    private String ngtyysb_udef15;

    @Column(name = "ngtyysb_zbnm", length = 255)
    private String ngtyysb_zbnm;

    @Column(name = "ngtyysb_udef14", length = 255)
    private String ngtyysb_udef14;

    @Column(name = "created_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdTime;

    @Column(name = "updated_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedTime;
}
