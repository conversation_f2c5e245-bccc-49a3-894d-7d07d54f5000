package com.nercar.datasynchronization.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.*;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 部门层级重构服务
 * 基于full_name字段智能分析和重建正确的部门层级关系
 */
@Slf4j
@Service
public class DepartmentHierarchyReconstructionService {

    /**
     * 基于full_name重构department_sync_test.sql的层级关系
     */
    public String reconstructHierarchyFromFullName(String inputFilePath, String outputFilePath) {
        try {
            log.info("开始基于full_name重构部门层级，输入文件: {}, 输出文件: {}", inputFilePath, outputFilePath);
            
            // 1. 解析原始SQL文件
            List<DepartmentRecord> departments = parseSqlFile(inputFilePath);
            log.info("解析到 {} 条部门记录", departments.size());
            
            // 2. 基于full_name分析层级关系
            Map<String, String> hierarchyMapping = analyzeHierarchyFromFullName(departments);
            log.info("分析出 {} 个层级关系映射", hierarchyMapping.size());
            
            // 3. 重建部门层级结构
            List<DepartmentRecord> reconstructedDepartments = reconstructDepartmentHierarchy(departments, hierarchyMapping);
            log.info("重构完成，共 {} 条记录", reconstructedDepartments.size());
            
            // 4. 生成PostgreSQL迁移文件
            generatePostgreSQLMigration(reconstructedDepartments, outputFilePath);
            
            log.info("部门层级重构完成，生成文件: {}", outputFilePath);
            return "重构成功，共处理 " + reconstructedDepartments.size() + " 条记录，修正了层级关系";
            
        } catch (Exception e) {
            log.error("部门层级重构失败", e);
            throw new RuntimeException("部门层级重构失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解析SQL文件，提取部门数据
     */
    private List<DepartmentRecord> parseSqlFile(String filePath) throws IOException {
        List<DepartmentRecord> departments = new ArrayList<>();
        
        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            String line;
            
            // 正则表达式匹配INSERT语句
            Pattern insertPattern = Pattern.compile(
                "INSERT INTO `department_sync_test` VALUES \\((\\d+),\\s*'([^']+)',\\s*'([^']*)',\\s*'([^']*)',\\s*'([^']*)',\\s*'([^']*)',\\s*(\\d+),\\s*'([^']*)',\\s*'[^']*',\\s*'[^']*',\\s*'[^']*'\\);"
            );
            
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                
                if (line.startsWith("INSERT INTO `department_sync_test`")) {
                    Matcher matcher = insertPattern.matcher(line);
                    if (matcher.find()) {
                        try {
                            DepartmentRecord dept = new DepartmentRecord();
                            dept.id = Long.parseLong(matcher.group(1));
                            dept.orgCode = matcher.group(2);
                            dept.orgName = matcher.group(3);
                            dept.parentCode = matcher.group(4);
                            dept.deptUuid = matcher.group(5);
                            dept.fullName = matcher.group(6);
                            dept.isHistory = Integer.parseInt(matcher.group(7));
                            dept.userPredef14 = matcher.group(8);
                            
                            // 只处理有效数据
                            if (dept.isHistory == 0 && !"D".equals(dept.userPredef14)) {
                                departments.add(dept);
                                log.debug("解析到有效部门: {} ({})", dept.orgName, dept.orgCode);
                            }
                        } catch (Exception e) {
                            log.warn("解析记录失败: {}", line, e);
                        }
                    }
                }
            }
        }
        
        return departments;
    }

    /**
     * 基于full_name分析层级关系
     */
    private Map<String, String> analyzeHierarchyFromFullName(List<DepartmentRecord> departments) {
        Map<String, String> hierarchyMapping = new HashMap<>();
        Map<String, String> deptNameToCode = new HashMap<>();
        
        // 建立部门名称到编码的映射
        for (DepartmentRecord dept : departments) {
            deptNameToCode.put(dept.orgName, dept.orgCode);
        }
        
        for (DepartmentRecord dept : departments) {
            String correctParentCode = findCorrectParentCode(dept, departments, deptNameToCode);
            if (correctParentCode != null && !correctParentCode.equals(dept.parentCode)) {
                hierarchyMapping.put(dept.orgCode, correctParentCode);
                log.debug("发现层级关系修正: {} -> {} (原: {})", 
                         dept.orgCode, correctParentCode, dept.parentCode);
            }
        }
        
        return hierarchyMapping;
    }

    /**
     * 基于full_name智能分析找到正确的父级部门编码
     */
    private String findCorrectParentCode(DepartmentRecord dept, List<DepartmentRecord> allDepartments, Map<String, String> deptNameToCode) {
        String fullName = dept.fullName;
        if (fullName == null || fullName.trim().isEmpty()) {
            return "1"; // 根节点
        }
        
        // 1. 分析一级部门（事业部、集团、公司等）
        if (isTopLevelDepartment(fullName)) {
            return "1"; // 根节点
        }
        
        // 2. 分析二级部门（厂、处等）
        String parentDeptName = extractParentDepartmentName(fullName);
        if (parentDeptName != null) {
            // 查找父级部门的编码
            for (DepartmentRecord parentDept : allDepartments) {
                if (parentDept.orgName.equals(parentDeptName) || 
                    parentDept.fullName.contains(parentDeptName)) {
                    return parentDept.orgCode;
                }
            }
        }
        
        // 3. 如果找不到明确的父级，尝试模糊匹配
        return findParentByFuzzyMatching(dept, allDepartments);
    }

    /**
     * 判断是否为一级部门
     */
    private boolean isTopLevelDepartment(String fullName) {
        // 基于实际的一级部门列表进行判断

        // 1. 明确的一级部门后缀
        if (fullName.matches(".*事业部$") ||
            fullName.matches(".*集团$") ||
            fullName.matches(".*有限公司$")) {
            return true;
        }

        // 2. 根据实际一级部门列表（基于用户提供的56个一级部门）
        Set<String> topLevelDepartments = Set.of(
            // 基础职能部门
            "公司办公室", "人力资源部", "企业文化部", "财务部", "党委办公室", "组织部",
            "党委工作部", "审计部", "集团领导", "风险合规部", "安全环保部", "市场部",
            "证券部", "科技质量部", "保卫部", "工会", "国资",

            // 战略运营相关
            "战略运营部", "集团战略发展部", "集团综合资产部", "集团财务审计部",
            "集团资产处置公司", "集团工会",

            // 生产运营中心
            "物流中心", "采购中心", "制造部",

            // 研究院
            "新材料研究院", "数字应用研究院", "蔚蓝高科技集团",

            // 项目指挥部
            "印尼焦化项目部", "印尼焦炭项目指挥部", "集团宿迁金鑫公司项目指挥部",
            "南京退休职工服务中心"
        );

        // 检查是否在一级部门列表中
        if (topLevelDepartments.contains(fullName)) {
            return true;
        }

        // 3. 没有前缀的部门（如：人力资源部、财务部、保卫部等）
        if (fullName.matches("^[^事业部集团有限公司厂车间科室班]*部$") &&
            !fullName.contains("事业部") &&
            !fullName.contains("集团") &&
            !fullName.contains("有限公司") &&
            !fullName.contains("厂") &&
            !fullName.contains("车间") &&
            !fullName.contains("科") &&
            !fullName.contains("室")) {
            return true;
        }

        // 4. 没有前缀的中心（如：物流中心、采购中心等）
        if (fullName.matches("^[^事业部集团有限公司厂车间科室班]*中心$") &&
            !fullName.contains("事业部") &&
            !fullName.contains("集团") &&
            !fullName.contains("有限公司") &&
            !fullName.contains("厂") &&
            !fullName.contains("车间")) {
            return true;
        }

        // 5. 没有前缀的厂（如：制氧厂、水厂、燃气厂等）
        if (fullName.matches("^[^事业部集团有限公司车间科室班]*厂$") &&
            !fullName.contains("事业部") &&
            !fullName.contains("集团") &&
            !fullName.contains("有限公司") &&
            !fullName.contains("车间")) {
            return true;
        }

        // 6. 特殊的一级部门（研究院等）
        if (fullName.contains("研究院") && !fullName.contains("事业部") && !fullName.contains("集团")) {
            return true;
        }

        return false;
    }

    /**
     * 从full_name中提取父级部门名称
     */
    private String extractParentDepartmentName(String fullName) {
        // 事业部下的厂/处
        if (fullName.contains("事业部") && (fullName.contains("厂") || fullName.contains("处"))) {
            if (fullName.contains("厂")) {
                String[] parts = fullName.split("厂");
                if (parts.length > 1) {
                    return parts[0].substring(parts[0].lastIndexOf("事业部") + 3) + "厂";
                }
            }
            if (fullName.contains("处")) {
                String[] parts = fullName.split("处");
                if (parts.length > 1) {
                    return parts[0].substring(parts[0].lastIndexOf("事业部") + 3) + "处";
                }
            }
        }
        
        // 厂下的车间/科/室
        if (fullName.contains("厂") && (fullName.contains("车间") || fullName.contains("科") || fullName.contains("室"))) {
            String[] parts = fullName.split("厂");
            if (parts.length > 1) {
                return parts[0] + "厂";
            }
        }
        
        // 车间下的班组
        if (fullName.contains("车间") && fullName.contains("班")) {
            String[] parts = fullName.split("班");
            if (parts.length > 0) {
                String beforeBan = parts[0];
                if (beforeBan.contains("车间")) {
                    return beforeBan.substring(0, beforeBan.lastIndexOf("车间") + 2);
                }
            }
        }
        
        return null;
    }

    /**
     * 通过模糊匹配查找父级部门
     */
    private String findParentByFuzzyMatching(DepartmentRecord dept, List<DepartmentRecord> allDepartments) {
        String fullName = dept.fullName;
        
        // 尝试找到最长的公共前缀作为父级
        String bestMatch = null;
        int maxMatchLength = 0;
        
        for (DepartmentRecord candidate : allDepartments) {
            if (candidate.orgCode.equals(dept.orgCode)) continue;
            
            if (fullName.startsWith(candidate.orgName) && candidate.orgName.length() > maxMatchLength) {
                bestMatch = candidate.orgCode;
                maxMatchLength = candidate.orgName.length();
            }
        }
        
        return bestMatch != null ? bestMatch : "1";
    }

    /**
     * 重建部门层级结构
     */
    private List<DepartmentRecord> reconstructDepartmentHierarchy(List<DepartmentRecord> departments, Map<String, String> hierarchyMapping) {
        List<DepartmentRecord> reconstructed = new ArrayList<>();
        
        for (DepartmentRecord dept : departments) {
            DepartmentRecord newDept = new DepartmentRecord();
            newDept.id = dept.id;
            newDept.orgCode = dept.orgCode;
            newDept.orgName = dept.orgName;
            newDept.deptUuid = dept.deptUuid;
            newDept.fullName = dept.fullName;
            newDept.isHistory = dept.isHistory;
            newDept.userPredef14 = dept.userPredef14;
            
            // 使用重构后的父级编码
            newDept.parentCode = hierarchyMapping.getOrDefault(dept.orgCode, dept.parentCode);
            
            reconstructed.add(newDept);
        }
        
        return reconstructed;
    }

    /**
     * 生成PostgreSQL格式的迁移SQL文件
     */
    private void generatePostgreSQLMigration(List<DepartmentRecord> departments, String outputFilePath) throws IOException {
        try (PrintWriter writer = new PrintWriter(new FileWriter(outputFilePath))) {
            writer.println("-- =====================================================");
            writer.println("-- 基于full_name重构的部门数据迁移SQL");
            writer.println("-- 生成时间: " + new Date());
            writer.println("-- 数据来源: department_sync_test.sql（已重构层级关系）");
            writer.println("-- 记录数量: " + departments.size());
            writer.println("-- =====================================================");
            writer.println();
            
            writer.println("-- 清理现有同步数据");
            writer.println("DELETE FROM t_org_structure WHERE data_source = 2;");
            writer.println();
            
            writer.println("-- 插入重构后的部门数据");
            writer.println("INSERT INTO t_org_structure (id, organ_name, pre_id, order_info, is_del, create_time, modify_time, data_source) VALUES");
            
            for (int i = 0; i < departments.size(); i++) {
                DepartmentRecord dept = departments.get(i);
                
                // 转换ID
                long id = convertOrgCodeToId(dept.orgCode);
                
                // 转换父级ID
                String preId = convertParentCodeToPreId(dept.parentCode);
                
                writer.printf("(%d, '%s', %s, %d, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 2)",
                    id,
                    dept.orgName.replace("'", "''"), // 转义单引号
                    preId,
                    i + 1 // 排序信息
                );
                
                if (i < departments.size() - 1) {
                    writer.println(",");
                } else {
                    writer.println(";");
                }
            }
            
            writer.println();
            writer.println("-- 验证迁移结果");
            writer.println("SELECT ");
            writer.println("    '=== 迁移统计结果 ===' as section,");
            writer.println("    data_source,");
            writer.println("    COUNT(*) as record_count,");
            writer.println("    COUNT(CASE WHEN pre_id IS NULL THEN 1 END) as root_count,");
            writer.println("    COUNT(CASE WHEN pre_id IS NOT NULL THEN 1 END) as child_count");
            writer.println("FROM t_org_structure ");
            writer.println("WHERE data_source = 2");
            writer.println("GROUP BY data_source;");
        }
    }

    /**
     * 转换组织编码为数字ID
     */
    private long convertOrgCodeToId(String orgCode) {
        if (orgCode.startsWith("X")) {
            String numericPart = orgCode.substring(1);
            try {
                return Long.parseLong(numericPart);
            } catch (NumberFormatException e) {
                // 处理特殊编码
                switch (orgCode) {
                    case "XA1000000": return 865634712L;
                    case "XB1000000": return 1875040113L;
                    case "XC5000000": return 1642783431L;
                    default: return orgCode.hashCode(); // 备用方案
                }
            }
        }
        return orgCode.hashCode();
    }

    /**
     * 转换父级编码为父级ID
     */
    private String convertParentCodeToPreId(String parentCode) {
        if (parentCode == null || parentCode.isEmpty() || "1".equals(parentCode)) {
            return "NULL";
        }
        
        long parentId = convertOrgCodeToId(parentCode);
        return String.valueOf(parentId);
    }

    /**
     * 部门记录数据类
     */
    private static class DepartmentRecord {
        Long id;
        String orgCode;
        String orgName;
        String parentCode;
        String deptUuid;
        String fullName;
        Integer isHistory;
        String userPredef14;
    }
}
