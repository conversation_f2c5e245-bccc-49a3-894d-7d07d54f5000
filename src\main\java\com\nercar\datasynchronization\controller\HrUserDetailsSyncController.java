package com.nercar.datasynchronization.controller;

import com.nercar.datasynchronization.service.HrUserDetailsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 员工岗位详情同步控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/hr-details")
@Tag(name = "员工岗位详情同步API", description = "提供从HR系统同步员工岗位详情数据功能")
public class HrUserDetailsSyncController {

    @Autowired
    private HrUserDetailsService hrUserDetailsService;

    @GetMapping("/sync")
    @Operation(summary = "同步所有员工岗位详情", description = "从HR系统同步全量员工岗位详情数据")
    public ResponseEntity<Map<String, Object>> syncAllHrUserDetails() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("开始同步所有员工岗位详情数据");
            hrUserDetailsService.updateAllUserDetailFromErp();
            
            result.put("success", true);
            result.put("message", "员工岗位详情数据同步成功");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("员工岗位详情数据同步失败", e);
            result.put("success", false);
            result.put("message", "员工岗位详情数据同步失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    @GetMapping("/sync/from-file")
    @Operation(summary = "从文件同步员工岗位详情", description = "从本地txt文件读取JSON数据并同步到数据库，用于内网环境测试")
    public ResponseEntity<Map<String, Object>> syncHrUserDetailsFromFile(
            @Parameter(description = "包含JSON数据的txt文件路径，例如: D:/data/hr_data.txt")
            @RequestParam String filePath) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("开始从文件同步员工岗位详情数据，文件路径: {}", filePath);
            hrUserDetailsService.updateUserDetailFromFile(filePath);
            
            result.put("success", true);
            result.put("message", "从文件同步员工岗位详情数据成功");
            result.put("filePath", filePath);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("从文件同步员工岗位详情数据失败", e);
            result.put("success", false);
            result.put("message", "从文件同步员工岗位详情数据失败: " + e.getMessage());
            result.put("filePath", filePath);
            return ResponseEntity.internalServerError().body(result);
        }
    }
    
//    @GetMapping("/sync/by-time")
//    @Operation(summary = "同步指定时间范围的员工岗位详情", description = "从HR系统同步指定时间范围内的员工岗位详情数据")
//    public ResponseEntity<Map<String, Object>> syncHrUserDetailsByTime(
//            @Parameter(description = "开始时间 (格式: yyyy-MM-dd HH:mm:ss)")
//            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startDate,
//            @Parameter(description = "结束时间 (格式: yyyy-MM-dd HH:mm:ss)")
//            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endDate) {
//
//        Map<String, Object> result = new HashMap<>();
//
//        try {
//            log.info("开始同步时间范围内的员工岗位详情数据: {} 至 {}", startDate, endDate);
//            hrUserDetailsService.updateUserDetailFromErp(startDate, endDate);
//
//            result.put("success", true);
//            result.put("message", "员工岗位详情数据同步成功");
//            return ResponseEntity.ok(result);
//        } catch (Exception e) {
//            log.error("员工岗位详情数据同步失败", e);
//            result.put("success", false);
//            result.put("message", "员工岗位详情数据同步失败: " + e.getMessage());
//            return ResponseEntity.internalServerError().body(result);
//        }
//    }
}