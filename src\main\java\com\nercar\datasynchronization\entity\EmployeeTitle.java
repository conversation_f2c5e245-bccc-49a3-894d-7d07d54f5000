package com.nercar.datasynchronization.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.util.Date;

/**
 * 员工职称实体 - 存储员工的职称信息
 * 对应文档1.5.3人员信息下发接口中的从表2（人员岗级关系）
 */
@Data
@Entity
@Table(name = "employee_title")
@DynamicInsert
@DynamicUpdate
public class EmployeeTitle {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "guid")
    private String guid;

    @Column(name = "employee_mdm_id")
    private String employeeMdmId;

    @Column(name = "title_code")
    private String titleCode;

    @Column(name = "title_type")
    private String titleType;

    @Column(name = "title_level")
    private String titleLevel;

    @Column(name = "status")
    private String status;

    @Column(name = "title_name")
    private String titleName;

    @Column(name = "title_category")
    private String titleCategory;

    @Column(name = "created_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdTime;

    @Column(name = "updated_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedTime;

    @Column(name = "default_org_code")
    private String defaultOrgCode;

    @Column(name = "is_disabled")
    private String isDisabled;
}