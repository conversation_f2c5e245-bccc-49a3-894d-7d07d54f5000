package com.nercar.datasynchronization.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.*;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 基于full_name智能分析部门层级关系服务
 * 直接从department_sync_test2.sql构建t_org_structure表结构
 */
@Slf4j
@Service
public class DepartmentFullNameAnalysisService {

    // 已知的56个一级部门（根据用户提供的完整列表）
    private static final Set<String> TOP_LEVEL_DEPARTMENTS = Set.of(
        // 第一张图
        "新材料研究院（合署）", "公司办公室", "人力资源部", "企业文化部", "财务部",
        "党委办公室", "组织部", "党委工作部", "审计部", "集团领导", "风险合规部",
        "安全环保部", "公司办公室（党风廉政办公室）", "集团战略发展部", "江苏金珂水务有限公司",
        "工会", "印尼焦化项目部", "特钢事业部", "数字应用研究院（人工智能研究院）",
        "南京三金房地产开发有限公司", "南京退休职工服务中心", "集团资产处置公司",

        // 第二张图
        "江苏金珂和电子商务有限公司2", "国资", "公司领导", "南京鑫智链科技信息有限公司2",
        "科技质量部", "数字应用研究院", "蔚蓝高科技集团", "战略运营部（产业发展研究院）",
        "物流中心", "能源动力事业部", "炼铁事业部", "保卫部", "新产业投资集团",
        "采购中心", "制造部", "板材事业部", "市场部", "江苏南钢市政环保设备股份有限公司",
        "印尼焦炭项目指挥部", "江苏南钢鑫洋供应链有限公司", "集团信达金融公司",
        "南京金智工程技术有限公司",

        // 第三张图
        "集团综合资产部", "证券部", "香港金腾公司", "南京钢铁集团国际经济贸易有限公司",
        "集团工会", "集团财务审计部", "集团宿迁金鑫公司项目指挥部", "江苏金恒信息科技股份有限公司"
    );

    /**
     * 从department_sync_test2.sql分析并生成t_org_structure结构的SQL
     */
    public String analyzeAndGenerateOrgStructure(String inputFilePath, String outputFilePath) {
        try {
            log.info("开始分析部门层级，输入文件: {}, 输出文件: {}", inputFilePath, outputFilePath);
            
            // 1. 解析原始SQL文件
            List<DepartmentRecord> departments = parseSqlFile(inputFilePath);
            log.info("解析到 {} 条有效部门记录", departments.size());
            
            // 2. 构建层级树结构
            DepartmentTree tree = buildDepartmentTree(departments);
            log.info("构建层级树完成，根节点数: {}", tree.getRootNodes().size());
            
            // 3. 生成t_org_structure格式的SQL
            generateOrgStructureSQL(tree, outputFilePath);
            
            log.info("部门层级分析完成，生成文件: {}", outputFilePath);
            return String.format("成功处理 %d 条记录，生成 %d 个层级节点", 
                               departments.size(), tree.getAllNodes().size());
            
        } catch (Exception e) {
            log.error("部门层级分析失败", e);
            throw new RuntimeException("部门层级分析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解析SQL文件，提取部门数据
     */
    private List<DepartmentRecord> parseSqlFile(String filePath) throws IOException {
        List<DepartmentRecord> departments = new ArrayList<>();
        
        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            String line;
            
            // 正则表达式匹配INSERT语句
            Pattern insertPattern = Pattern.compile(
                "INSERT INTO `department_sync_test` VALUES \\((\\d+),\\s*'([^']+)',\\s*'([^']*)',\\s*'([^']*)',\\s*'([^']*)',\\s*'([^']*)',\\s*(\\d+),\\s*'([^']*)',\\s*'[^']*',\\s*'[^']*',\\s*'[^']*'\\);"
            );

            while ((line = reader.readLine()) != null) {
                line = line.trim();

                if (line.startsWith("INSERT INTO `department_sync_test`")) {
                    Matcher matcher = insertPattern.matcher(line);
                    if (matcher.find()) {
                        try {
                            DepartmentRecord dept = new DepartmentRecord();
                            dept.id = Long.parseLong(matcher.group(1));
                            dept.orgCode = matcher.group(2);
                            dept.orgName = matcher.group(3);
                            dept.parentCode = matcher.group(4);
                            dept.deptUuid = matcher.group(5);
                            dept.fullName = matcher.group(6);
                            dept.isHistory = Integer.parseInt(matcher.group(7));
                            dept.userPredef14 = matcher.group(8);
                            
                            // 只处理有效数据
                            if (dept.isHistory == 0 && !"D".equals(dept.userPredef14)) {
                                departments.add(dept);
                                log.debug("解析到有效部门: {} -> {}", dept.orgName, dept.fullName);
                            }
                        } catch (Exception e) {
                            log.warn("解析记录失败: {}", line, e);
                        }
                    }
                }
            }
        }
        
        return departments;
    }

    /**
     * 构建部门层级树
     */
    private DepartmentTree buildDepartmentTree(List<DepartmentRecord> departments) {
        DepartmentTree tree = new DepartmentTree();
        Map<String, DepartmentNode> nodeMap = new HashMap<>();
        Map<String, DepartmentNode> topLevelNodeMap = new HashMap<>(); // 一级部门映射

        // 1. 首先创建所有节点
        for (DepartmentRecord dept : departments) {
            DepartmentNode node = new DepartmentNode();
            node.id = generateUniqueId(dept.orgCode);
            node.orgCode = dept.orgCode;
            node.orgName = dept.orgName;
            node.fullName = dept.fullName;
            node.level = calculateLevel(dept.fullName, dept.orgName);

            nodeMap.put(dept.orgCode, node);
            tree.addNode(node);
        }

        // 2. 识别一级部门（基于已知的56个一级部门列表）
        int topLevelCount = 0;
        for (DepartmentRecord dept : departments) {
            DepartmentNode node = nodeMap.get(dept.orgCode);

            // 判断是否为一级部门：在已知一级部门列表中
            if (isRealTopLevelDepartment(dept.fullName, dept.orgName)) {
                node.parentId = null;
                node.level = 1;
                tree.addRootNode(node);
                topLevelNodeMap.put(dept.orgName, node);
                topLevelCount++;
                log.info("识别为一级部门: {} (full_name: {})", node.orgName, dept.fullName);
            }
        }
        log.info("共识别出 {} 个一级部门", topLevelCount);

        // 3. 为非一级部门基于full_name构建完整层级关系
        for (DepartmentRecord dept : departments) {
            DepartmentNode node = nodeMap.get(dept.orgCode);

            // 跳过已经处理的一级部门
            if (node.level == 1) {
                continue;
            }

            // 简单直接的层级构建逻辑
            DepartmentNode parentNode = buildSimpleHierarchy(dept, topLevelNodeMap, nodeMap, tree);

            if (parentNode != null) {
                node.parentId = parentNode.id;
                node.level = parentNode.level + 1;
                parentNode.children.add(node);
                log.debug("建立层级关系: {} -> {} (level {})", parentNode.orgName, node.orgName, node.level);
            } else {
                log.error("完全无法确定父级: {} (full_name: {})", node.orgName, dept.fullName);
            }
        }

        return tree;
    }

    /**
     * 计算部门层级
     */
    private int calculateLevel(String fullName, String orgName) {
        // 如果是一级部门
        if (isTopLevelDepartment(fullName, orgName)) {
            return 1;
        }
        
        // 通过full_name分析层级深度
        if (fullName == null || fullName.trim().isEmpty()) {
            return 1;
        }
        
        // 简单的层级计算：通过常见分隔符计算
        String[] parts = fullName.split("[事业部|集团|有限公司|厂|车间|科|室|部|中心]");
        return Math.max(1, parts.length);
    }

    /**
     * 判断是否为真正的一级部门（简单粗暴的方法）
     */
    private boolean isRealTopLevelDepartment(String fullName, String orgName) {
        if (fullName == null || fullName.trim().isEmpty()) {
            return false;
        }

        // 简单粗暴的方法：计算层级标识符的数量
        String[] suffixes = {"厂", "车间", "科", "室", "部", "中心", "处", "班", "组", "站", "队", "所"};
        int count = 0;

        for (String suffix : suffixes) {
            int index = 0;
            while ((index = fullName.indexOf(suffix, index)) != -1) {
                count++;
                index += suffix.length();
            }
        }

        // 如果只有1个或0个层级标识符，认为是一级部门
        boolean isTopLevel = count <= 1;

        log.debug("检查是否为一级部门: {} (full_name: {}, 层级标识符数量: {}) -> {}",
                 orgName, fullName, count, isTopLevel);
        return isTopLevel;
    }

    /**
     * 简单直接的层级构建逻辑
     */
    private DepartmentNode buildSimpleHierarchy(DepartmentRecord dept, Map<String, DepartmentNode> topLevelNodeMap,
                                              Map<String, DepartmentNode> allNodeMap, DepartmentTree tree) {
        String fullName = dept.fullName;
        String orgName = dept.orgName;

        // 1. 拆解全名
        List<String> levels = parseFullNameToHierarchy(fullName, orgName);
        if (levels.size() <= 1) {
            return null; // 单级部门，不需要父级
        }

        // 2. 一层一层匹配和创建
        DepartmentNode currentParent = null;

        // 从第一级开始（除了最后一级，最后一级是当前部门）
        for (int i = 0; i < levels.size() - 1; i++) {
            String levelName = levels.get(i);

            if (i == 0) {
                // 第一级：在一级部门中查找
                currentParent = findMatchingTopLevelDepartment(levelName, topLevelNodeMap);
                if (currentParent == null) {
                    log.warn("找不到匹配的一级部门: {}", levelName);
                    return null;
                }
            } else {
                // 中间级：查找或创建
                DepartmentNode existingNode = findChildByName(currentParent, levelName, allNodeMap);
                if (existingNode != null) {
                    currentParent = existingNode;
                } else {
                    // 创建新的中间层级
                    DepartmentNode newNode = createVirtualDepartmentNode(levelName, currentParent);
                    allNodeMap.put(newNode.orgCode, newNode);
                    tree.addNode(newNode);
                    currentParent.children.add(newNode);
                    currentParent = newNode;
                    log.info("动态创建中间层级: {} -> {}", currentParent.orgName, levelName);
                }
            }
        }

        return currentParent;
    }

    /**
     * 查找匹配的一级部门
     */
    private DepartmentNode findMatchingTopLevelDepartment(String levelName, Map<String, DepartmentNode> topLevelNodeMap) {
        // 精确匹配
        if (topLevelNodeMap.containsKey(levelName)) {
            return topLevelNodeMap.get(levelName);
        }

        // 模糊匹配：基于业务规则
        for (Map.Entry<String, DepartmentNode> entry : topLevelNodeMap.entrySet()) {
            String topLevelName = entry.getKey();
            if (isBusinessMatch(levelName, topLevelName)) {
                return entry.getValue();
            }
        }

        return null;
    }

    /**
     * 业务规则匹配
     */
    private boolean isBusinessMatch(String levelName, String topLevelName) {
        // 炼铁相关
        if (levelName.contains("炼铁") && topLevelName.equals("炼铁事业部")) {
            return true;
        }
        // 板材相关
        if ((levelName.contains("板") || levelName.contains("卷")) && topLevelName.equals("板材事业部")) {
            return true;
        }
        // 特钢相关
        if (levelName.contains("特钢") && topLevelName.equals("特钢事业部")) {
            return true;
        }
        return false;
    }

    /**
     * 查找子部门
     */
    private DepartmentNode findChildByName(DepartmentNode parent, String childName, Map<String, DepartmentNode> allNodeMap) {
        for (DepartmentNode child : parent.children) {
            if (child.orgName.equals(childName)) {
                return child;
            }
        }
        return null;
    }

    /**
     * 基于full_name构建层级关系（保留原方法）
     */
    private DepartmentNode buildHierarchyFromFullName(DepartmentRecord dept, Map<String, DepartmentNode> topLevelNodeMap,
                                                    Map<String, DepartmentNode> allNodeMap, DepartmentTree tree) {
        String fullName = dept.fullName;
        String orgName = dept.orgName;

        if (fullName == null || fullName.trim().isEmpty()) {
            return null;
        }

        // 分析full_name，提取层级路径
        // 例如：第一炼铁厂综合科驾驶班 -> [第一炼铁厂, 综合科, 驾驶班]
        List<String> hierarchyLevels = parseFullNameToHierarchy(fullName, orgName);

        if (hierarchyLevels.isEmpty()) {
            return null;
        }

        // 找到对应的一级部门
        DepartmentNode currentParent = findTopLevelParentByHierarchy(hierarchyLevels, topLevelNodeMap);
        if (currentParent == null) {
            return null;
        }

        // 从一级部门开始，逐级构建或查找中间层级
        for (int i = 1; i < hierarchyLevels.size() - 1; i++) { // 跳过最后一级（当前部门）
            String levelName = hierarchyLevels.get(i);

            // 查找是否已存在该层级部门
            DepartmentNode existingNode = findExistingNodeByName(levelName, allNodeMap);

            if (existingNode != null && existingNode.parentId != null && existingNode.parentId.equals(currentParent.id)) {
                currentParent = existingNode;
                log.debug("找到已存在的中间层级: {}", levelName);
            } else {
                // 动态创建缺失的中间层级部门
                DepartmentNode newNode = createVirtualDepartmentNode(levelName, currentParent);
                allNodeMap.put(newNode.orgCode, newNode);
                tree.addNode(newNode);
                currentParent.children.add(newNode);
                currentParent = newNode;
                log.info("动态创建中间层级部门: {} -> {}", currentParent.orgName, levelName);
            }
        }

        return currentParent;
    }

    /**
     * 解析full_name为层级列表（改进版）
     */
    private List<String> parseFullNameToHierarchy(String fullName, String orgName) {
        List<String> levels = new ArrayList<>();

        // 特殊情况处理
        if (fullName == null || fullName.trim().isEmpty()) {
            return levels;
        }

        // 先处理特殊的复杂情况
        if (fullName.contains("江苏金珂水务有限公司")) {
            return parseCompanyHierarchy(fullName, "江苏金珂水务有限公司");
        }
        if (fullName.contains("江苏南钢鑫洋供应链有限公司")) {
            return parseCompanyHierarchy(fullName, "江苏南钢鑫洋供应链有限公司");
        }
        if (fullName.contains("蔚蓝高科技集团")) {
            return parseCompanyHierarchy(fullName, "蔚蓝高科技集团");
        }
        if (fullName.contains("新产业投资集团")) {
            return parseCompanyHierarchy(fullName, "新产业投资集团");
        }

        // 处理括号情况
        if (fullName.contains("（") && fullName.contains("）")) {
            return parseBracketHierarchy(fullName, orgName);
        }

        // 标准的层级分割
        return parseStandardHierarchy(fullName, orgName);
    }

    /**
     * 处理公司层级
     */
    private List<String> parseCompanyHierarchy(String fullName, String companyName) {
        List<String> levels = new ArrayList<>();

        if (fullName.startsWith(companyName)) {
            levels.add(companyName);
            String remaining = fullName.substring(companyName.length());

            // 继续分割剩余部分
            List<String> remainingLevels = parseStandardHierarchy(remaining, "");
            levels.addAll(remainingLevels);
        } else {
            // 如果不是以公司名开头，使用标准分割
            return parseStandardHierarchy(fullName, "");
        }

        return levels;
    }

    /**
     * 处理括号层级
     */
    private List<String> parseBracketHierarchy(String fullName, String orgName) {
        List<String> levels = new ArrayList<>();

        // 简单处理：将括号内容与前面的内容合并为一个层级
        String[] parts = fullName.split("(?<=）)");

        for (String part : parts) {
            part = part.trim();
            if (!part.isEmpty()) {
                if (part.equals(orgName)) {
                    // 如果这部分等于orgName，说明是最后一级
                    levels.add(part);
                } else {
                    // 否则继续分割
                    List<String> subLevels = parseStandardHierarchy(part, "");
                    levels.addAll(subLevels);
                }
            }
        }

        return levels;
    }

    /**
     * 标准层级分割
     */
    private List<String> parseStandardHierarchy(String text, String orgName) {
        List<String> levels = new ArrayList<>();

        if (text == null || text.trim().isEmpty()) {
            return levels;
        }

        // 改进的分割逻辑：基于常见的部门层级后缀
        String[] suffixes = {"有限公司", "事业部", "集团", "厂", "车间", "科", "室", "部", "中心", "处", "班", "组", "站", "队", "所"};

        String remaining = text.trim();

        while (!remaining.isEmpty()) {
            boolean found = false;

            // 查找最早出现的后缀
            int earliestIndex = remaining.length();
            String foundSuffix = "";

            for (String suffix : suffixes) {
                int index = remaining.indexOf(suffix);
                if (index >= 0 && index < earliestIndex) {
                    earliestIndex = index;
                    foundSuffix = suffix;
                }
            }

            if (!foundSuffix.isEmpty() && earliestIndex < remaining.length() - foundSuffix.length()) {
                // 找到了后缀，且不是在字符串末尾
                String level = remaining.substring(0, earliestIndex + foundSuffix.length());
                levels.add(level);
                remaining = remaining.substring(earliestIndex + foundSuffix.length());
                found = true;
            }

            if (!found) {
                // 没找到更多后缀，剩余部分作为最后一级
                if (!remaining.trim().isEmpty()) {
                    levels.add(remaining.trim());
                }
                break;
            }
        }

        // 如果没有分割出多个层级，说明可能是单级部门
        if (levels.isEmpty()) {
            levels.add(text);
        }

        log.debug("解析full_name: {} -> {}", text, levels);
        return levels;
    }

    /**
     * 基于常见模式分割full_name
     */
    private List<String> splitByCommonPatterns(String fullName) {
        List<String> levels = new ArrayList<>();

        // 这里可以添加更复杂的分割逻辑
        // 暂时简单处理
        if (fullName.length() > 6) {
            // 尝试按照常见的部门命名模式分割
            String remaining = fullName;

            // 查找第一个可能的分割点
            for (String suffix : Arrays.asList("厂", "车间", "科", "室", "部", "中心", "处")) {
                int index = remaining.indexOf(suffix);
                if (index > 0 && index < remaining.length() - 1) {
                    levels.add(remaining.substring(0, index + suffix.length()));
                    remaining = remaining.substring(index + suffix.length());
                    break;
                }
            }

            if (!remaining.isEmpty()) {
                levels.add(remaining);
            }
        }

        if (levels.isEmpty()) {
            levels.add(fullName);
        }

        return levels;
    }

    /**
     * 简单的父级匹配策略
     */
    private DepartmentNode findSimpleParent(DepartmentRecord dept, Map<String, DepartmentNode> topLevelNodeMap) {
        String fullName = dept.fullName;
        String orgName = dept.orgName;

        // 基于常见的业务规则进行匹配
        for (Map.Entry<String, DepartmentNode> entry : topLevelNodeMap.entrySet()) {
            String topLevelName = entry.getKey();
            DepartmentNode topLevelNode = entry.getValue();

            // 1. 如果full_name包含一级部门名称
            if (fullName.contains(topLevelName)) {
                return topLevelNode;
            }

            // 2. 基于业务规则匹配
            if (matchByBusinessRules(fullName, orgName, topLevelName)) {
                return topLevelNode;
            }
        }

        return null;
    }

    /**
     * 基于业务规则匹配
     */
    private boolean matchByBusinessRules(String fullName, String orgName, String topLevelName) {
        // 炼铁相关
        if ((fullName.contains("炼铁") || orgName.contains("炼铁")) && topLevelName.equals("炼铁事业部")) {
            return true;
        }

        // 板材相关
        if ((fullName.contains("板") || orgName.contains("板")) && topLevelName.equals("板材事业部")) {
            return true;
        }

        // 特钢相关
        if ((fullName.contains("特钢") || orgName.contains("特钢")) && topLevelName.equals("特钢事业部")) {
            return true;
        }

        // 能源动力相关
        if ((fullName.contains("能源") || fullName.contains("动力")) && topLevelName.equals("能源动力事业部")) {
            return true;
        }

        // 人力资源相关
        if ((fullName.contains("人力") || orgName.contains("人力")) && topLevelName.equals("人力资源部")) {
            return true;
        }

        return false;
    }

    /**
     * 根据层级列表找到对应的一级部门
     */
    private DepartmentNode findTopLevelParentByHierarchy(List<String> hierarchyLevels, Map<String, DepartmentNode> topLevelNodeMap) {
        if (hierarchyLevels.isEmpty()) {
            return null;
        }

        String firstLevel = hierarchyLevels.get(0);

        // 直接匹配一级部门
        for (Map.Entry<String, DepartmentNode> entry : topLevelNodeMap.entrySet()) {
            String topLevelName = entry.getKey();
            DepartmentNode topLevelNode = entry.getValue();

            // 精确匹配或包含匹配
            if (firstLevel.equals(topLevelName) || firstLevel.contains(topLevelName) || topLevelName.contains(firstLevel)) {
                log.debug("找到对应的一级部门: {} -> {}", firstLevel, topLevelName);
                return topLevelNode;
            }
        }

        // 如果找不到，返回默认的一级部门（可以根据业务规则调整）
        log.warn("无法找到对应的一级部门: {}", firstLevel);
        return null;
    }

    /**
     * 判断是否为一级部门（保留原方法）
     */
    private boolean isTopLevelDepartment(String fullName, String orgName) {
        // 1. 精确匹配已知一级部门
        if (TOP_LEVEL_DEPARTMENTS.contains(orgName)) {
            return true;
        }
        
        // 2. full_name等于org_name的情况（如：人力资源部）
        if (fullName != null && fullName.equals(orgName)) {
            return true;
        }
        
        // 3. 明确的一级部门后缀
        if (fullName != null && (
            fullName.matches(".*事业部$") ||
            fullName.matches(".*集团$") ||
            fullName.matches(".*有限公司$")
        )) {
            return true;
        }
        
        return false;
    }

    /**
     * 构建完整的层级路径，动态创建缺失的中间层级部门
     */
    private DepartmentNode buildHierarchyPath(DepartmentRecord dept, Map<String, DepartmentNode> topLevelNodeMap,
                                            Map<String, DepartmentNode> allNodeMap, DepartmentTree tree) {
        String fullName = dept.fullName;
        String orgName = dept.orgName;

        if (fullName == null || fullName.trim().isEmpty()) {
            return null;
        }

        // 1. 首先找到对应的一级部门
        DepartmentNode topLevelParent = findTopLevelParent(dept, topLevelNodeMap, allNodeMap);
        if (topLevelParent == null) {
            return null;
        }

        // 2. 分析full_name，提取层级路径
        List<String> hierarchyPath = extractHierarchyPath(fullName, topLevelParent.orgName, orgName);

        // 3. 从一级部门开始，逐级构建或查找中间层级
        DepartmentNode currentParent = topLevelParent;

        for (String levelName : hierarchyPath) {
            // 检查是否已存在该层级部门
            DepartmentNode existingNode = findExistingNodeByName(levelName, allNodeMap);

            if (existingNode != null) {
                currentParent = existingNode;
                log.debug("找到已存在的中间层级: {}", levelName);
            } else {
                // 动态创建缺失的中间层级部门
                DepartmentNode newNode = createVirtualDepartmentNode(levelName, currentParent);
                allNodeMap.put(newNode.orgCode, newNode);
                tree.addNode(newNode);
                currentParent.children.add(newNode);
                currentParent = newNode;
                log.info("动态创建中间层级部门: {} -> {}", currentParent.orgName, levelName);
            }
        }

        return currentParent;
    }

    /**
     * 从full_name中提取层级路径
     */
    private List<String> extractHierarchyPath(String fullName, String topLevelName, String currentName) {
        List<String> path = new ArrayList<>();

        // 移除一级部门名称前缀
        String remaining = fullName;
        if (fullName.startsWith(topLevelName)) {
            remaining = fullName.substring(topLevelName.length());
        }

        // 移除当前部门名称后缀
        if (remaining.endsWith(currentName)) {
            remaining = remaining.substring(0, remaining.length() - currentName.length());
        }

        // 分析中间的层级结构
        if (!remaining.trim().isEmpty()) {
            // 基于常见的层级分隔符进行分割
            String[] parts = remaining.split("(?<=厂)|(?<=车间)|(?<=科)|(?<=室)|(?<=部)|(?<=中心)|(?<=处)|(?<=班)");

            for (String part : parts) {
                part = part.trim();
                if (!part.isEmpty() && part.length() > 1) {
                    path.add(part);
                }
            }
        }

        return path;
    }

    /**
     * 根据名称查找已存在的节点
     */
    private DepartmentNode findExistingNodeByName(String name, Map<String, DepartmentNode> allNodeMap) {
        for (DepartmentNode node : allNodeMap.values()) {
            if (node.orgName.equals(name)) {
                return node;
            }
        }
        return null;
    }

    /**
     * 创建虚拟的中间层级部门节点
     */
    private DepartmentNode createVirtualDepartmentNode(String name, DepartmentNode parent) {
        DepartmentNode node = new DepartmentNode();
        node.id = generateUniqueId("VIRTUAL_" + name + "_" + System.currentTimeMillis());
        node.orgCode = "VIRTUAL_" + name.hashCode();
        node.orgName = name;
        node.fullName = parent.fullName + name;
        node.parentId = parent.id;
        node.level = parent.level + 1;
        node.isVirtual = true; // 标记为虚拟节点

        return node;
    }

    /**
     * 基于full_name分析，找到对应的一级部门
     */
    private DepartmentNode findTopLevelParent(DepartmentRecord dept, Map<String, DepartmentNode> topLevelNodeMap, Map<String, DepartmentNode> allNodeMap) {
        String fullName = dept.fullName;
        String orgName = dept.orgName;

        if (fullName == null || fullName.trim().isEmpty()) {
            return null;
        }

        // 1. 直接匹配：检查full_name是否包含已知的一级部门名称
        for (Map.Entry<String, DepartmentNode> entry : topLevelNodeMap.entrySet()) {
            String topLevelName = entry.getKey();
            DepartmentNode topLevelNode = entry.getValue();

            // 如果full_name以一级部门名称开头，说明属于该一级部门
            if (fullName.startsWith(topLevelName) && !fullName.equals(topLevelName)) {
                log.debug("通过前缀匹配找到父级: {} -> {}", orgName, topLevelName);
                return topLevelNode;
            }
        }

        // 2. 模糊匹配：分析full_name中的关键词
        for (Map.Entry<String, DepartmentNode> entry : topLevelNodeMap.entrySet()) {
            String topLevelName = entry.getKey();
            DepartmentNode topLevelNode = entry.getValue();

            // 检查是否包含一级部门的关键词
            if (fullName.contains(topLevelName)) {
                log.debug("通过关键词匹配找到父级: {} -> {}", orgName, topLevelName);
                return topLevelNode;
            }
        }

        // 3. 基于业务规则的匹配
        return findParentByBusinessRules(dept, topLevelNodeMap);
    }

    /**
     * 基于业务规则查找父级部门
     */
    private DepartmentNode findParentByBusinessRules(DepartmentRecord dept, Map<String, DepartmentNode> topLevelNodeMap) {
        String fullName = dept.fullName;
        String orgName = dept.orgName;

        // 事业部相关
        if (fullName.contains("事业部")) {
            if (fullName.contains("板材")) {
                return topLevelNodeMap.get("板材事业部");
            } else if (fullName.contains("特钢")) {
                return topLevelNodeMap.get("特钢事业部");
            } else if (fullName.contains("炼铁")) {
                return topLevelNodeMap.get("炼铁事业部");
            } else if (fullName.contains("能源动力")) {
                return topLevelNodeMap.get("能源动力事业部");
            }
        }

        // 集团相关
        if (fullName.contains("蔚蓝高科技集团")) {
            return topLevelNodeMap.get("蔚蓝高科技集团");
        }

        if (fullName.contains("新产业投资集团")) {
            return topLevelNodeMap.get("新产业投资集团");
        }

        // 有限公司相关
        if (fullName.contains("江苏金珂水务有限公司")) {
            return topLevelNodeMap.get("江苏金珂水务有限公司");
        }

        if (fullName.contains("南京三金房地产开发有限公司")) {
            return topLevelNodeMap.get("南京三金房地产开发有限公司");
        }

        if (fullName.contains("南京金智工程技术有限公司")) {
            return topLevelNodeMap.get("南京金智工程技术有限公司");
        }

        if (fullName.contains("江苏南钢鑫洋供应链有限公司")) {
            return topLevelNodeMap.get("江苏南钢鑫洋供应链有限公司");
        }

        // 研究院相关
        if (fullName.contains("新材料研究院")) {
            return topLevelNodeMap.get("新材料研究院（合署）");
        }

        if (fullName.contains("数字应用研究院") || fullName.contains("人工智能研究院")) {
            return topLevelNodeMap.get("数字应用研究院（人工智能研究院）");
        }

        log.debug("无法通过业务规则确定父级: {}", orgName);
        return null;
    }

    /**
     * 查找父级部门的org_code（保留原方法以备用）
     */
    private String findParentOrgCode(DepartmentRecord dept, List<DepartmentRecord> allDepartments) {
        String fullName = dept.fullName;
        String orgName = dept.orgName;
        
        // 如果是一级部门，没有父级
        if (isTopLevelDepartment(fullName, orgName)) {
            return null;
        }
        
        // 通过full_name分析父级
        String parentName = extractParentName(fullName, orgName);
        if (parentName != null) {
            // 查找匹配的父级部门
            for (DepartmentRecord candidate : allDepartments) {
                if (candidate.orgName.equals(parentName) || 
                    (candidate.fullName != null && candidate.fullName.equals(parentName))) {
                    return candidate.orgCode;
                }
            }
        }
        
        return null;
    }

    /**
     * 从full_name中提取父级部门名称
     */
    private String extractParentName(String fullName, String orgName) {
        if (fullName == null || fullName.trim().isEmpty()) {
            return null;
        }
        
        // 如果full_name包含当前部门名称，提取前面的部分作为父级
        if (fullName.contains(orgName)) {
            String prefix = fullName.substring(0, fullName.lastIndexOf(orgName)).trim();
            if (!prefix.isEmpty()) {
                return prefix;
            }
        }
        
        // 其他复杂的层级分析逻辑
        return analyzeParentFromFullName(fullName);
    }

    /**
     * 复杂的父级分析逻辑
     */
    private String analyzeParentFromFullName(String fullName) {
        // 事业部 -> 厂 -> 车间 -> 班组 的层级模式
        if (fullName.contains("事业部") && fullName.contains("厂")) {
            // 提取事业部名称
            int eventIndex = fullName.indexOf("事业部");
            if (eventIndex > 0) {
                return fullName.substring(0, eventIndex + 3);
            }
        }
        
        // 集团 -> 子公司 -> 部门 的层级模式
        if (fullName.contains("集团") && !fullName.endsWith("集团")) {
            int groupIndex = fullName.indexOf("集团");
            if (groupIndex > 0) {
                return fullName.substring(0, groupIndex + 2);
            }
        }
        
        return null;
    }

    // 用于确保ID唯一性的计数器
    private static long idCounter = 1000000L;

    /**
     * 生成唯一ID
     */
    private Long generateUniqueId(String orgCode) {
        // 基于org_code生成唯一的数字ID
        if (orgCode.startsWith("X")) {
            try {
                // 移除X前缀，转换为数字
                String numericPart = orgCode.substring(1);
                return Long.parseLong(numericPart);
            } catch (NumberFormatException e) {
                // 如果转换失败，使用递增的唯一ID
                return ++idCounter;
            }
        } else if (orgCode.startsWith("VIRTUAL_")) {
            // 虚拟节点使用递增ID
            return ++idCounter;
        } else {
            // 对于其他格式的orgCode，尝试解析为数字
            try {
                return Long.parseLong(orgCode);
            } catch (NumberFormatException e) {
                // 如果无法解析，使用递增的唯一ID
                return ++idCounter;
            }
        }
    }

    /**
     * 生成t_org_structure格式的SQL
     */
    private void generateOrgStructureSQL(DepartmentTree tree, String outputFilePath) throws IOException {
        try (PrintWriter writer = new PrintWriter(new FileWriter(outputFilePath))) {
            writer.println("-- =====================================================");
            writer.println("-- 基于full_name智能分析的部门层级数据");
            writer.println("-- 生成时间: " + new Date());
            writer.println("-- 数据来源: department_sync_test2.sql");
            writer.println("-- 总记录数: " + tree.getAllNodes().size());
            writer.println("-- 包含动态创建的中间层级部门");
            writer.println("-- 目标表结构: t_org_structure");
            writer.println("-- =====================================================");
            writer.println();
            
            writer.println("-- 清理现有同步数据");
            writer.println("DELETE FROM t_org_structure WHERE data_source = 2;");
            writer.println();
            
            writer.println("-- 插入分析后的部门层级数据");
            writer.println("INSERT INTO t_org_structure (id, organ_name, pre_id, order_info, is_del, create_time, modify_time, data_source) VALUES");
            
            List<DepartmentNode> allNodes = tree.getAllNodes();
            for (int i = 0; i < allNodes.size(); i++) {
                DepartmentNode node = allNodes.get(i);
                
                writer.printf("(%d, '%s', %s, %d, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 2)",
                    node.id,
                    node.orgName.replace("'", "''"), // 转义单引号
                    node.parentId != null ? node.parentId.toString() : "NULL",
                    i + 1 // 排序信息
                );
                
                if (i < allNodes.size() - 1) {
                    writer.println(",");
                } else {
                    writer.println(";");
                }
            }
            
            writer.println();
            writer.println("-- 验证层级结构");
            writer.println("SELECT ");
            writer.println("    '=== 层级统计 ===' as section,");
            writer.println("    COUNT(*) as total_count,");
            writer.println("    COUNT(CASE WHEN pre_id IS NULL THEN 1 END) as root_count,");
            writer.println("    COUNT(CASE WHEN pre_id IS NOT NULL THEN 1 END) as child_count");
            writer.println("FROM t_org_structure WHERE data_source = 2;");
        }
    }

    // 数据结构类
    private static class DepartmentRecord {
        Long id;
        String orgCode;
        String orgName;
        String parentCode;
        String deptUuid;
        String fullName;
        Integer isHistory;
        String userPredef14;
    }

    private static class DepartmentNode {
        Long id;
        String orgCode;
        String orgName;
        String fullName;
        Long parentId;
        int level;
        boolean isVirtual = false; // 标记是否为虚拟创建的中间层级节点
        List<DepartmentNode> children = new ArrayList<>();
    }

    private static class DepartmentTree {
        private List<DepartmentNode> rootNodes = new ArrayList<>();
        private List<DepartmentNode> allNodes = new ArrayList<>();
        
        public void addNode(DepartmentNode node) {
            allNodes.add(node);
        }
        
        public void addRootNode(DepartmentNode node) {
            rootNodes.add(node);
        }
        
        public List<DepartmentNode> getRootNodes() {
            return rootNodes;
        }
        
        public List<DepartmentNode> getAllNodes() {
            return allNodes;
        }
    }
}
