package com.nercar.datasynchronization.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 部门同步测试实体类
 * 基于ERP同步逻辑的精简版部门表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "department_sync_test")
public class DepartmentSyncTest {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 组织编码（唯一标识）
     */
    @Column(name = "org_code", length = 50, nullable = false, unique = true)
    private String orgCode;

    /**
     * 组织名称
     */
    @Column(name = "org_name", length = 200)
    private String orgName;

    /**
     * 父级编码
     */
    @Column(name = "parent_code", length = 50)
    private String parentCode;

    /**
     * 部门UUID（MDM系统标识）
     */
    @Column(name = "dept_uuid", length = 50)
    private String deptUuid;

    /**
     * 组织全称
     */
    @Column(name = "full_name", length = 500)
    private String fullName;

    /**
     * 历史状态（0=正常，1=历史）
     */
    @Column(name = "is_history")
    private Integer isHistory = 0;

    /**
     * 操作标识（D=删除）
     */
    @Column(name = "user_predef_14", length = 10)
    private String userPredef14;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private LocalDateTime updateTime;

    /**
     * 同步日期
     */
    @Column(name = "sync_date")
    private LocalDate syncDate;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;

    @PrePersist
    protected void onCreate() {
        if (createTime == null) {
            createTime = LocalDateTime.now();
        }
        if (updateTime == null) {
            updateTime = LocalDateTime.now();
        }
        if (isHistory == null) {
            isHistory = 0;
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updateTime = LocalDateTime.now();
    }
}
