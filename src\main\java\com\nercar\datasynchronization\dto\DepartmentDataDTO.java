package com.nercar.datasynchronization.dto;

import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * 部门数据传输对象
 * 用于返回解析后的部门数据给外部系统，对应department表结构
 */
@Data
public class DepartmentDataDTO {

    /**
     * 部门唯一标识符
     */
    private String deptUuid;

    /**
     * 组织代码
     */
    private String orgCode;

    /**
     * 组织名称
     */
    private String orgName;

    /**
     * 父级部门ID
     */
    private String parentId;

    /**
     * 父级部门代码
     */
    private String parentCode;

    /**
     * 部门全称
     */
    private String fullName;

    /**
     * 是否历史部门：0=当前，1=历史
     */
    private Integer isHistory;

    /**
     * 部门描述
     */
    private String description;

    /**
     * 传真
     */
    private String fax;

    /**
     * 网站地址
     */
    private String webAddress;

    /**
     * 部门经理
     */
    private String orgManager;

    /**
     * 邮政编码
     */
    private String postCode;

    /**
     * 自定义字段13
     */
    private String userPredef13;

    /**
     * 自定义字段14
     */
    private String userPredef14;

    /**
     * 自定义字段18
     */
    private String userPredef18;

    /**
     * 记录更新时间
     */
    private Date updateTime;

    /**
     * 组织类型，如公司、部门、科室等
     */
    private String orgType;

    /**
     * 组织层级，如1=集团、2=公司、3=部门、4=科室
     */
    private Integer orgLevel;

    /**
     * 组织路径，存储从根节点到当前节点的完整路径
     */
    private String orgPath;

    /**
     * 完整组织代码，包含所有上级组织代码
     */
    private String fullOrgCode;

    /**
     * 父级节点ID
     */
    private String parentNodeId;

    /**
     * 组织类型编码：1=集团，2=公司，3=部门，4=共享中心
     */
    private String orgTypeCode;

    /**
     * 预算币种编码
     */
    private String budgetCurrency;

    /**
     * 预留字段10
     */
    private String userPredef10;

    /**
     * 预留字段17
     */
    private String userPredef17;

    /**
     * 是否企业法人
     */
    private String isLegalEntity;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 部门子表信息列表（其他系统中的标识信息）
     */
    private List<DepartmentChildDTO> children;
}
