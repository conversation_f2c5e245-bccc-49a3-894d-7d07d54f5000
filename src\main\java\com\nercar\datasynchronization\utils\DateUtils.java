package com.nercar.datasynchronization.utils;

import lombok.extern.slf4j.Slf4j;

import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 日期工具类
 * 提供灵活的日期解析功能，支持多种日期格式
 */
@Slf4j
public class DateUtils {
    
    /**
     * 支持的日期格式列表
     */
    private static final String[] DATE_PATTERNS = {
        "yyyy-MM-dd HH:mm:ss",
        "yyyy/MM/dd HH:mm:ss", 
        "yyyy-MM-dd",
        "yyyy/MM/dd",
        "yyyy-MM-dd'T'HH:mm:ss",
        "yyyy-MM-dd'T'HH:mm:ss.SSS",
        "yyyy-MM-dd'T'HH:mm:ss.SSSZ",
        "yyyy-MM-dd'T'HH:mm:ss'Z'",
        "yyyyMMdd",
        "yyyyMMdd HHmmss",
        "yyyy年MM月dd日",
        "yyyy年MM月dd日 HH时mm分ss秒"
    };
    
    /**
     * 解析日期字符串为Date对象
     * 支持多种日期格式，自动处理URL编码
     * 
     * @param dateStr 日期字符串
     * @return Date对象
     * @throws IllegalArgumentException 如果无法解析日期
     */
    public static Date parseDate(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            throw new IllegalArgumentException("日期字符串不能为空");
        }
        
        // 处理URL编码
        String decodedDateStr = urlDecode(dateStr.trim());
        
        log.debug("尝试解析日期字符串: 原始={}, 解码后={}", dateStr, decodedDateStr);
        
        // 尝试使用不同的日期格式进行解析
        for (String pattern : DATE_PATTERNS) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat(pattern);
                sdf.setLenient(false); // 严格模式
                Date date = sdf.parse(decodedDateStr);
                log.debug("成功使用格式 {} 解析日期: {}", pattern, date);
                return date;
            } catch (ParseException e) {
                // 继续尝试下一个格式
                log.trace("格式 {} 解析失败: {}", pattern, e.getMessage());
            }
        }
        
        // 如果所有格式都失败，抛出异常
        throw new IllegalArgumentException(
            String.format("无法解析日期字符串: %s (原始: %s)。支持的格式: %s", 
                decodedDateStr, dateStr, String.join(", ", DATE_PATTERNS))
        );
    }
    
    /**
     * URL解码
     * 处理URL编码的字符串，如 %20 -> 空格
     * 
     * @param str 需要解码的字符串
     * @return 解码后的字符串
     */
    private static String urlDecode(String str) {
        try {
            // 尝试URL解码
            String decoded = URLDecoder.decode(str, StandardCharsets.UTF_8);
            log.trace("URL解码: {} -> {}", str, decoded);
            return decoded;
        } catch (Exception e) {
            // 如果解码失败，返回原字符串
            log.trace("URL解码失败，使用原字符串: {}", str);
            return str;
        }
    }
    
    /**
     * 验证日期字符串是否有效
     * 
     * @param dateStr 日期字符串
     * @return 是否有效
     */
    public static boolean isValidDate(String dateStr) {
        try {
            parseDate(dateStr);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 格式化日期为标准字符串
     * 
     * @param date 日期对象
     * @return 格式化后的日期字符串
     */
    public static String formatDate(Date date) {
        if (date == null) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(date);
    }
    
    /**
     * 获取支持的日期格式列表
     * 
     * @return 支持的日期格式数组
     */
    public static String[] getSupportedPatterns() {
        return DATE_PATTERNS.clone();
    }
}
