package com.nercar.datasynchronization.controller;

import com.nercar.datasynchronization.service.DataRetrievalService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.servlet.MockMvc;

import java.util.ArrayList;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 数据获取控制器测试类
 */
@WebMvcTest(DataRetrievalController.class)
public class DataRetrievalControllerTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @MockBean
    private DataRetrievalService dataRetrievalService;
    
    @Test
    public void testGetDepartments() throws Exception {
        // 模拟服务返回空列表
        when(dataRetrievalService.getDepartmentData(any(), any())).thenReturn(new ArrayList<>());
        
        mockMvc.perform(get("/api/data/departments")
                .param("startDate", "2024-01-01 00:00:00")
                .param("endDate", "2024-01-02 00:00:00"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("成功获取部门数据"))
                .andExpect(jsonPath("$.totalCount").value(0));
    }
    
    @Test
    public void testGetEmployees() throws Exception {
        // 模拟服务返回空列表
        when(dataRetrievalService.getEmployeeData(any(), any())).thenReturn(new ArrayList<>());
        
        mockMvc.perform(get("/api/data/employees")
                .param("startDate", "2024-01-01 00:00:00")
                .param("endDate", "2024-01-02 00:00:00"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("成功获取员工数据"))
                .andExpect(jsonPath("$.totalCount").value(0));
    }
    
    @Test
    public void testApiTest() throws Exception {
        mockMvc.perform(get("/api/data/test"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("API测试成功"))
                .andExpect(jsonPath("$.data").value("数据获取API正常工作"));
    }
}
