package com.nercar.datasynchronization.utils;

import lombok.extern.slf4j.Slf4j;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.springframework.util.StringUtils;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;

@Slf4j
public class SchemaGeneratorTool {

    // 表结构映射
    private static final Map<String, String> CHILD_TABLE_MAPPING = new HashMap<>();
    static {
        // 子表映射配置：子表XML名称 -> 表名
        CHILD_TABLE_MAPPING.put("O_CHILDS1", "employee_position");
        CHILD_TABLE_MAPPING.put("O_CHILDS2", "employee_title");
        CHILD_TABLE_MAPPING.put("O_CHILDS3", "employee_system");
    }

    public static void generateSchemaFromSoapResponse(String soapResponse, String baseEntityName, String outputPath) {
        try {
            if (!StringUtils.hasText(soapResponse)) {
                log.error("SOAP响应为空");
                return;
            }

            // 解析SOAP响应获取内嵌XML
            String xmlData = extractXmlFromSoapResponse(soapResponse);
            if (xmlData == null) {
                return;
            }
            
            // 解析提取出的真正XML数据
            Document dataDocument = DocumentHelper.parseText(xmlData);
            Element rootElement = dataDocument.getRootElement();
            
            if (!"O_DATAS".equals(rootElement.getName())) {
                log.error("XML根元素不是 O_DATAS, 而是: {}", rootElement.getName());
                return;
            }
            
            // 获取O_DATA元素
            Element dataElement = rootElement.element("O_DATA");
            if (dataElement == null) {
                log.error("未找到 O_DATA 元素");
                return;
            }
            
            // 确保输出目录存在
            Files.createDirectories(Paths.get(outputPath));
            
            // 处理主表
            Map<String, Set<String>> tableFields = new HashMap<>();
            Set<String> mainFields = new HashSet<>();
            
            // 收集主表字段（排除子表结构）
            for (Object obj : dataElement.elements()) {
                if (obj instanceof Element) {
                    Element element = (Element) obj;
                    String name = element.getName();
                    
                    if (!name.startsWith("O_CHILDS")) {
                        mainFields.add(name);
                    }
                }
            }
            
            tableFields.put(baseEntityName, mainFields);
            
            // 处理子表
            Map<String, String> foreignKeyMap = new HashMap<>();
            
            for (Object obj : dataElement.elements()) {
                if (obj instanceof Element) {
                    Element element = (Element) obj;
                    String childTableName = element.getName();
                    
                    if (childTableName.startsWith("O_CHILDS") && CHILD_TABLE_MAPPING.containsKey(childTableName)) {
                        String tableName = CHILD_TABLE_MAPPING.get(childTableName);
                        Element childElement = element.element("O_CHILD");
                        
                        if (childElement != null) {
                            Set<String> childFields = new HashSet<>();
                            // 收集子表的所有字段
                            for (Object childObj : childElement.elements()) {
                                if (childObj instanceof Element) {
                                    childFields.add(((Element) childObj).getName());
                                }
                            }
                            
                            tableFields.put(tableName, childFields);
                            
                            // 记录外键关系
                            foreignKeyMap.put(tableName, "employee_mdm_id");
                        }
                    }
                }
            }
            
            // 生成每个表的实体类和SQL文件
            for (Map.Entry<String, Set<String>> entry : tableFields.entrySet()) {
                String tableName = entry.getKey();
                Set<String> fields = entry.getValue();
                
                // 如果是子表，添加外键字段
                if (foreignKeyMap.containsKey(tableName)) {
                    fields.add("FOREIGN_KEY_FIELD"); // 这是一个占位符
                }
                
                // 生成实体类
                generateEntityClass(tableName, fields, foreignKeyMap.get(tableName), outputPath);
                
                // 生成SQL建表语句
                generateSQLStatement(tableName, fields, foreignKeyMap.get(tableName), outputPath);
            }
            
            log.info("模式生成完成，共生成 {} 个表", tableFields.size());
        } catch (Exception e) {
            log.error("生成模式失败", e);
            e.printStackTrace();
        }
    }
    
    private static String extractXmlFromSoapResponse(String soapResponse) {
        try {
            // 解析SOAP响应
            Document document = DocumentHelper.parseText(soapResponse);
            Element envelope = document.getRootElement();
            
            // 获取Body元素
            Element body = envelope.element("Body");
            if (body == null) {
                log.error("未找到SOAP Body元素");
                return null;
            }
            
            // 获取响应元素 (GetUserInfoFromMDMResponse 或 GetOrgInfoFromMDMResponse)
            Element responseElement = (Element) body.elements().get(0);
            if (responseElement == null) {
                log.error("未找到响应元素");
                return null;
            }
            
            // 获取结果元素
            Element resultElement = null;
            if (responseElement.element("GetUserInfoFromMDMResult") != null) {
                resultElement = responseElement.element("GetUserInfoFromMDMResult");
            } else if (responseElement.element("GetOrgInfoFromMDMResult") != null) {
                resultElement = responseElement.element("GetOrgInfoFromMDMResult");
            } else {
                log.error("未找到结果元素");
                return null;
            }
            
            // 获取结果文本（这是一个转义的XML字符串）
            String escapedXml = resultElement.getText();
            if (!StringUtils.hasText(escapedXml)) {
                log.error("结果元素内容为空");
                return null;
            }
            
            // 反转义XML字符串
            return escapedXml
                .replace("&lt;", "<")
                .replace("&gt;", ">")
                .replace("&amp;", "&")
                .replace("&quot;", "\"")
                .replace("&apos;", "'");
                
        } catch (Exception e) {
            log.error("提取内嵌XML失败", e);
            return null;
        }
    }
    
    private static void generateEntityClass(String tableName, Set<String> fields, String foreignKeyField, String outputPath) throws Exception {
        String className = StringUtils.capitalize(tableName);
        String fileName = className + ".java";
        
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(outputPath + "/" + fileName))) {
            writer.write("package com.nercar.datasynchronization.entity;\n\n");
            writer.write("import jakarta.persistence.*;\n");
            writer.write("import lombok.Data;\n");
            writer.write("import org.hibernate.annotations.DynamicInsert;\n");
            writer.write("import org.hibernate.annotations.DynamicUpdate;\n\n");
            writer.write("import java.util.Date;\n\n");
            
            writer.write("@Data\n");
            writer.write("@Entity\n");
            writer.write("@Table(name = \"" + tableName + "\")\n");
            writer.write("@DynamicInsert\n");
            writer.write("@DynamicUpdate\n");
            writer.write("public class " + className + " {\n\n");
            
            // ID字段
            writer.write("    @Id\n");
            writer.write("    @GeneratedValue(strategy = GenerationType.IDENTITY)\n");
            writer.write("    private Long id;\n\n");
            
            // 如果是子表，添加外键字段
            if (foreignKeyField != null) {
                writer.write("    @Column(name = \"" + foreignKeyField + "\", length = 50)\n");
                writer.write("    private String " + convertToJavaField(foreignKeyField) + ";\n\n");
            }
            
            // 所有从XML中解析的字段
            for (String field : fields) {
                // 跳过占位符
                if ("FOREIGN_KEY_FIELD".equals(field)) continue;
                
                String javaFieldName = convertToJavaField(field);
                writer.write("    @Column(name = \"" + javaFieldName + "\", length = 255)\n");
                writer.write("    private String " + javaFieldName + ";\n\n");
            }
            
            // 通用字段
            writer.write("    @Column(name = \"created_time\")\n");
            writer.write("    @Temporal(TemporalType.TIMESTAMP)\n");
            writer.write("    private Date createdTime;\n\n");
            
            writer.write("    @Column(name = \"updated_time\")\n");
            writer.write("    @Temporal(TemporalType.TIMESTAMP)\n");
            writer.write("    private Date updatedTime;\n");
            
            writer.write("}\n");
        }
        
        log.info("实体类文件已生成: {}", outputPath + "/" + fileName);
    }
    
    private static void generateSQLStatement(String tableName, Set<String> fields, String foreignKeyField, String outputPath) throws Exception {
        String fileName = tableName + "_table.sql";
        
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(outputPath + "/" + fileName))) {
            writer.write("CREATE TABLE `" + tableName + "` (\n");
            writer.write("  `id` bigint NOT NULL AUTO_INCREMENT,\n");
            
            // 如果是子表，添加外键字段
            if (foreignKeyField != null) {
                writer.write("  `" + foreignKeyField + "` varchar(50) DEFAULT NULL,\n");
            }
            
            // 所有从XML中解析的字段
            for (String field : fields) {
                // 跳过占位符
                if ("FOREIGN_KEY_FIELD".equals(field)) continue;
                
                String dbFieldName = convertToJavaField(field);
                writer.write("  `" + dbFieldName + "` varchar(255) DEFAULT NULL,\n");
            }
            
            // 通用字段
            writer.write("  `created_time` datetime DEFAULT NULL,\n");
            writer.write("  `updated_time` datetime DEFAULT NULL,\n");
            writer.write("  PRIMARY KEY (`id`)");
            
            // 如果是子表，添加索引
            if (foreignKeyField != null) {
                writer.write(",\n  INDEX `idx_" + foreignKeyField + "` (`" + foreignKeyField + "`)");
            }
            
            writer.write("\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;\n");
        }
        
        log.info("SQL文件已生成: {}", outputPath + "/" + fileName);
    }
    
    private static String convertToJavaField(String xmlField) {
        // 将XML字段转换为Java字段名（小写，下划线分隔）
        return xmlField.toLowerCase().replaceAll("[^a-zA-Z0-9]", "_");
    }
    
    // 示例用法
    public static void main(String[] args) throws Exception {
// 使用项目目录下的logs文件夹
        String projectPath = System.getProperty("user.dir"); // 获取项目根目录
        String logsPath = projectPath + "/logs"; // 项目下的logs目录
        String outputPath = logsPath + "/schema"; // 在logs下创建schema子目录存放生成的文件

// 确保目录存在
        Files.createDirectories(Paths.get(outputPath));

// 读取SOAP响应XML文件（假设也放在logs目录下）
        String soapXmlFilePath = logsPath + "/sample_employee.xml";
        String soapXmlData = new String(Files.readAllBytes(Paths.get(soapXmlFilePath)));

// 从SOAP响应生成模式
        generateSchemaFromSoapResponse(soapXmlData, "employee", outputPath);

        System.out.println("处理完成，输出目录: " + outputPath);
    }
}