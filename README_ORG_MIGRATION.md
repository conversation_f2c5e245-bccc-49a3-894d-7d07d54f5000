# 组织架构迁移功能使用说明

## 概述

组织架构迁移功能用于将MySQL数据库中的`department`表数据迁移到PostgreSQL数据库中的`t_org_structure`表。该功能支持层级关系转换、状态过滤、批量处理等特性。

## 功能特点

- ✅ **跨数据库迁移**：MySQL → PostgreSQL
- ✅ **状态过滤**：只迁移正常状态的部门（`is_history=0` 且 `user_predef_14!='D'`）
- ✅ **层级关系保持**：正确转换父子关系
- ✅ **批量处理**：高效的批量插入操作
- ✅ **数据验证**：完整性检查和层级关系验证
- ✅ **安全清理**：只删除同步数据，保留手工数据

## 数据映射关系

| MySQL字段 | PostgreSQL字段 | 说明 |
|-----------|----------------|------|
| `org_name` | `organ_name` | 组织名称 |
| `parent_id` | `pre_id` | 父级ID（需要转换） |
| `org_code` | `order_info` | 排序信息（基于组织代码生成） |
| - | `is_del` | 固定为`false`（已过滤删除数据） |
| `update_time` | `create_time` | 创建时间 |
| `update_time` | `modify_time` | 修改时间 |
| - | `data_source` | 固定为`2`（数据同步标识） |

## 前置条件

### 1. 数据库配置

确保`application.yml`中已配置PostgreSQL数据源：

```yaml
spring:
  datasource:
    postgresql:
      url: ************************************
      username: your_username
      password: your_password
      driver-class-name: org.postgresql.Driver
```

### 2. PostgreSQL初始化

执行初始化脚本创建序列：

```sql
-- 执行 src/main/resources/sql/postgresql_init.sql
CREATE SEQUENCE IF NOT EXISTS t_org_structure_id_seq
    START WITH 1
    INCREMENT BY 1;
```

### 3. 依赖检查

确保项目中包含PostgreSQL驱动：

```xml
<dependency>
    <groupId>org.postgresql</groupId>
    <artifactId>postgresql</artifactId>
    <scope>runtime</scope>
</dependency>
```

## API接口使用

### 1. 检查连接状态

```bash
GET /api/migration/org-structure/check-connection
```

**响应示例**：
```json
{
  "success": true,
  "connected": true,
  "message": "PostgreSQL连接正常"
}
```

### 2. 执行数据迁移

```bash
POST /api/migration/org-structure/migrate?clearExisting=false
```

**参数说明**：
- `clearExisting`: 是否清理现有同步数据（默认：false）

**响应示例**：
```json
{
  "success": true,
  "message": "组织架构数据迁移成功",
  "statistics": "迁移统计信息:\n- MySQL正常部门数量: 150\n- PostgreSQL迁移记录数量: 150\n..."
}
```

### 3. 验证迁移结果

```bash
GET /api/migration/org-structure/validate
```

**响应示例**：
```json
{
  "success": true,
  "message": "迁移结果验证通过",
  "migratedCount": 150,
  "statistics": "..."
}
```

### 4. 获取统计信息

```bash
GET /api/migration/org-structure/statistics
```

### 5. 清理同步数据

```bash
DELETE /api/migration/org-structure/clear
```

## 使用流程

### 标准迁移流程

1. **检查连接**
   ```bash
   curl -X GET "http://localhost:8080/api/migration/org-structure/check-connection"
   ```

2. **执行迁移**
   ```bash
   curl -X POST "http://localhost:8080/api/migration/org-structure/migrate?clearExisting=true"
   ```

3. **验证结果**
   ```bash
   curl -X GET "http://localhost:8080/api/migration/org-structure/validate"
   ```

### 重新迁移流程

如果需要重新迁移：

1. **清理数据**
   ```bash
   curl -X DELETE "http://localhost:8080/api/migration/org-structure/clear"
   ```

2. **重新迁移**
   ```bash
   curl -X POST "http://localhost:8080/api/migration/org-structure/migrate"
   ```

## 注意事项

### 1. 数据安全

- ✅ 只删除`data_source=2`的记录，不影响手工录入数据
- ✅ 支持事务回滚，迁移失败时自动回滚
- ✅ 提供验证功能，确保数据完整性

### 2. 性能考虑

- 批量处理：每批500条记录
- 分层处理：避免深度递归
- 索引优化：自动创建必要索引

### 3. 错误处理

- 连接失败：检查PostgreSQL配置
- 迁移失败：查看日志获取详细错误信息
- 验证失败：检查数据一致性和层级关系

### 4. 监控建议

- 定期检查迁移统计信息
- 监控PostgreSQL连接状态
- 关注应用日志中的错误信息

## 故障排除

### 常见问题

1. **PostgreSQL连接失败**
   - 检查数据库配置
   - 确认网络连通性
   - 验证用户权限

2. **迁移数据不完整**
   - 检查MySQL中的数据状态
   - 验证过滤条件
   - 查看详细日志

3. **层级关系错误**
   - 检查parent_id字段
   - 验证数据完整性
   - 重新执行迁移

### 日志查看

迁移过程中的详细日志会记录在应用日志中，关键日志级别：

- `INFO`：迁移进度和统计信息
- `WARN`：数据异常警告
- `ERROR`：迁移失败错误
- `DEBUG`：详细的映射和处理信息

## 技术支持

如遇到问题，请提供以下信息：

1. 错误日志
2. 迁移统计信息
3. 数据库配置（脱敏）
4. MySQL和PostgreSQL的数据样本
