package com.nercar.datasynchronization.client.impl;

import com.nercar.datasynchronization.client.SoapClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * SOAP客户端实现
 * 用于调用MDM系统的SOAP服务获取组织和人员信息
 */
@Slf4j
@Component
public class SoapClientImpl implements SoapClient {

    @Value("${sync.soap.user-url}")
    private String userUrl;

    @Value("${sync.soap.org-url}")
    private String orgUrl;

    // 超时设置（毫秒）- 可配置
    @Value("${sync.soap.connect-timeout:30000}")
    private int connectTimeout;

    @Value("${sync.soap.read-timeout:120000}")  // 增加到2分钟，因为MDM系统响应较慢
    private int readTimeout;

    // 使用斜杠分隔的日期格式，与接口文档中的日期格式保持一致
    private static final ThreadLocal<SimpleDateFormat> DATE_FORMAT =
            ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy/MM/dd HH:mm:ss"));

    /**
     * 获取用户信息
     * 调用文档1.5.3人员信息下发接口中的GetUserInfoFromMDM方法
     *
     * @param startDate 开始时间，对应I_UDEF2参数
     * @param endDate 结束时间，对应I_UDEF3参数
     * @return SOAP响应XML
     */
    @Override
    public String getUserInfo(Date startDate, Date endDate) {
        String startStr = DATE_FORMAT.get().format(startDate);
        String endStr = DATE_FORMAT.get().format(endDate);

        String soapRequest = buildUserInfoRequest(startStr, endStr);
        log.debug("请求人员数据，参数：开始时间={}，结束时间={}", startStr, endStr);
        // GetUserInfoFromMDM 操作名用于SOAPAction头
        return sendSoapRequest(userUrl, soapRequest, "http://tempuri.org/GetUserInfoFromMDM");
    }

    /**
     * 获取组织信息
     * 调用文档1.5.1组织信息下发接口中的GetOrgInfoFromMDM方法
     *
     * @param startDate 开始时间，对应I_UDEF2参数
     * @param endDate 结束时间，对应I_UDEF3参数
     * @return SOAP响应XML
     */
    @Override
    public String getOrgInfo(Date startDate, Date endDate) {
        String startStr = DATE_FORMAT.get().format(startDate);
        String endStr = DATE_FORMAT.get().format(endDate);

        String soapRequest = buildOrgInfoRequest(startStr, endStr);
        log.debug("请求部门数据，参数：开始时间={}，结束时间={}", startStr, endStr);
        // GetOrgInfoFromMDM 操作名用于SOAPAction头
        return sendSoapRequest(orgUrl, soapRequest, "http://tempuri.org/GetOrgInfoFromMDM");
    }

    /**
     * 构建获取用户信息的SOAP请求
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return SOAP请求XML
     */
    private String buildUserInfoRequest(String startDate, String endDate) {
        return "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\">" +
                "<soapenv:Header/>" +
                "<soapenv:Body>" +
                "<tem:GetUserInfoFromMDM>" +
                "<tem:InXml><![CDATA[<?xml version=\"1.0\" encoding=\"utf-8\"?>" +
                "<I_DATAS><I_DATA>" +
                "<I_UDEF1>ERP</I_UDEF1>" + // 系统标识，ERP表示母公司
                "<I_UDEF2>" + startDate + "</I_UDEF2>" + // 开始日期
                "<I_UDEF3>" + endDate + "</I_UDEF3>" + // 截止日期
                "<I_UDEF4></I_UDEF4>" + // 预留条件字段
                "</I_DATA></I_DATAS>]]></tem:InXml>" +
                "</tem:GetUserInfoFromMDM>" +
                "</soapenv:Body>" +
                "</soapenv:Envelope>";
    }

    /**
     * 构建获取组织信息的SOAP请求
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return SOAP请求XML
     */
    private String buildOrgInfoRequest(String startDate, String endDate) {
        return "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\">" +
                "<soapenv:Header/>" +
                "<soapenv:Body>" +
                "<tem:GetOrgInfoFromMDM>" +
                "<tem:InXml><![CDATA[<?xml version=\"1.0\" encoding=\"utf-8\"?>" +
                "<I_DATAS><I_DATA>" +
                "<I_UDEF1>ERP</I_UDEF1>" + // 系统标识，ERP表示母公司
                "<I_UDEF2>" + startDate + "</I_UDEF2>" + // 开始日期
                "<I_UDEF3>" + endDate + "</I_UDEF3>" + // 截止日期
                "<I_UDEF4></I_UDEF4>" + // 预留条件字段
                "</I_DATA></I_DATAS>]]></tem:InXml>" +
                "</tem:GetOrgInfoFromMDM>" +
                "</soapenv:Body>" +
                "</soapenv:Envelope>";
    }

    /**
     * 发送SOAP请求并获取响应
     *
     * @param url SOAP服务URL
     * @param soapRequest SOAP请求XML
     * @param soapAction SOAP操作名
     * @return SOAP响应XML
     */
    private String sendSoapRequest(String url, String soapRequest, String soapAction) {
        HttpURLConnection con = null;
        try {
            URL obj = new URL(url);
            con = (HttpURLConnection) obj.openConnection();

            // 设置连接和读取超时
            con.setConnectTimeout(connectTimeout);
            con.setReadTimeout(readTimeout);

            log.debug("SOAP请求超时设置: 连接超时={}ms, 读取超时={}ms", connectTimeout, readTimeout);

            // 设置请求方法和请求头
            con.setRequestMethod("POST");
            con.setRequestProperty("Content-Type", "text/xml;charset=UTF-8");
            // 设置正确的SOAPAction头
            con.setRequestProperty("SOAPAction", soapAction);
            con.setDoOutput(true);

            log.debug("发送SOAP请求到 {}, SOAPAction: {}", url, soapAction);

            // 发送请求
            try (OutputStream os = con.getOutputStream()) {
                byte[] input = soapRequest.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }

            // 读取响应
            int responseCode = con.getResponseCode();
            log.debug("SOAP请求响应码: {}", responseCode);

            if (responseCode == HttpURLConnection.HTTP_OK) {
                StringBuilder response = new StringBuilder();
                try (BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream(), StandardCharsets.UTF_8))) {
                    String inputLine;
                    while ((inputLine = in.readLine()) != null) {
                        response.append(inputLine);
                    }
                }

                String responseString = response.toString();

                // 记录响应长度和前100字符
                String logResponse = responseString.length() > 100 ?
                        responseString.substring(0, 100) + "..." : responseString;
                log.debug("SOAP请求响应长度: {} 字符，前100字符: {}", responseString.length(), logResponse);

                // 验证响应数据完整性
                if (responseString.trim().isEmpty()) {
                    log.error("收到空的SOAP响应");
                    throw new RuntimeException("收到空的SOAP响应");
                }

                // 检查XML结构完整性
                String trimmedResponse = responseString.trim();
                if (!trimmedResponse.startsWith("<?xml") && !trimmedResponse.startsWith("<soap:")) {
                    log.error("响应不是有效的XML格式，开头: {}",
                        trimmedResponse.length() > 50 ? trimmedResponse.substring(0, 50) : trimmedResponse);
                    throw new RuntimeException("响应不是有效的XML格式");
                }

                if (!trimmedResponse.endsWith("</soap:Envelope>") && !trimmedResponse.endsWith("</Envelope>")) {
                    log.error("XML响应可能不完整，末尾: {}",
                        trimmedResponse.length() > 100 ? trimmedResponse.substring(trimmedResponse.length() - 100) : trimmedResponse);
                    throw new RuntimeException("XML响应数据不完整");
                }

                return responseString;
            } else {
                log.error("SOAP请求失败，响应码: {}", responseCode);

                // 尝试读取错误流
                StringBuilder errorResponse = new StringBuilder();
                try (BufferedReader in = new BufferedReader(
                        new InputStreamReader(con.getErrorStream() != null ?
                                              con.getErrorStream() :
                                              con.getInputStream(), StandardCharsets.UTF_8))) {
                    String inputLine;
                    while ((inputLine = in.readLine()) != null) {
                        errorResponse.append(inputLine);
                    }
                } catch (Exception e) {
                    log.error("读取错误流失败", e);
                }

                if (errorResponse.length() > 0) {
                    log.error("错误响应内容: {}", errorResponse.toString());
                    throw new RuntimeException(String.format("SOAP请求失败，响应码: %d, 错误内容: %s",
                            responseCode, errorResponse.toString()));
                } else {
                    throw new RuntimeException(String.format("SOAP请求失败，响应码: %d", responseCode));
                }
            }
        } catch (Exception e) {
            log.error("发送SOAP请求时发生异常: {}", e.getMessage(), e);
            // 重要：抛出异常而不是返回null，这样重试机制才能正常工作
            throw new RuntimeException("SOAP请求失败: " + e.getMessage(), e);
        } finally {
            // 确保在finally块中关闭连接
            if (con != null) {
                con.disconnect();
            }
        }
    }

    @Override
    public void destroy() {
        // 清理ThreadLocal资源
        DATE_FORMAT.remove();
    }
}