package com.nercar.datasynchronization.utils;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 时间分片工具类
 * 用于将大时间段拆分成小片段，避免单次请求数据量过大导致超时
 */
@Slf4j
public class TimeSliceUtils {
    
    /**
     * 默认分片大小（小时）
     */
    public static final int DEFAULT_SLICE_HOURS = 24;
    
    /**
     * 最小分片大小（小时）
     */
    public static final int MIN_SLICE_HOURS = 1;
    
    /**
     * 最大分片大小（小时）
     */
    public static final int MAX_SLICE_HOURS = 168; // 7天
    
    /**
     * 时间片段
     */
    @Data
    public static class TimeSlice {
        private Date startDate;
        private Date endDate;
        private int sliceIndex;
        private int totalSlices;
        
        public TimeSlice(Date startDate, Date endDate, int sliceIndex, int totalSlices) {
            this.startDate = startDate;
            this.endDate = endDate;
            this.sliceIndex = sliceIndex;
            this.totalSlices = totalSlices;
        }
        
        @Override
        public String toString() {
            return String.format("片段%d/%d: %s ~ %s", 
                    sliceIndex, totalSlices, 
                    DateUtils.formatDate(startDate), 
                    DateUtils.formatDate(endDate));
        }
    }
    
    /**
     * 将时间范围分片
     * 
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param sliceHours 分片大小（小时）
     * @return 时间片段列表
     */
    public static List<TimeSlice> sliceTimeRange(Date startDate, Date endDate, int sliceHours) {
        if (startDate == null || endDate == null) {
            throw new IllegalArgumentException("开始时间和结束时间不能为空");
        }
        
        if (startDate.after(endDate)) {
            throw new IllegalArgumentException("开始时间不能晚于结束时间");
        }
        
        if (sliceHours < MIN_SLICE_HOURS || sliceHours > MAX_SLICE_HOURS) {
            throw new IllegalArgumentException(String.format("分片大小必须在%d到%d小时之间", MIN_SLICE_HOURS, MAX_SLICE_HOURS));
        }
        
        List<TimeSlice> slices = new ArrayList<>();
        
        LocalDateTime start = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDateTime end = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        
        // 计算总的小时数
        long totalHours = ChronoUnit.HOURS.between(start, end);
        
        // 如果时间范围小于等于分片大小，直接返回一个片段
        if (totalHours <= sliceHours) {
            slices.add(new TimeSlice(startDate, endDate, 1, 1));
            log.info("时间范围较小，无需分片: {} 小时", totalHours);
            return slices;
        }
        
        // 计算分片数量
        int totalSlices = (int) Math.ceil((double) totalHours / sliceHours);
        
        log.info("时间范围分片: 总时长{}小时，分片大小{}小时，共{}个片段", totalHours, sliceHours, totalSlices);
        
        LocalDateTime currentStart = start;
        
        for (int i = 1; i <= totalSlices; i++) {
            LocalDateTime currentEnd = currentStart.plusHours(sliceHours);
            
            // 最后一个片段，确保不超过结束时间
            if (i == totalSlices || currentEnd.isAfter(end)) {
                currentEnd = end;
            }
            
            Date sliceStart = Date.from(currentStart.atZone(ZoneId.systemDefault()).toInstant());
            Date sliceEnd = Date.from(currentEnd.atZone(ZoneId.systemDefault()).toInstant());
            
            TimeSlice slice = new TimeSlice(sliceStart, sliceEnd, i, totalSlices);
            slices.add(slice);
            
            log.debug("创建时间片段: {}", slice);
            
            currentStart = currentEnd;
            
            // 如果已经到达结束时间，跳出循环
            if (!currentStart.isBefore(end)) {
                break;
            }
        }
        
        return slices;
    }
    
    /**
     * 使用默认分片大小分片时间范围
     * 
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 时间片段列表
     */
    public static List<TimeSlice> sliceTimeRange(Date startDate, Date endDate) {
        return sliceTimeRange(startDate, endDate, DEFAULT_SLICE_HOURS);
    }
    
    /**
     * 智能分片：根据时间范围自动选择合适的分片大小
     * 
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 时间片段列表
     */
    public static List<TimeSlice> smartSliceTimeRange(Date startDate, Date endDate) {
        LocalDateTime start = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDateTime end = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        
        long totalHours = ChronoUnit.HOURS.between(start, end);
        
        int sliceHours;
        if (totalHours <= 24) {
            // 1天以内，不分片
            sliceHours = (int) totalHours;
        } else if (totalHours <= 168) {
            // 1周以内，按天分片
            sliceHours = 24;
        } else if (totalHours <= 720) {
            // 1个月以内，按3天分片
            sliceHours = 72;
        } else {
            // 超过1个月，按周分片
            sliceHours = 168;
        }
        
        log.info("智能分片: 总时长{}小时，选择分片大小{}小时", totalHours, sliceHours);
        
        return sliceTimeRange(startDate, endDate, sliceHours);
    }
    
    /**
     * 计算时间范围的总小时数
     * 
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 总小时数
     */
    public static long calculateTotalHours(Date startDate, Date endDate) {
        LocalDateTime start = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDateTime end = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        return ChronoUnit.HOURS.between(start, end);
    }
    
    /**
     * 判断是否需要分片
     * 
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param thresholdHours 阈值小时数
     * @return 是否需要分片
     */
    public static boolean needsSlicing(Date startDate, Date endDate, int thresholdHours) {
        return calculateTotalHours(startDate, endDate) > thresholdHours;
    }
    
    /**
     * 判断是否需要分片（使用默认阈值24小时）
     * 
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 是否需要分片
     */
    public static boolean needsSlicing(Date startDate, Date endDate) {
        return needsSlicing(startDate, endDate, DEFAULT_SLICE_HOURS);
    }
}
