package com.nercar.datasynchronization.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.util.Date;

@Data
@Entity
@Table(name = "employee")
@DynamicInsert
@DynamicUpdate
public class Employee {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "mdmzgzd_tybz", length = 255)
    private String mdmzgzd_tybz;

    @Column(name = "mdmzgzd_tel", length = 255)
    private String mdmzgzd_tel;

    @Column(name = "mdmzgzd_zgxb", length = 255)
    private String mdmzgzd_zgxb;

    @Column(name = "userpredef_4", length = 255)
    private String userpredef_4;

    @Column(name = "userpredef_5", length = 255)
    private String userpredef_5;

    @Column(name = "mdmzgzd_zgxm", length = 255)
    private String mdmzgzd_zgxm;

    @Column(name = "userpredef_3", length = 255)
    private String userpredef_3;

    @Column(name = "userpredef_17", length = 255)
    private String userpredef_17;

    @Column(name = "userpredef_8", length = 255)
    private String userpredef_8;

    @Column(name = "userpredef_18", length = 255)
    private String userpredef_18;

    @Column(name = "userpredef_9", length = 255)
    private String userpredef_9;

    @Column(name = "userpredef_19", length = 255)
    private String userpredef_19;

    @Column(name = "userpredef_6", length = 255)
    private String userpredef_6;

    @Column(name = "mdmzgzd_mobile", length = 255)
    private String mdmzgzd_mobile;

    @Column(name = "userpredef_7", length = 255)
    private String userpredef_7;

    @Column(name = "userpredef_13", length = 255)
    private String userpredef_13;

    @Column(name = "userpredef_14", length = 255)
    private String userpredef_14;

    @Column(name = "userpredef_15", length = 255)
    private String userpredef_15;

    @Column(name = "userpredef_16", length = 255)
    private String userpredef_16;

    @Column(name = "mdmzgzd_csrq", length = 255)
    private String mdmzgzd_csrq;

    @Column(name = "userpredef_10", length = 255)
    private String userpredef_10;

    @Column(name = "userpredef_11", length = 255)
    private String userpredef_11;

    @Column(name = "mdmzgzd_wechat", length = 255)
    private String mdmzgzd_wechat;

    @Column(name = "userpredef_12", length = 255)
    private String userpredef_12;

    @Column(name = "mdmzgzd_cyxm", length = 255)
    private String mdmzgzd_cyxm;

    @Column(name = "mdmzgzd_zgbh", length = 255)
    private String mdmzgzd_zgbh;

    @Column(name = "mdmzgzd_note", length = 255)
    private String mdmzgzd_note;

    @Column(name = "mdmzgzd_email", length = 255)
    private String mdmzgzd_email;

    @Column(name = "mdmzgzd_hrdwnm", length = 255)
    private String mdmzgzd_hrdwnm;

    @Column(name = "mdmzgzd_zh", length = 255)
    private String mdmzgzd_zh;

    @Column(name = "userpredef_20", length = 255)
    private String userpredef_20;

    @Column(name = "mdmzgzd_nm", length = 255)
    private String mdmzgzd_nm;

    @Column(name = "userpredef_21", length = 255)
    private String userpredef_21;

    @Column(name = "created_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdTime;

    @Column(name = "updated_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedTime;
}
