package com.nercar.datasynchronization.repository;

import com.nercar.datasynchronization.entity.Department;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DepartmentRepository extends JpaRepository<Department, Integer> {

    /**
     * 根据部门UUID查找部门
     */
    Department findByDeptUuid(String deptUuid);

    /**
     * 根据组织代码查找部门
     */
    Department findByOrgCode(String orgCode);

    /**
     * 根据组织名称查找部门
     */
    Department findByOrgName(String orgName);

    /**
     * 根据父部门ID查找子部门
     */
    List<Department> findByParentId(String parentId);

    /**
     * 根据组织代码检查部门是否存在
     */
    boolean existsByOrgCode(String orgCode);

    /**
     * 根据父部门代码查找子部门
     */
    List<Department> findByParentCode(String parentCode);

    /**
     * 根据组织名称模糊查询
     */
    List<Department> findByOrgNameContaining(String nameKeyword);

    /**
     * 查询非历史部门
     */
    List<Department> findByIsHistory(Integer isHistory);

    /**
     * 查询所有正常状态的部门（用于迁移）
     * 条件：is_history = 0 AND user_predef_14 != 'D'
     * 按组织代码排序
     */
    @Query("SELECT d FROM Department d WHERE d.isHistory = 0 AND d.userPredef14 != 'D' ORDER BY d.orgCode")
    List<Department> findActiveDepartments();

    /**
     * 查询根节点部门（用于迁移）
     * 条件：is_history = 0 AND user_predef_14 != 'D' AND (parent_code IS NULL OR parent_code = '')
     */
    @Query("SELECT d FROM Department d WHERE d.isHistory = 0 AND d.userPredef14 != 'D' AND (d.parentCode IS NULL OR d.parentCode = '') ORDER BY d.orgCode")
    List<Department> findRootDepartments();

    /**
     * 根据父级组织代码查询子部门（用于迁移）
     * 条件：is_history = 0 AND user_predef_14 != 'D' AND parent_code = :parentCode
     */
    @Query("SELECT d FROM Department d WHERE d.isHistory = 0 AND d.userPredef14 != 'D' AND d.parentCode = :parentCode ORDER BY d.orgCode")
    List<Department> findChildDepartmentsByParentCode(@Param("parentCode") String parentCode);

    /**
     * 统计正常状态部门数量
     */
    @Query("SELECT COUNT(d) FROM Department d WHERE d.isHistory = 0 AND d.userPredef14 != 'D'")
    long countActiveDepartments();

    /**
     * 分页查询正常状态部门（用于大数据量处理）
     */
    @Query("SELECT d FROM Department d WHERE d.isHistory = 0 AND d.userPredef14 != 'D' ORDER BY d.orgCode")
    List<Department> findActiveDepartmentsWithLimit(@Param("offset") int offset, @Param("limit") int limit);

    // =====================================================
    // 数据诊断查询方法 - 用于分析数据不全问题
    // =====================================================

    /**
     * 查询所有部门（包括历史和删除的）- 用于数据完整性分析
     */
    @Query("SELECT d FROM Department d ORDER BY d.orgCode")
    List<Department> findAllDepartmentsForAnalysis();

    /**
     * 查询历史部门数量
     */
    @Query("SELECT COUNT(d) FROM Department d WHERE d.isHistory = 1")
    long countHistoryDepartments();

    /**
     * 查询删除状态部门数量
     */
    @Query("SELECT COUNT(d) FROM Department d WHERE d.userPredef14 = 'D'")
    long countDeletedDepartments();

    /**
     * 查询既是历史又是删除状态的部门数量
     */
    @Query("SELECT COUNT(d) FROM Department d WHERE d.isHistory = 1 AND d.userPredef14 = 'D'")
    long countHistoryAndDeletedDepartments();

    /**
     * 查询父级代码不存在的孤儿部门
     */
    @Query("SELECT d FROM Department d WHERE d.isHistory = 0 AND d.userPredef14 != 'D' " +
           "AND d.parentCode IS NOT NULL AND d.parentCode != '' " +
           "AND NOT EXISTS (SELECT p FROM Department p WHERE p.orgCode = d.parentCode AND p.isHistory = 0 AND p.userPredef14 != 'D')")
    List<Department> findOrphanDepartments();

    /**
     * 查询特定组织代码的所有状态记录（用于分析状态变更）
     */
    @Query("SELECT d FROM Department d WHERE d.orgCode = :orgCode ORDER BY d.updateTime DESC")
    List<Department> findAllByOrgCode(@Param("orgCode") String orgCode);

    /**
     * 查询可能被误过滤的部门（历史=0但删除标记=D）
     */
    @Query("SELECT d FROM Department d WHERE d.isHistory = 0 AND d.userPredef14 = 'D'")
    List<Department> findPossiblyMissingDepartments();

    /**
     * 查询根节点候选（parent_code为空或为特殊值）
     */
    @Query("SELECT d FROM Department d WHERE (d.parentCode IS NULL OR d.parentCode = '' OR d.parentCode = 'X') ORDER BY d.orgCode")
    List<Department> findRootCandidates();

    /**
     * 按状态分组统计
     */
    @Query("SELECT d.isHistory, d.userPredef14, COUNT(d) FROM Department d GROUP BY d.isHistory, d.userPredef14")
    List<Object[]> countByStatus();
}