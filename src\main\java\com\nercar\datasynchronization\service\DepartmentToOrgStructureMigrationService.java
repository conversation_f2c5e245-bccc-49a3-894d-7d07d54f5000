package com.nercar.datasynchronization.service;

import com.nercar.datasynchronization.entity.DepartmentSyncTest;
import com.nercar.datasynchronization.repository.DepartmentSyncTestRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.FileWriter;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 部门数据迁移服务
 * 将department_sync_test数据迁移为t_org_structure格式
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DepartmentToOrgStructureMigrationService {

    private final DepartmentSyncTestRepository departmentSyncTestRepository;

    /**
     * 执行数据迁移，生成PostgreSQL格式的SQL文件
     */
    public String migrateToOrgStructure(String outputFilePath) {
        try {
            log.info("开始数据迁移，目标文件: {}", outputFilePath);
            
            // 获取所有有效的部门数据
            List<DepartmentSyncTest> departments = departmentSyncTestRepository.findAll().stream()
                    .filter(d -> d.getIsHistory() == 0 && !"D".equals(d.getUserPredef14()))
                    .collect(Collectors.toList());
            
            log.info("获取到 {} 条有效部门数据", departments.size());
            
            // 生成SQL文件
            generateSqlFile(departments, outputFilePath);
            
            log.info("数据迁移完成，生成文件: {}", outputFilePath);
            return "迁移成功，共处理 " + departments.size() + " 条记录";
            
        } catch (Exception e) {
            log.error("数据迁移失败", e);
            throw new RuntimeException("数据迁移失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成PostgreSQL格式的SQL文件
     */
    private void generateSqlFile(List<DepartmentSyncTest> departments, String outputFilePath) throws IOException {
        try (FileWriter writer = new FileWriter(outputFilePath)) {
            // 写入文件头注释
            writer.write("-- =====================================================\n");
            writer.write("-- 部门数据迁移SQL - 从department_sync_test到t_org_structure\n");
            writer.write("-- 生成时间: " + java.time.LocalDateTime.now() + "\n");
            writer.write("-- 数据来源: ERP系统25年完整同步数据\n");
            writer.write("-- 记录数量: " + departments.size() + "\n");
            writer.write("-- =====================================================\n\n");
            
            // 写入表结构说明
            writer.write("-- 目标表结构: t_org_structure\n");
            writer.write("-- id: numeric (组织编码，来源于org_code)\n");
            writer.write("-- organ_name: varchar (组织名称，来源于org_name)\n");
            writer.write("-- pre_id: numeric (父级ID，来源于parent_code)\n");
            writer.write("-- order_info: numeric (排序信息，默认值)\n");
            writer.write("-- is_del: numeric (删除标识，0=正常)\n");
            writer.write("-- data_source: numeric (数据来源，2=同步数据)\n\n");
            
            // 写入清理语句
            writer.write("-- 清理现有同步数据\n");
            writer.write("DELETE FROM t_org_structure WHERE data_source = 2;\n\n");
            
            // 统计信息
            long rootCount = departments.stream()
                    .filter(d -> "1".equals(d.getParentCode()) || 
                               d.getParentCode() == null || 
                               d.getParentCode().isEmpty())
                    .count();
            
            writer.write("-- 数据统计信息\n");
            writer.write("-- 总记录数: " + departments.size() + "\n");
            writer.write("-- 根节点数: " + rootCount + "\n");
            writer.write("-- 子节点数: " + (departments.size() - rootCount) + "\n\n");
            
            // 生成INSERT语句
            writer.write("-- 插入部门数据\n");
            writer.write("INSERT INTO t_org_structure (id, organ_name, pre_id, order_info, is_del, data_source) VALUES\n");
            
            for (int i = 0; i < departments.size(); i++) {
                DepartmentSyncTest dept = departments.get(i);
                String insertLine = generateInsertLine(dept);
                
                if (i == departments.size() - 1) {
                    writer.write(insertLine + ";\n");
                } else {
                    writer.write(insertLine + ",\n");
                }
            }
            
            // 写入验证查询
            writer.write("\n-- 验证查询\n");
            writer.write("SELECT \n");
            writer.write("    '数据验证' as 检查项,\n");
            writer.write("    COUNT(*) as 记录数\n");
            writer.write("FROM t_org_structure \n");
            writer.write("WHERE data_source = 2;\n\n");
            
            writer.write("-- 根节点查询\n");
            writer.write("SELECT \n");
            writer.write("    id,\n");
            writer.write("    organ_name,\n");
            writer.write("    pre_id\n");
            writer.write("FROM t_org_structure \n");
            writer.write("WHERE data_source = 2 AND pre_id IS NULL\n");
            writer.write("ORDER BY id;\n\n");
            
            writer.write("-- 孤儿节点检查\n");
            writer.write("SELECT \n");
            writer.write("    COUNT(*) as 孤儿节点数\n");
            writer.write("FROM t_org_structure t1\n");
            writer.write("WHERE t1.data_source = 2\n");
            writer.write("AND t1.pre_id IS NOT NULL\n");
            writer.write("AND t1.pre_id NOT IN (\n");
            writer.write("    SELECT t2.id FROM t_org_structure t2 WHERE t2.data_source = 2\n");
            writer.write(");\n");
        }
    }

    /**
     * 生成单条记录的INSERT语句
     */
    private String generateInsertLine(DepartmentSyncTest dept) {
        // 处理组织编码 - 移除X前缀，转换为数字
        String orgCodeStr = dept.getOrgCode();
        Long id = convertOrgCodeToId(orgCodeStr);
        
        // 处理组织名称 - 转义单引号
        String organName = dept.getOrgName().replace("'", "''");
        
        // 处理父级ID
        String preIdStr = convertParentCodeToPreId(dept.getParentCode());
        
        // 默认值
        int orderInfo = 0; // 默认排序
        int isDel = 0;     // 正常状态
        int dataSource = 2; // 同步数据
        
        return String.format("    (%d, '%s', %s, %d, %d, %d)", 
                id, organName, preIdStr, orderInfo, isDel, dataSource);
    }

    /**
     * 将org_code转换为数字ID
     */
    private Long convertOrgCodeToId(String orgCode) {
        if (orgCode == null || orgCode.isEmpty()) {
            throw new IllegalArgumentException("组织编码不能为空");
        }
        
        try {
            // 如果以X开头，移除X前缀
            if (orgCode.startsWith("X")) {
                String numericPart = orgCode.substring(1);
                return Long.parseLong(numericPart);
            } else {
                return Long.parseLong(orgCode);
            }
        } catch (NumberFormatException e) {
            log.warn("无法转换组织编码为数字: {}", orgCode);
            // 使用hashCode作为备用方案
            return Math.abs((long) orgCode.hashCode());
        }
    }

    /**
     * 将parent_code转换为pre_id
     */
    private String convertParentCodeToPreId(String parentCode) {
        if (parentCode == null || parentCode.isEmpty() || "1".equals(parentCode)) {
            // 根节点，pre_id为NULL
            return "NULL";
        }
        
        try {
            // 如果以X开头，移除X前缀
            if (parentCode.startsWith("X")) {
                String numericPart = parentCode.substring(1);
                return numericPart;
            } else {
                return parentCode;
            }
        } catch (Exception e) {
            log.warn("无法转换父级编码: {}", parentCode);
            return "NULL";
        }
    }

    /**
     * 获取迁移统计信息
     */
    public String getMigrationStatistics() {
        List<DepartmentSyncTest> departments = departmentSyncTestRepository.findAll().stream()
                .filter(d -> d.getIsHistory() == 0 && !"D".equals(d.getUserPredef14()))
                .collect(Collectors.toList());
        
        long totalCount = departments.size();
        long rootCount = departments.stream()
                .filter(d -> "1".equals(d.getParentCode()) || 
                           d.getParentCode() == null || 
                           d.getParentCode().isEmpty())
                .count();
        
        long xPrefixCount = departments.stream()
                .filter(d -> d.getOrgCode().startsWith("X"))
                .count();
        
        StringBuilder stats = new StringBuilder();
        stats.append("数据迁移统计信息:\n");
        stats.append("- 总记录数: ").append(totalCount).append("\n");
        stats.append("- 根节点数: ").append(rootCount).append("\n");
        stats.append("- 子节点数: ").append(totalCount - rootCount).append("\n");
        stats.append("- X前缀编码数: ").append(xPrefixCount).append("\n");
        
        return stats.toString();
    }
}
