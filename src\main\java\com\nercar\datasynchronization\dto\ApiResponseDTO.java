package com.nercar.datasynchronization.dto;

import lombok.Data;
import java.util.Date;

/**
 * 统一API响应格式
 * @param <T> 数据类型
 */
@Data
public class ApiResponseDTO<T> {
    
    /**
     * 是否成功
     */
    private boolean success;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 响应时间戳
     */
    private Date timestamp;
    
    /**
     * 数据总数（用于分页或统计）
     */
    private Integer totalCount;
    
    /**
     * 构造成功响应
     * @param data 响应数据
     * @param message 响应消息
     * @param <T> 数据类型
     * @return 成功响应对象
     */
    public static <T> ApiResponseDTO<T> success(T data, String message) {
        ApiResponseDTO<T> response = new ApiResponseDTO<>();
        response.setSuccess(true);
        response.setMessage(message);
        response.setData(data);
        response.setTimestamp(new Date());
        return response;
    }
    
    /**
     * 构造成功响应（默认消息）
     * @param data 响应数据
     * @param <T> 数据类型
     * @return 成功响应对象
     */
    public static <T> ApiResponseDTO<T> success(T data) {
        return success(data, "操作成功");
    }
    
    /**
     * 构造失败响应
     * @param message 错误消息
     * @param <T> 数据类型
     * @return 失败响应对象
     */
    public static <T> ApiResponseDTO<T> error(String message) {
        ApiResponseDTO<T> response = new ApiResponseDTO<>();
        response.setSuccess(false);
        response.setMessage(message);
        response.setTimestamp(new Date());
        return response;
    }
    
    /**
     * 设置数据总数
     * @param totalCount 总数
     * @return 当前响应对象
     */
    public ApiResponseDTO<T> withTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
        return this;
    }
}
