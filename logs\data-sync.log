2025-06-09T10:51:39.285+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : Starting DataSynchronizationApplication using Java ******** with PID 9876 (E:\fushun\data-synchronization\target\classes started by Dell in E:\fushun\data-synchronization)
2025-06-09T10:51:39.301+08:00 DEBUG 9876 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-09T10:51:39.303+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : No active profile set, falling back to 1 default profile: "default"
2025-06-09T10:51:39.503+08:00  INFO 9876 --- [data-synchronization] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-06-09T10:51:39.504+08:00  INFO 9876 --- [data-synchronization] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-06-09T10:51:43.430+08:00  INFO 9876 --- [data-synchronization] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-09T10:51:43.646+08:00  INFO 9876 --- [data-synchronization] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 158 ms. Found 12 JPA repository interfaces.
2025-06-09T10:51:45.742+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-06-09T10:51:45.791+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-09T10:51:45.792+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-06-09T10:51:45.897+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-09T10:51:45.898+08:00  INFO 9876 --- [data-synchronization] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 6391 ms
2025-06-09T10:51:46.685+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-09T10:51:46.820+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.3.1.Final
2025-06-09T10:51:46.886+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-09T10:51:47.667+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-09T10:51:47.743+08:00  INFO 9876 --- [data-synchronization] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-06-09T10:51:48.724+08:00  INFO 9876 --- [data-synchronization] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5d2f767c
2025-06-09T10:51:48.727+08:00  INFO 9876 --- [data-synchronization] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-06-09T10:51:48.860+08:00  WARN 9876 --- [data-synchronization] [restartedMain] org.hibernate.orm.deprecation            : HHH90000025: MySQL8Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-09T10:51:48.862+08:00  WARN 9876 --- [data-synchronization] [restartedMain] org.hibernate.orm.deprecation            : HHH90000026: MySQL8Dialect has been deprecated; use org.hibernate.dialect.MySQLDialect instead
2025-06-09T10:51:50.726+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-09T10:51:51.460+08:00  INFO 9876 --- [data-synchronization] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-09T10:51:52.965+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 开始预加载员工缓存...
2025-06-09T10:51:54.560+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载员工ID缓存完成，共 506 条记录
2025-06-09T10:51:56.399+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载岗位ID缓存完成，共 1517 条记录
2025-06-09T10:51:57.784+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载职称ID缓存完成，共 1329 条记录
2025-06-09T10:51:58.525+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载系统标识ID缓存完成，共 548 条记录
2025-06-09T10:52:00.744+08:00  WARN 9876 --- [data-synchronization] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-09T10:52:02.843+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.impl.StdSchedulerFactory      : Using default implementation for ThreadExecutor
2025-06-09T10:52:02.867+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.core.SchedulerSignalerImpl    : Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-06-09T10:52:02.867+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : Quartz Scheduler v.2.3.2 created.
2025-06-09T10:52:02.869+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.simpl.RAMJobStore             : RAMJobStore initialized.
2025-06-09T10:52:02.871+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-06-09T10:52:02.871+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.impl.StdSchedulerFactory      : Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-06-09T10:52:02.872+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.impl.StdSchedulerFactory      : Quartz scheduler version: 2.3.2
2025-06-09T10:52:02.872+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@5f8bcfec
2025-06-09T10:52:04.094+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-06-09T10:52:04.146+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-06-09T10:52:04.337+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path ''
2025-06-09T10:52:04.339+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.s.s.quartz.SchedulerFactoryBean        : Starting Quartz Scheduler now
2025-06-09T10:52:04.339+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-06-09T10:52:04.370+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : Started DataSynchronizationApplication in 26.28 seconds (process running for 30.325)
2025-06-09T10:52:04.373+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T10:52:04.373+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 数据同步应用启动成功！
2025-06-09T10:52:04.374+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 应用访问地址: http://*************:8080
2025-06-09T10:52:04.374+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : Swagger文档地址: http://*************:8080/swagger-ui.html
2025-06-09T10:52:04.374+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T10:52:04.374+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : SOAP服务调用信息：
2025-06-09T10:52:04.374+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 组织数据服务地址: https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery
2025-06-09T10:52:04.374+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 接口方法: GetOrgInfoFromMDM
2025-06-09T10:52:04.375+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 人员数据服务地址: https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery
2025-06-09T10:52:04.375+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 接口方法: GetUserInfoFromMDM
2025-06-09T10:52:04.375+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T10:52:04.375+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : HTTP接口调用信息：
2025-06-09T10:52:04.375+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 岗位数据服务地址: http://172.28.98.74:9000/ds/dgc/getQualityPersonnelInfo
2025-06-09T10:52:04.375+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 接口方法: getQualityPersonnelInfo
2025-06-09T10:52:04.375+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T10:52:04.375+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 定时任务配置信息：
2025-06-09T10:52:04.375+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 数据同步任务执行时间: 0 0 3 * * ? (凌晨3点执行)
2025-06-09T10:52:04.376+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 数据同步范围: 昨天00:00:00至23:59:59的数据
2025-06-09T10:52:04.376+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T10:58:43.242+08:00  INFO 9876 --- [data-synchronization] [http-nio-8080-exec-2] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-09T10:58:43.243+08:00  INFO 9876 --- [data-synchronization] [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-09T10:58:43.251+08:00  INFO 9876 --- [data-synchronization] [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet        : Completed initialization in 8 ms
2025-06-09T10:58:45.024+08:00  INFO 9876 --- [data-synchronization] [http-nio-8080-exec-10] o.springdoc.api.AbstractOpenApiResource  : Init duration for springdoc-openapi is: 726 ms
2025-06-09T11:10:36.497+08:00  WARN 9876 --- [data-synchronization] [http-nio-8080-exec-1] .w.s.m.s.DefaultHandlerExceptionResolver : Resolved [org.springframework.web.method.annotation.MethodArgumentTypeMismatchException: Failed to convert value of type 'java.lang.String' to required type 'java.util.Date'; Failed to convert from type [java.lang.String] to type [@io.swagger.v3.oas.annotations.Parameter @org.springframework.web.bind.annotation.RequestParam @org.springframework.format.annotation.DateTimeFormat java.util.Date] for value [2024-01-01%2000:00:00]]
2025-06-09T11:15:41.628+08:00  INFO 9876 --- [data-synchronization] [http-nio-8080-exec-4] c.n.d.c.DataRetrievalController          : 接收到数据获取API测试请求
2025-06-09T11:20:26.272+08:00  INFO 9876 --- [data-synchronization] [http-nio-8080-exec-6] c.n.d.c.DataRetrievalController          : 接收到数据获取API测试请求
2025-06-09T11:21:08.870+08:00  WARN 9876 --- [data-synchronization] [http-nio-8080-exec-8] .w.s.m.s.DefaultHandlerExceptionResolver : Resolved [org.springframework.web.method.annotation.MethodArgumentTypeMismatchException: Failed to convert value of type 'java.lang.String' to required type 'java.util.Date'; Failed to convert from type [java.lang.String] to type [@io.swagger.v3.oas.annotations.Parameter @org.springframework.web.bind.annotation.RequestParam @org.springframework.format.annotation.DateTimeFormat java.util.Date] for value [2024-01-01T00:00:00]]
2025-06-09T11:25:43.820+08:00  WARN 9876 --- [data-synchronization] [http-nio-8080-exec-9] .w.s.m.s.DefaultHandlerExceptionResolver : Resolved [org.springframework.web.method.annotation.MethodArgumentTypeMismatchException: Failed to convert value of type 'java.lang.String' to required type 'java.util.Date'; Failed to convert from type [java.lang.String] to type [@io.swagger.v3.oas.annotations.Parameter @org.springframework.web.bind.annotation.RequestParam @org.springframework.format.annotation.DateTimeFormat java.util.Date] for value [2024-01-01%2000:00:00]]
2025-06-09T11:28:50.482+08:00  INFO 9876 --- [data-synchronization] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-06-09T11:28:50.503+08:00  INFO 9876 --- [data-synchronization] [Thread-5] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-09T11:28:50.541+08:00  INFO 9876 --- [data-synchronization] [Thread-5] o.s.s.quartz.SchedulerFactoryBean        : Shutting down Quartz Scheduler
2025-06-09T11:28:50.541+08:00  INFO 9876 --- [data-synchronization] [Thread-5] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-06-09T11:28:50.541+08:00  INFO 9876 --- [data-synchronization] [Thread-5] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-09T11:28:50.541+08:00  INFO 9876 --- [data-synchronization] [Thread-5] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-06-09T11:28:50.547+08:00  INFO 9876 --- [data-synchronization] [Thread-5] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-09T11:28:50.562+08:00  INFO 9876 --- [data-synchronization] [Thread-5] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-06-09T11:28:50.568+08:00  INFO 9876 --- [data-synchronization] [Thread-5] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-06-09T11:28:50.833+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : Starting DataSynchronizationApplication using Java ******** with PID 9876 (E:\fushun\data-synchronization\target\classes started by Dell in E:\fushun\data-synchronization)
2025-06-09T11:28:50.833+08:00 DEBUG 9876 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-09T11:28:50.833+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : No active profile set, falling back to 1 default profile: "default"
2025-06-09T11:28:51.663+08:00  INFO 9876 --- [data-synchronization] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-09T11:28:51.715+08:00  INFO 9876 --- [data-synchronization] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 51 ms. Found 12 JPA repository interfaces.
2025-06-09T11:28:52.199+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-06-09T11:28:52.200+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-09T11:28:52.200+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-06-09T11:28:52.231+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-09T11:28:52.232+08:00  INFO 9876 --- [data-synchronization] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1395 ms
2025-06-09T11:28:52.624+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-09T11:28:52.630+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-09T11:28:52.638+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-09T11:28:52.639+08:00  INFO 9876 --- [data-synchronization] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-06-09T11:28:53.619+08:00  INFO 9876 --- [data-synchronization] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection com.mysql.cj.jdbc.ConnectionImpl@63374b5c
2025-06-09T11:28:53.620+08:00  INFO 9876 --- [data-synchronization] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-06-09T11:28:53.622+08:00  WARN 9876 --- [data-synchronization] [restartedMain] org.hibernate.orm.deprecation            : HHH90000025: MySQL8Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-09T11:28:53.623+08:00  WARN 9876 --- [data-synchronization] [restartedMain] org.hibernate.orm.deprecation            : HHH90000026: MySQL8Dialect has been deprecated; use org.hibernate.dialect.MySQLDialect instead
2025-06-09T11:28:54.042+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-09T11:28:54.760+08:00  INFO 9876 --- [data-synchronization] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-09T11:28:55.690+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 开始预加载员工缓存...
2025-06-09T11:28:56.967+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载员工ID缓存完成，共 506 条记录
2025-06-09T11:28:58.610+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载岗位ID缓存完成，共 1517 条记录
2025-06-09T11:28:59.800+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载职称ID缓存完成，共 1329 条记录
2025-06-09T11:29:00.512+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载系统标识ID缓存完成，共 548 条记录
2025-06-09T11:29:01.499+08:00  WARN 9876 --- [data-synchronization] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-09T11:29:02.516+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.impl.StdSchedulerFactory      : Using default implementation for ThreadExecutor
2025-06-09T11:29:02.518+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.core.SchedulerSignalerImpl    : Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-06-09T11:29:02.518+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : Quartz Scheduler v.2.3.2 created.
2025-06-09T11:29:02.518+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.simpl.RAMJobStore             : RAMJobStore initialized.
2025-06-09T11:29:02.519+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-06-09T11:29:02.519+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.impl.StdSchedulerFactory      : Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-06-09T11:29:02.519+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.impl.StdSchedulerFactory      : Quartz scheduler version: 2.3.2
2025-06-09T11:29:02.519+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@33be9295
2025-06-09T11:29:03.278+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-06-09T11:29:03.319+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-06-09T11:29:03.436+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path ''
2025-06-09T11:29:03.437+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.s.s.quartz.SchedulerFactoryBean        : Starting Quartz Scheduler now
2025-06-09T11:29:03.437+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-06-09T11:29:03.465+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : Started DataSynchronizationApplication in 12.687 seconds (process running for 2249.42)
2025-06-09T11:29:03.467+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:29:03.467+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 数据同步应用启动成功！
2025-06-09T11:29:03.468+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 应用访问地址: http://*************:8080
2025-06-09T11:29:03.468+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : Swagger文档地址: http://*************:8080/swagger-ui.html
2025-06-09T11:29:03.468+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:29:03.468+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : SOAP服务调用信息：
2025-06-09T11:29:03.468+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 组织数据服务地址: https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery
2025-06-09T11:29:03.469+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 接口方法: GetOrgInfoFromMDM
2025-06-09T11:29:03.469+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 人员数据服务地址: https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery
2025-06-09T11:29:03.469+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 接口方法: GetUserInfoFromMDM
2025-06-09T11:29:03.469+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:29:03.469+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : HTTP接口调用信息：
2025-06-09T11:29:03.469+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 岗位数据服务地址: http://172.28.98.74:9000/ds/dgc/getQualityPersonnelInfo
2025-06-09T11:29:03.470+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 接口方法: getQualityPersonnelInfo
2025-06-09T11:29:03.470+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:29:03.470+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 定时任务配置信息：
2025-06-09T11:29:03.470+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 数据同步任务执行时间: 0 0 3 * * ? (凌晨3点执行)
2025-06-09T11:29:03.470+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 数据同步范围: 昨天00:00:00至23:59:59的数据
2025-06-09T11:29:03.470+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:29:03.492+08:00  INFO 9876 --- [data-synchronization] [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-06-09T11:29:20.923+08:00  INFO 9876 --- [data-synchronization] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (1 addition, 0 deletions, 0 modifications)
2025-06-09T11:29:20.925+08:00  INFO 9876 --- [data-synchronization] [Thread-7] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-09T11:29:20.951+08:00  INFO 9876 --- [data-synchronization] [Thread-7] o.s.s.quartz.SchedulerFactoryBean        : Shutting down Quartz Scheduler
2025-06-09T11:29:20.951+08:00  INFO 9876 --- [data-synchronization] [Thread-7] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-06-09T11:29:20.951+08:00  INFO 9876 --- [data-synchronization] [Thread-7] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-09T11:29:20.951+08:00  INFO 9876 --- [data-synchronization] [Thread-7] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-06-09T11:29:20.953+08:00  INFO 9876 --- [data-synchronization] [Thread-7] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-09T11:29:20.955+08:00  INFO 9876 --- [data-synchronization] [Thread-7] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-06-09T11:29:20.958+08:00  INFO 9876 --- [data-synchronization] [Thread-7] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-06-09T11:29:21.100+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : Starting DataSynchronizationApplication using Java ******** with PID 9876 (E:\fushun\data-synchronization\target\classes started by Dell in E:\fushun\data-synchronization)
2025-06-09T11:29:21.100+08:00 DEBUG 9876 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-09T11:29:21.101+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : No active profile set, falling back to 1 default profile: "default"
2025-06-09T11:29:21.763+08:00  INFO 9876 --- [data-synchronization] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-09T11:29:21.834+08:00  INFO 9876 --- [data-synchronization] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 71 ms. Found 12 JPA repository interfaces.
2025-06-09T11:29:22.552+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-06-09T11:29:22.554+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-09T11:29:22.555+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-06-09T11:29:22.595+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-09T11:29:22.595+08:00  INFO 9876 --- [data-synchronization] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1490 ms
2025-06-09T11:29:23.037+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-09T11:29:23.041+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-09T11:29:23.050+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-09T11:29:23.051+08:00  INFO 9876 --- [data-synchronization] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-3 - Starting...
2025-06-09T11:29:23.945+08:00  INFO 9876 --- [data-synchronization] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-3 - Added connection com.mysql.cj.jdbc.ConnectionImpl@72ae60f7
2025-06-09T11:29:23.945+08:00  INFO 9876 --- [data-synchronization] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-3 - Start completed.
2025-06-09T11:29:23.945+08:00  WARN 9876 --- [data-synchronization] [restartedMain] org.hibernate.orm.deprecation            : HHH90000025: MySQL8Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-09T11:29:23.946+08:00  WARN 9876 --- [data-synchronization] [restartedMain] org.hibernate.orm.deprecation            : HHH90000026: MySQL8Dialect has been deprecated; use org.hibernate.dialect.MySQLDialect instead
2025-06-09T11:29:24.263+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-09T11:29:24.897+08:00  INFO 9876 --- [data-synchronization] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-09T11:29:25.790+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 开始预加载员工缓存...
2025-06-09T11:29:27.100+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载员工ID缓存完成，共 506 条记录
2025-06-09T11:29:29.122+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载岗位ID缓存完成，共 1517 条记录
2025-06-09T11:29:30.185+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载职称ID缓存完成，共 1329 条记录
2025-06-09T11:29:31.003+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载系统标识ID缓存完成，共 548 条记录
2025-06-09T11:29:32.466+08:00  WARN 9876 --- [data-synchronization] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-09T11:29:33.486+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.impl.StdSchedulerFactory      : Using default implementation for ThreadExecutor
2025-06-09T11:29:33.488+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.core.SchedulerSignalerImpl    : Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-06-09T11:29:33.488+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : Quartz Scheduler v.2.3.2 created.
2025-06-09T11:29:33.488+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.simpl.RAMJobStore             : RAMJobStore initialized.
2025-06-09T11:29:33.488+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-06-09T11:29:33.488+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.impl.StdSchedulerFactory      : Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-06-09T11:29:33.488+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.impl.StdSchedulerFactory      : Quartz scheduler version: 2.3.2
2025-06-09T11:29:33.488+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@208b3fe2
2025-06-09T11:29:34.139+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-06-09T11:29:34.164+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-06-09T11:29:34.259+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path ''
2025-06-09T11:29:34.259+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.s.s.quartz.SchedulerFactoryBean        : Starting Quartz Scheduler now
2025-06-09T11:29:34.260+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-06-09T11:29:34.290+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : Started DataSynchronizationApplication in 13.236 seconds (process running for 2280.245)
2025-06-09T11:29:34.293+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:29:34.293+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 数据同步应用启动成功！
2025-06-09T11:29:34.293+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 应用访问地址: http://*************:8080
2025-06-09T11:29:34.293+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : Swagger文档地址: http://*************:8080/swagger-ui.html
2025-06-09T11:29:34.293+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:29:34.294+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : SOAP服务调用信息：
2025-06-09T11:29:34.294+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 组织数据服务地址: https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery
2025-06-09T11:29:34.294+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 接口方法: GetOrgInfoFromMDM
2025-06-09T11:29:34.294+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 人员数据服务地址: https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery
2025-06-09T11:29:34.294+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 接口方法: GetUserInfoFromMDM
2025-06-09T11:29:34.294+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:29:34.295+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : HTTP接口调用信息：
2025-06-09T11:29:34.295+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 岗位数据服务地址: http://172.28.98.74:9000/ds/dgc/getQualityPersonnelInfo
2025-06-09T11:29:34.295+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 接口方法: getQualityPersonnelInfo
2025-06-09T11:29:34.296+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:29:34.296+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 定时任务配置信息：
2025-06-09T11:29:34.297+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 数据同步任务执行时间: 0 0 3 * * ? (凌晨3点执行)
2025-06-09T11:29:34.297+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 数据同步范围: 昨天00:00:00至23:59:59的数据
2025-06-09T11:29:34.297+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:29:34.303+08:00  INFO 9876 --- [data-synchronization] [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-06-09T11:29:39.673+08:00  INFO 9876 --- [data-synchronization] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-06-09T11:29:39.675+08:00  INFO 9876 --- [data-synchronization] [Thread-11] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-09T11:29:39.705+08:00  INFO 9876 --- [data-synchronization] [Thread-11] o.s.s.quartz.SchedulerFactoryBean        : Shutting down Quartz Scheduler
2025-06-09T11:29:39.705+08:00  INFO 9876 --- [data-synchronization] [Thread-11] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-06-09T11:29:39.706+08:00  INFO 9876 --- [data-synchronization] [Thread-11] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-09T11:29:39.706+08:00  INFO 9876 --- [data-synchronization] [Thread-11] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-06-09T11:29:39.709+08:00  INFO 9876 --- [data-synchronization] [Thread-11] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-09T11:29:39.710+08:00  INFO 9876 --- [data-synchronization] [Thread-11] com.zaxxer.hikari.HikariDataSource       : HikariPool-3 - Shutdown initiated...
2025-06-09T11:29:39.716+08:00  INFO 9876 --- [data-synchronization] [Thread-11] com.zaxxer.hikari.HikariDataSource       : HikariPool-3 - Shutdown completed.
2025-06-09T11:29:39.918+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : Starting DataSynchronizationApplication using Java ******** with PID 9876 (E:\fushun\data-synchronization\target\classes started by Dell in E:\fushun\data-synchronization)
2025-06-09T11:29:39.918+08:00 DEBUG 9876 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-09T11:29:39.919+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : No active profile set, falling back to 1 default profile: "default"
2025-06-09T11:29:41.029+08:00  INFO 9876 --- [data-synchronization] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-09T11:29:41.095+08:00  INFO 9876 --- [data-synchronization] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 65 ms. Found 12 JPA repository interfaces.
2025-06-09T11:29:41.563+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-06-09T11:29:41.564+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-09T11:29:41.564+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-06-09T11:29:41.588+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-09T11:29:41.588+08:00  INFO 9876 --- [data-synchronization] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1662 ms
2025-06-09T11:29:42.015+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-09T11:29:42.022+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-09T11:29:42.035+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-09T11:29:42.036+08:00  INFO 9876 --- [data-synchronization] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-4 - Starting...
2025-06-09T11:29:43.217+08:00  INFO 9876 --- [data-synchronization] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-4 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6365ad9d
2025-06-09T11:29:43.218+08:00  INFO 9876 --- [data-synchronization] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-4 - Start completed.
2025-06-09T11:29:43.218+08:00  WARN 9876 --- [data-synchronization] [restartedMain] org.hibernate.orm.deprecation            : HHH90000025: MySQL8Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-09T11:29:43.218+08:00  WARN 9876 --- [data-synchronization] [restartedMain] org.hibernate.orm.deprecation            : HHH90000026: MySQL8Dialect has been deprecated; use org.hibernate.dialect.MySQLDialect instead
2025-06-09T11:29:43.451+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-09T11:29:44.016+08:00  INFO 9876 --- [data-synchronization] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-09T11:29:44.880+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 开始预加载员工缓存...
2025-06-09T11:29:46.245+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载员工ID缓存完成，共 506 条记录
2025-06-09T11:29:47.760+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载岗位ID缓存完成，共 1517 条记录
2025-06-09T11:29:48.799+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载职称ID缓存完成，共 1329 条记录
2025-06-09T11:29:49.487+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载系统标识ID缓存完成，共 548 条记录
2025-06-09T11:29:50.289+08:00  WARN 9876 --- [data-synchronization] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-09T11:29:51.339+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.impl.StdSchedulerFactory      : Using default implementation for ThreadExecutor
2025-06-09T11:29:51.341+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.core.SchedulerSignalerImpl    : Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-06-09T11:29:51.341+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : Quartz Scheduler v.2.3.2 created.
2025-06-09T11:29:51.341+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.simpl.RAMJobStore             : RAMJobStore initialized.
2025-06-09T11:29:51.342+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-06-09T11:29:51.342+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.impl.StdSchedulerFactory      : Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-06-09T11:29:51.342+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.impl.StdSchedulerFactory      : Quartz scheduler version: 2.3.2
2025-06-09T11:29:51.342+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@3d810663
2025-06-09T11:29:52.124+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-06-09T11:29:52.151+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-06-09T11:29:52.241+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path ''
2025-06-09T11:29:52.242+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.s.s.quartz.SchedulerFactoryBean        : Starting Quartz Scheduler now
2025-06-09T11:29:52.245+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-06-09T11:29:52.293+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : Started DataSynchronizationApplication in 12.438 seconds (process running for 2298.248)
2025-06-09T11:29:52.296+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:29:52.296+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 数据同步应用启动成功！
2025-06-09T11:29:52.297+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 应用访问地址: http://*************:8080
2025-06-09T11:29:52.297+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : Swagger文档地址: http://*************:8080/swagger-ui.html
2025-06-09T11:29:52.297+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:29:52.297+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : SOAP服务调用信息：
2025-06-09T11:29:52.297+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 组织数据服务地址: https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery
2025-06-09T11:29:52.297+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 接口方法: GetOrgInfoFromMDM
2025-06-09T11:29:52.297+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 人员数据服务地址: https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery
2025-06-09T11:29:52.297+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 接口方法: GetUserInfoFromMDM
2025-06-09T11:29:52.297+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:29:52.297+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : HTTP接口调用信息：
2025-06-09T11:29:52.297+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 岗位数据服务地址: http://172.28.98.74:9000/ds/dgc/getQualityPersonnelInfo
2025-06-09T11:29:52.298+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 接口方法: getQualityPersonnelInfo
2025-06-09T11:29:52.298+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:29:52.298+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 定时任务配置信息：
2025-06-09T11:29:52.298+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 数据同步任务执行时间: 0 0 3 * * ? (凌晨3点执行)
2025-06-09T11:29:52.298+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 数据同步范围: 昨天00:00:00至23:59:59的数据
2025-06-09T11:29:52.298+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:29:52.302+08:00  INFO 9876 --- [data-synchronization] [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-06-09T11:29:58.748+08:00  INFO 9876 --- [data-synchronization] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-06-09T11:29:58.750+08:00  INFO 9876 --- [data-synchronization] [Thread-15] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-09T11:29:58.778+08:00  INFO 9876 --- [data-synchronization] [Thread-15] o.s.s.quartz.SchedulerFactoryBean        : Shutting down Quartz Scheduler
2025-06-09T11:29:58.778+08:00  INFO 9876 --- [data-synchronization] [Thread-15] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-06-09T11:29:58.778+08:00  INFO 9876 --- [data-synchronization] [Thread-15] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-09T11:29:58.778+08:00  INFO 9876 --- [data-synchronization] [Thread-15] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-06-09T11:29:58.780+08:00  INFO 9876 --- [data-synchronization] [Thread-15] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-09T11:29:58.782+08:00  INFO 9876 --- [data-synchronization] [Thread-15] com.zaxxer.hikari.HikariDataSource       : HikariPool-4 - Shutdown initiated...
2025-06-09T11:29:58.784+08:00  INFO 9876 --- [data-synchronization] [Thread-15] com.zaxxer.hikari.HikariDataSource       : HikariPool-4 - Shutdown completed.
2025-06-09T11:29:58.950+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : Starting DataSynchronizationApplication using Java ******** with PID 9876 (E:\fushun\data-synchronization\target\classes started by Dell in E:\fushun\data-synchronization)
2025-06-09T11:29:58.950+08:00 DEBUG 9876 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-09T11:29:58.950+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : No active profile set, falling back to 1 default profile: "default"
2025-06-09T11:29:59.797+08:00  INFO 9876 --- [data-synchronization] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-09T11:29:59.845+08:00  INFO 9876 --- [data-synchronization] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 47 ms. Found 12 JPA repository interfaces.
2025-06-09T11:30:00.286+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-06-09T11:30:00.287+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-09T11:30:00.287+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-06-09T11:30:00.319+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-09T11:30:00.320+08:00  INFO 9876 --- [data-synchronization] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1368 ms
2025-06-09T11:30:00.853+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-09T11:30:00.859+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-09T11:30:00.868+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-09T11:30:00.869+08:00  INFO 9876 --- [data-synchronization] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-5 - Starting...
2025-06-09T11:30:01.654+08:00  INFO 9876 --- [data-synchronization] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-5 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1bd09e89
2025-06-09T11:30:01.655+08:00  INFO 9876 --- [data-synchronization] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-5 - Start completed.
2025-06-09T11:30:01.656+08:00  WARN 9876 --- [data-synchronization] [restartedMain] org.hibernate.orm.deprecation            : HHH90000025: MySQL8Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-09T11:30:01.656+08:00  WARN 9876 --- [data-synchronization] [restartedMain] org.hibernate.orm.deprecation            : HHH90000026: MySQL8Dialect has been deprecated; use org.hibernate.dialect.MySQLDialect instead
2025-06-09T11:30:01.928+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-09T11:30:02.462+08:00  INFO 9876 --- [data-synchronization] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-09T11:30:03.542+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 开始预加载员工缓存...
2025-06-09T11:30:04.610+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载员工ID缓存完成，共 506 条记录
2025-06-09T11:30:06.040+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载岗位ID缓存完成，共 1517 条记录
2025-06-09T11:30:07.109+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载职称ID缓存完成，共 1329 条记录
2025-06-09T11:30:07.816+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载系统标识ID缓存完成，共 548 条记录
2025-06-09T11:30:08.748+08:00  WARN 9876 --- [data-synchronization] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-09T11:30:09.697+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.impl.StdSchedulerFactory      : Using default implementation for ThreadExecutor
2025-06-09T11:30:09.698+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.core.SchedulerSignalerImpl    : Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-06-09T11:30:09.698+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : Quartz Scheduler v.2.3.2 created.
2025-06-09T11:30:09.698+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.simpl.RAMJobStore             : RAMJobStore initialized.
2025-06-09T11:30:09.698+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-06-09T11:30:09.698+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.impl.StdSchedulerFactory      : Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-06-09T11:30:09.698+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.impl.StdSchedulerFactory      : Quartz scheduler version: 2.3.2
2025-06-09T11:30:09.699+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7878d907
2025-06-09T11:30:10.499+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-06-09T11:30:10.556+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-06-09T11:30:10.698+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path ''
2025-06-09T11:30:10.698+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.s.s.quartz.SchedulerFactoryBean        : Starting Quartz Scheduler now
2025-06-09T11:30:10.698+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-06-09T11:30:10.760+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : Started DataSynchronizationApplication in 11.852 seconds (process running for 2316.714)
2025-06-09T11:30:10.763+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:30:10.764+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 数据同步应用启动成功！
2025-06-09T11:30:10.764+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 应用访问地址: http://*************:8080
2025-06-09T11:30:10.764+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : Swagger文档地址: http://*************:8080/swagger-ui.html
2025-06-09T11:30:10.764+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:30:10.764+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : SOAP服务调用信息：
2025-06-09T11:30:10.764+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 组织数据服务地址: https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery
2025-06-09T11:30:10.764+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 接口方法: GetOrgInfoFromMDM
2025-06-09T11:30:10.764+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 人员数据服务地址: https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery
2025-06-09T11:30:10.764+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 接口方法: GetUserInfoFromMDM
2025-06-09T11:30:10.764+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:30:10.764+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : HTTP接口调用信息：
2025-06-09T11:30:10.764+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 岗位数据服务地址: http://172.28.98.74:9000/ds/dgc/getQualityPersonnelInfo
2025-06-09T11:30:10.764+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 接口方法: getQualityPersonnelInfo
2025-06-09T11:30:10.764+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:30:10.765+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 定时任务配置信息：
2025-06-09T11:30:10.765+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 数据同步任务执行时间: 0 0 3 * * ? (凌晨3点执行)
2025-06-09T11:30:10.765+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 数据同步范围: 昨天00:00:00至23:59:59的数据
2025-06-09T11:30:10.765+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:30:10.773+08:00  INFO 9876 --- [data-synchronization] [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-06-09T11:30:37.598+08:00  INFO 9876 --- [data-synchronization] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-06-09T11:30:37.602+08:00  INFO 9876 --- [data-synchronization] [Thread-19] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-09T11:30:37.637+08:00  INFO 9876 --- [data-synchronization] [Thread-19] o.s.s.quartz.SchedulerFactoryBean        : Shutting down Quartz Scheduler
2025-06-09T11:30:37.637+08:00  INFO 9876 --- [data-synchronization] [Thread-19] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-06-09T11:30:37.637+08:00  INFO 9876 --- [data-synchronization] [Thread-19] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-09T11:30:37.637+08:00  INFO 9876 --- [data-synchronization] [Thread-19] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-06-09T11:30:37.639+08:00  INFO 9876 --- [data-synchronization] [Thread-19] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-09T11:30:37.640+08:00  INFO 9876 --- [data-synchronization] [Thread-19] com.zaxxer.hikari.HikariDataSource       : HikariPool-5 - Shutdown initiated...
2025-06-09T11:30:37.650+08:00  INFO 9876 --- [data-synchronization] [Thread-19] com.zaxxer.hikari.HikariDataSource       : HikariPool-5 - Shutdown completed.
2025-06-09T11:30:37.955+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : Starting DataSynchronizationApplication using Java ******** with PID 9876 (E:\fushun\data-synchronization\target\classes started by Dell in E:\fushun\data-synchronization)
2025-06-09T11:30:37.956+08:00 DEBUG 9876 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-09T11:30:37.956+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : No active profile set, falling back to 1 default profile: "default"
2025-06-09T11:30:38.728+08:00  INFO 9876 --- [data-synchronization] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-09T11:30:38.784+08:00  INFO 9876 --- [data-synchronization] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 54 ms. Found 12 JPA repository interfaces.
2025-06-09T11:30:39.135+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-06-09T11:30:39.136+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-09T11:30:39.136+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-06-09T11:30:39.163+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-09T11:30:39.164+08:00  INFO 9876 --- [data-synchronization] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1203 ms
2025-06-09T11:30:39.555+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-09T11:30:39.559+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-09T11:30:39.565+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-09T11:30:39.566+08:00  INFO 9876 --- [data-synchronization] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-6 - Starting...
2025-06-09T11:30:40.681+08:00  INFO 9876 --- [data-synchronization] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-6 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6f17ceac
2025-06-09T11:30:40.681+08:00  INFO 9876 --- [data-synchronization] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-6 - Start completed.
2025-06-09T11:30:40.682+08:00  WARN 9876 --- [data-synchronization] [restartedMain] org.hibernate.orm.deprecation            : HHH90000025: MySQL8Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-09T11:30:40.682+08:00  WARN 9876 --- [data-synchronization] [restartedMain] org.hibernate.orm.deprecation            : HHH90000026: MySQL8Dialect has been deprecated; use org.hibernate.dialect.MySQLDialect instead
2025-06-09T11:30:41.617+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-09T11:30:42.240+08:00  INFO 9876 --- [data-synchronization] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-09T11:30:42.830+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 开始预加载员工缓存...
2025-06-09T11:30:44.051+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载员工ID缓存完成，共 506 条记录
2025-06-09T11:30:45.695+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载岗位ID缓存完成，共 1517 条记录
2025-06-09T11:30:46.775+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载职称ID缓存完成，共 1329 条记录
2025-06-09T11:30:47.517+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载系统标识ID缓存完成，共 548 条记录
2025-06-09T11:30:48.278+08:00  WARN 9876 --- [data-synchronization] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-09T11:30:49.431+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.impl.StdSchedulerFactory      : Using default implementation for ThreadExecutor
2025-06-09T11:30:49.433+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.core.SchedulerSignalerImpl    : Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-06-09T11:30:49.433+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : Quartz Scheduler v.2.3.2 created.
2025-06-09T11:30:49.433+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.simpl.RAMJobStore             : RAMJobStore initialized.
2025-06-09T11:30:49.433+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-06-09T11:30:49.434+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.impl.StdSchedulerFactory      : Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-06-09T11:30:49.434+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.impl.StdSchedulerFactory      : Quartz scheduler version: 2.3.2
2025-06-09T11:30:49.434+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@3bf57564
2025-06-09T11:30:50.140+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-06-09T11:30:50.161+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-06-09T11:30:50.224+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path ''
2025-06-09T11:30:50.225+08:00  INFO 9876 --- [data-synchronization] [restartedMain] o.s.s.quartz.SchedulerFactoryBean        : Starting Quartz Scheduler now
2025-06-09T11:30:50.225+08:00  INFO 9876 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-06-09T11:30:50.242+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : Started DataSynchronizationApplication in 12.352 seconds (process running for 2356.197)
2025-06-09T11:30:50.245+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:30:50.245+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 数据同步应用启动成功！
2025-06-09T11:30:50.245+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 应用访问地址: http://*************:8080
2025-06-09T11:30:50.245+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : Swagger文档地址: http://*************:8080/swagger-ui.html
2025-06-09T11:30:50.245+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:30:50.245+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : SOAP服务调用信息：
2025-06-09T11:30:50.245+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 组织数据服务地址: https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery
2025-06-09T11:30:50.245+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 接口方法: GetOrgInfoFromMDM
2025-06-09T11:30:50.245+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 人员数据服务地址: https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery
2025-06-09T11:30:50.245+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 接口方法: GetUserInfoFromMDM
2025-06-09T11:30:50.245+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:30:50.245+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : HTTP接口调用信息：
2025-06-09T11:30:50.245+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 岗位数据服务地址: http://172.28.98.74:9000/ds/dgc/getQualityPersonnelInfo
2025-06-09T11:30:50.245+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 接口方法: getQualityPersonnelInfo
2025-06-09T11:30:50.245+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:30:50.245+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 定时任务配置信息：
2025-06-09T11:30:50.245+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 数据同步任务执行时间: 0 0 3 * * ? (凌晨3点执行)
2025-06-09T11:30:50.245+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 数据同步范围: 昨天00:00:00至23:59:59的数据
2025-06-09T11:30:50.245+08:00  INFO 9876 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:30:50.249+08:00  INFO 9876 --- [data-synchronization] [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-06-09T11:32:23.835+08:00  INFO 9876 --- [data-synchronization] [SpringApplicationShutdownHook] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-09T11:32:23.871+08:00  INFO 9876 --- [data-synchronization] [SpringApplicationShutdownHook] o.s.s.quartz.SchedulerFactoryBean        : Shutting down Quartz Scheduler
2025-06-09T11:32:23.871+08:00  INFO 9876 --- [data-synchronization] [SpringApplicationShutdownHook] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-06-09T11:32:23.871+08:00  INFO 9876 --- [data-synchronization] [SpringApplicationShutdownHook] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-09T11:32:23.872+08:00  INFO 9876 --- [data-synchronization] [SpringApplicationShutdownHook] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-06-09T11:32:23.873+08:00  INFO 9876 --- [data-synchronization] [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-09T11:32:23.874+08:00  INFO 9876 --- [data-synchronization] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-6 - Shutdown initiated...
2025-06-09T11:32:23.878+08:00  INFO 9876 --- [data-synchronization] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-6 - Shutdown completed.
2025-06-09T11:32:34.126+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : Starting DataSynchronizationApplication using Java ******** with PID 4964 (E:\fushun\data-synchronization\target\classes started by Dell in E:\fushun\data-synchronization)
2025-06-09T11:32:34.129+08:00 DEBUG 4964 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-09T11:32:34.131+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : No active profile set, falling back to 1 default profile: "default"
2025-06-09T11:32:34.205+08:00  INFO 4964 --- [data-synchronization] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-06-09T11:32:34.206+08:00  INFO 4964 --- [data-synchronization] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-06-09T11:32:36.081+08:00  INFO 4964 --- [data-synchronization] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-09T11:32:36.192+08:00  INFO 4964 --- [data-synchronization] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 97 ms. Found 12 JPA repository interfaces.
2025-06-09T11:32:37.595+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-06-09T11:32:37.611+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-09T11:32:37.612+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-06-09T11:32:37.713+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-09T11:32:37.714+08:00  INFO 4964 --- [data-synchronization] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 3506 ms
2025-06-09T11:32:38.364+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-09T11:32:38.439+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.3.1.Final
2025-06-09T11:32:38.492+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-09T11:32:38.831+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-09T11:32:38.866+08:00  INFO 4964 --- [data-synchronization] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-06-09T11:32:40.087+08:00  INFO 4964 --- [data-synchronization] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4b0e0e85
2025-06-09T11:32:40.092+08:00  INFO 4964 --- [data-synchronization] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-06-09T11:32:40.216+08:00  WARN 4964 --- [data-synchronization] [restartedMain] org.hibernate.orm.deprecation            : HHH90000025: MySQL8Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-09T11:32:40.217+08:00  WARN 4964 --- [data-synchronization] [restartedMain] org.hibernate.orm.deprecation            : HHH90000026: MySQL8Dialect has been deprecated; use org.hibernate.dialect.MySQLDialect instead
2025-06-09T11:32:42.026+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-09T11:32:42.727+08:00  INFO 4964 --- [data-synchronization] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-09T11:32:43.707+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 开始预加载员工缓存...
2025-06-09T11:32:45.198+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载员工ID缓存完成，共 506 条记录
2025-06-09T11:32:46.549+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载岗位ID缓存完成，共 1517 条记录
2025-06-09T11:32:47.787+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载职称ID缓存完成，共 1329 条记录
2025-06-09T11:32:48.507+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载系统标识ID缓存完成，共 548 条记录
2025-06-09T11:32:49.972+08:00  WARN 4964 --- [data-synchronization] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-09T11:32:51.208+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.impl.StdSchedulerFactory      : Using default implementation for ThreadExecutor
2025-06-09T11:32:51.230+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.core.SchedulerSignalerImpl    : Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-06-09T11:32:51.230+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : Quartz Scheduler v.2.3.2 created.
2025-06-09T11:32:51.232+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.simpl.RAMJobStore             : RAMJobStore initialized.
2025-06-09T11:32:51.233+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-06-09T11:32:51.234+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.impl.StdSchedulerFactory      : Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-06-09T11:32:51.234+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.impl.StdSchedulerFactory      : Quartz scheduler version: 2.3.2
2025-06-09T11:32:51.234+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@268946c7
2025-06-09T11:32:52.084+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-06-09T11:32:52.098+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-06-09T11:32:52.213+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path ''
2025-06-09T11:32:52.216+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.s.s.quartz.SchedulerFactoryBean        : Starting Quartz Scheduler now
2025-06-09T11:32:52.216+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-06-09T11:32:52.242+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : Started DataSynchronizationApplication in 18.943 seconds (process running for 20.796)
2025-06-09T11:32:52.245+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:32:52.245+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 数据同步应用启动成功！
2025-06-09T11:32:52.245+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 应用访问地址: http://*************:8080
2025-06-09T11:32:52.246+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : Swagger文档地址: http://*************:8080/swagger-ui.html
2025-06-09T11:32:52.247+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:32:52.247+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : SOAP服务调用信息：
2025-06-09T11:32:52.247+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 组织数据服务地址: https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery
2025-06-09T11:32:52.247+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 接口方法: GetOrgInfoFromMDM
2025-06-09T11:32:52.248+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 人员数据服务地址: https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery
2025-06-09T11:32:52.248+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 接口方法: GetUserInfoFromMDM
2025-06-09T11:32:52.248+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:32:52.249+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : HTTP接口调用信息：
2025-06-09T11:32:52.249+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 岗位数据服务地址: http://172.28.98.74:9000/ds/dgc/getQualityPersonnelInfo
2025-06-09T11:32:52.249+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 接口方法: getQualityPersonnelInfo
2025-06-09T11:32:52.249+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:32:52.249+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 定时任务配置信息：
2025-06-09T11:32:52.250+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 数据同步任务执行时间: 0 0 3 * * ? (凌晨3点执行)
2025-06-09T11:32:52.250+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 数据同步范围: 昨天00:00:00至23:59:59的数据
2025-06-09T11:32:52.250+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:32:53.168+08:00  INFO 4964 --- [data-synchronization] [RMI TCP Connection(31)-*************] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-09T11:32:53.169+08:00  INFO 4964 --- [data-synchronization] [RMI TCP Connection(31)-*************] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-09T11:32:53.178+08:00  INFO 4964 --- [data-synchronization] [RMI TCP Connection(31)-*************] o.s.web.servlet.DispatcherServlet        : Completed initialization in 7 ms
2025-06-09T11:33:33.119+08:00  INFO 4964 --- [data-synchronization] [http-nio-8080-exec-3] o.springdoc.api.AbstractOpenApiResource  : Init duration for springdoc-openapi is: 840 ms
2025-06-09T11:33:41.701+08:00  INFO 4964 --- [data-synchronization] [http-nio-8080-exec-5] c.n.d.c.DataRetrievalController          : 接收到数据获取API测试请求
2025-06-09T11:34:25.393+08:00  INFO 4964 --- [data-synchronization] [http-nio-8080-exec-6] c.n.d.c.DataRetrievalController          : 接收到获取部门数据请求，开始时间: 2024-01-01%2000:00:00, 结束时间: 2024-01-01%2023:59:59
2025-06-09T11:34:25.395+08:00 DEBUG 4964 --- [data-synchronization] [http-nio-8080-exec-6] c.n.datasynchronization.utils.DateUtils  : 尝试解析日期字符串: 原始=2024-01-01%2000:00:00, 解码后=2024-01-01 00:00:00
2025-06-09T11:34:25.396+08:00 DEBUG 4964 --- [data-synchronization] [http-nio-8080-exec-6] c.n.datasynchronization.utils.DateUtils  : 成功使用格式 yyyy-MM-dd HH:mm:ss 解析日期: Mon Jan 01 00:00:00 CST 2024
2025-06-09T11:34:25.396+08:00 DEBUG 4964 --- [data-synchronization] [http-nio-8080-exec-6] c.n.datasynchronization.utils.DateUtils  : 尝试解析日期字符串: 原始=2024-01-01%2023:59:59, 解码后=2024-01-01 23:59:59
2025-06-09T11:34:25.397+08:00 DEBUG 4964 --- [data-synchronization] [http-nio-8080-exec-6] c.n.datasynchronization.utils.DateUtils  : 成功使用格式 yyyy-MM-dd HH:mm:ss 解析日期: Mon Jan 01 23:59:59 CST 2024
2025-06-09T11:34:25.397+08:00  INFO 4964 --- [data-synchronization] [http-nio-8080-exec-6] c.n.d.c.DataRetrievalController          : 解析后的日期 - 开始时间: Mon Jan 01 00:00:00 CST 2024, 结束时间: Mon Jan 01 23:59:59 CST 2024
2025-06-09T11:34:25.405+08:00  INFO 4964 --- [data-synchronization] [http-nio-8080-exec-6] c.n.d.s.impl.DataRetrievalServiceImpl    : 开始获取部门数据，开始时间: Mon Jan 01 00:00:00 CST 2024, 结束时间: Mon Jan 01 23:59:59 CST 2024
2025-06-09T11:34:25.406+08:00 DEBUG 4964 --- [data-synchronization] [http-nio-8080-exec-6] c.n.d.client.impl.SoapClientImpl         : 请求部门数据，参数：开始时间=2024/01/01 00:00:00，结束时间=2024/01/01 23:59:59
2025-06-09T11:34:25.599+08:00 DEBUG 4964 --- [data-synchronization] [http-nio-8080-exec-6] c.n.d.client.impl.SoapClientImpl         : 发送SOAP请求到 https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery, SOAPAction: http://tempuri.org/GetOrgInfoFromMDM
2025-06-09T11:34:26.594+08:00 DEBUG 4964 --- [data-synchronization] [http-nio-8080-exec-6] c.n.d.client.impl.SoapClientImpl         : SOAP请求响应码: 200
2025-06-09T11:34:26.597+08:00 DEBUG 4964 --- [data-synchronization] [http-nio-8080-exec-6] c.n.d.client.impl.SoapClientImpl         : SOAP请求响应: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/env...
2025-06-09T11:34:26.602+08:00 DEBUG 4964 --- [data-synchronization] [http-nio-8080-exec-6] c.n.datasynchronization.utils.XmlUtils   : 开始提取XML数据，SOAP响应(前200字符): <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLS...
2025-06-09T11:34:26.671+08:00 DEBUG 4964 --- [data-synchronization] [http-nio-8080-exec-6] c.n.datasynchronization.utils.XmlUtils   : SOAP命名空间: http://schemas.xmlsoap.org/soap/envelope/
2025-06-09T11:34:26.671+08:00 DEBUG 4964 --- [data-synchronization] [http-nio-8080-exec-6] c.n.datasynchronization.utils.XmlUtils   : 成功获取SOAP Body元素
2025-06-09T11:34:26.674+08:00 DEBUG 4964 --- [data-synchronization] [http-nio-8080-exec-6] c.n.datasynchronization.utils.XmlUtils   : 响应元素名称: GetOrgInfoFromMDMResponse
2025-06-09T11:34:26.674+08:00 DEBUG 4964 --- [data-synchronization] [http-nio-8080-exec-6] c.n.datasynchronization.utils.XmlUtils   : 成功提取XML数据(前100字符): <?xml version="1.0" encoding="utf-16"?><O_DATAS />
2025-06-09T11:34:26.678+08:00  INFO 4964 --- [data-synchronization] [http-nio-8080-exec-6] c.n.datasynchronization.utils.XmlUtils   : XML数据解析完成，共处理0条记录
2025-06-09T11:34:26.679+08:00  INFO 4964 --- [data-synchronization] [http-nio-8080-exec-6] c.n.d.s.impl.DataRetrievalServiceImpl    : 成功获取部门数据，共0条记录
2025-06-09T11:34:26.679+08:00  INFO 4964 --- [data-synchronization] [http-nio-8080-exec-6] c.n.d.c.PerformanceMonitoringAspect      : com.nercar.datasynchronization.service.impl.DataRetrievalServiceImpl#getDepartmentData 执行完成，耗时: 1276ms
2025-06-09T11:34:26.680+08:00  INFO 4964 --- [data-synchronization] [http-nio-8080-exec-6] c.n.d.c.DataRetrievalController          : 成功返回部门数据，共0条记录
2025-06-09T11:34:55.006+08:00  INFO 4964 --- [data-synchronization] [http-nio-8080-exec-9] c.n.d.c.DataRetrievalController          : 接收到获取部门数据请求，开始时间: 2024-01-01%2000:00:00, 结束时间: 2024-01-10%2023:59:59
2025-06-09T11:34:55.007+08:00 DEBUG 4964 --- [data-synchronization] [http-nio-8080-exec-9] c.n.datasynchronization.utils.DateUtils  : 尝试解析日期字符串: 原始=2024-01-01%2000:00:00, 解码后=2024-01-01 00:00:00
2025-06-09T11:34:55.007+08:00 DEBUG 4964 --- [data-synchronization] [http-nio-8080-exec-9] c.n.datasynchronization.utils.DateUtils  : 成功使用格式 yyyy-MM-dd HH:mm:ss 解析日期: Mon Jan 01 00:00:00 CST 2024
2025-06-09T11:34:55.007+08:00 DEBUG 4964 --- [data-synchronization] [http-nio-8080-exec-9] c.n.datasynchronization.utils.DateUtils  : 尝试解析日期字符串: 原始=2024-01-10%2023:59:59, 解码后=2024-01-10 23:59:59
2025-06-09T11:34:55.008+08:00 DEBUG 4964 --- [data-synchronization] [http-nio-8080-exec-9] c.n.datasynchronization.utils.DateUtils  : 成功使用格式 yyyy-MM-dd HH:mm:ss 解析日期: Wed Jan 10 23:59:59 CST 2024
2025-06-09T11:34:55.008+08:00  INFO 4964 --- [data-synchronization] [http-nio-8080-exec-9] c.n.d.c.DataRetrievalController          : 解析后的日期 - 开始时间: Mon Jan 01 00:00:00 CST 2024, 结束时间: Wed Jan 10 23:59:59 CST 2024
2025-06-09T11:34:55.008+08:00  INFO 4964 --- [data-synchronization] [http-nio-8080-exec-9] c.n.d.s.impl.DataRetrievalServiceImpl    : 开始获取部门数据，开始时间: Mon Jan 01 00:00:00 CST 2024, 结束时间: Wed Jan 10 23:59:59 CST 2024
2025-06-09T11:34:55.008+08:00 DEBUG 4964 --- [data-synchronization] [http-nio-8080-exec-9] c.n.d.client.impl.SoapClientImpl         : 请求部门数据，参数：开始时间=2024/01/01 00:00:00，结束时间=2024/01/10 23:59:59
2025-06-09T11:34:55.009+08:00 DEBUG 4964 --- [data-synchronization] [http-nio-8080-exec-9] c.n.d.client.impl.SoapClientImpl         : 发送SOAP请求到 https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery, SOAPAction: http://tempuri.org/GetOrgInfoFromMDM
2025-06-09T11:34:55.438+08:00 DEBUG 4964 --- [data-synchronization] [http-nio-8080-exec-9] c.n.d.client.impl.SoapClientImpl         : SOAP请求响应码: 200
2025-06-09T11:34:55.672+08:00 DEBUG 4964 --- [data-synchronization] [http-nio-8080-exec-9] c.n.d.client.impl.SoapClientImpl         : SOAP请求响应: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/env...
2025-06-09T11:34:55.673+08:00 DEBUG 4964 --- [data-synchronization] [http-nio-8080-exec-9] c.n.datasynchronization.utils.XmlUtils   : 开始提取XML数据，SOAP响应(前200字符): <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLS...
2025-06-09T11:34:55.695+08:00 DEBUG 4964 --- [data-synchronization] [http-nio-8080-exec-9] c.n.datasynchronization.utils.XmlUtils   : SOAP命名空间: http://schemas.xmlsoap.org/soap/envelope/
2025-06-09T11:34:55.695+08:00 DEBUG 4964 --- [data-synchronization] [http-nio-8080-exec-9] c.n.datasynchronization.utils.XmlUtils   : 成功获取SOAP Body元素
2025-06-09T11:34:55.695+08:00 DEBUG 4964 --- [data-synchronization] [http-nio-8080-exec-9] c.n.datasynchronization.utils.XmlUtils   : 响应元素名称: GetOrgInfoFromMDMResponse
2025-06-09T11:34:55.696+08:00 DEBUG 4964 --- [data-synchronization] [http-nio-8080-exec-9] c.n.datasynchronization.utils.XmlUtils   : 成功提取XML数据(前100字符): <?xml version="1.0" encoding="utf-16"?><O_DATAS>  <O_DATA>    <NM>07a453ec-8c46-41b5-9282-c5f83ebbae...
2025-06-09T11:34:55.710+08:00  INFO 4964 --- [data-synchronization] [http-nio-8080-exec-9] c.n.datasynchronization.utils.XmlUtils   : XML数据解析完成，共处理21条记录
2025-06-09T11:34:55.710+08:00  INFO 4964 --- [data-synchronization] [http-nio-8080-exec-9] c.n.d.s.impl.DataRetrievalServiceImpl    : 成功获取部门数据，共21条记录
2025-06-09T11:34:55.710+08:00  INFO 4964 --- [data-synchronization] [http-nio-8080-exec-9] c.n.d.c.PerformanceMonitoringAspect      : com.nercar.datasynchronization.service.impl.DataRetrievalServiceImpl#getDepartmentData 执行完成，耗时: 702ms
2025-06-09T11:34:55.711+08:00  INFO 4964 --- [data-synchronization] [http-nio-8080-exec-9] c.n.d.c.DataRetrievalController          : 成功返回部门数据，共21条记录
2025-06-09T11:40:13.137+08:00  INFO 4964 --- [data-synchronization] [http-nio-8080-exec-1] c.n.d.c.DataRetrievalController          : 接收到获取部门数据请求，开始时间: 2024-01-01%2000:00:00, 结束时间: 2024-01-10%2023:59:59
2025-06-09T11:40:13.138+08:00 DEBUG 4964 --- [data-synchronization] [http-nio-8080-exec-1] c.n.datasynchronization.utils.DateUtils  : 尝试解析日期字符串: 原始=2024-01-01%2000:00:00, 解码后=2024-01-01 00:00:00
2025-06-09T11:40:13.138+08:00 DEBUG 4964 --- [data-synchronization] [http-nio-8080-exec-1] c.n.datasynchronization.utils.DateUtils  : 成功使用格式 yyyy-MM-dd HH:mm:ss 解析日期: Mon Jan 01 00:00:00 CST 2024
2025-06-09T11:40:13.139+08:00 DEBUG 4964 --- [data-synchronization] [http-nio-8080-exec-1] c.n.datasynchronization.utils.DateUtils  : 尝试解析日期字符串: 原始=2024-01-10%2023:59:59, 解码后=2024-01-10 23:59:59
2025-06-09T11:40:13.139+08:00 DEBUG 4964 --- [data-synchronization] [http-nio-8080-exec-1] c.n.datasynchronization.utils.DateUtils  : 成功使用格式 yyyy-MM-dd HH:mm:ss 解析日期: Wed Jan 10 23:59:59 CST 2024
2025-06-09T11:40:13.139+08:00  INFO 4964 --- [data-synchronization] [http-nio-8080-exec-1] c.n.d.c.DataRetrievalController          : 解析后的日期 - 开始时间: Mon Jan 01 00:00:00 CST 2024, 结束时间: Wed Jan 10 23:59:59 CST 2024
2025-06-09T11:40:13.139+08:00  INFO 4964 --- [data-synchronization] [http-nio-8080-exec-1] c.n.d.s.impl.DataRetrievalServiceImpl    : 开始获取部门数据，开始时间: Mon Jan 01 00:00:00 CST 2024, 结束时间: Wed Jan 10 23:59:59 CST 2024
2025-06-09T11:40:13.141+08:00 DEBUG 4964 --- [data-synchronization] [http-nio-8080-exec-1] c.n.d.client.impl.SoapClientImpl         : 请求部门数据，参数：开始时间=2024/01/01 00:00:00，结束时间=2024/01/10 23:59:59
2025-06-09T11:40:13.141+08:00 DEBUG 4964 --- [data-synchronization] [http-nio-8080-exec-1] c.n.d.client.impl.SoapClientImpl         : 发送SOAP请求到 https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery, SOAPAction: http://tempuri.org/GetOrgInfoFromMDM
2025-06-09T11:40:13.839+08:00 DEBUG 4964 --- [data-synchronization] [http-nio-8080-exec-1] c.n.d.client.impl.SoapClientImpl         : SOAP请求响应码: 200
2025-06-09T11:40:14.067+08:00 DEBUG 4964 --- [data-synchronization] [http-nio-8080-exec-1] c.n.d.client.impl.SoapClientImpl         : SOAP请求响应: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/env...
2025-06-09T11:40:14.068+08:00 DEBUG 4964 --- [data-synchronization] [http-nio-8080-exec-1] c.n.datasynchronization.utils.XmlUtils   : 开始提取XML数据，SOAP响应(前200字符): <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLS...
2025-06-09T11:40:14.076+08:00 DEBUG 4964 --- [data-synchronization] [http-nio-8080-exec-1] c.n.datasynchronization.utils.XmlUtils   : SOAP命名空间: http://schemas.xmlsoap.org/soap/envelope/
2025-06-09T11:40:14.077+08:00 DEBUG 4964 --- [data-synchronization] [http-nio-8080-exec-1] c.n.datasynchronization.utils.XmlUtils   : 成功获取SOAP Body元素
2025-06-09T11:40:14.077+08:00 DEBUG 4964 --- [data-synchronization] [http-nio-8080-exec-1] c.n.datasynchronization.utils.XmlUtils   : 响应元素名称: GetOrgInfoFromMDMResponse
2025-06-09T11:40:14.078+08:00 DEBUG 4964 --- [data-synchronization] [http-nio-8080-exec-1] c.n.datasynchronization.utils.XmlUtils   : 成功提取XML数据(前100字符): <?xml version="1.0" encoding="utf-16"?><O_DATAS>  <O_DATA>    <NM>07a453ec-8c46-41b5-9282-c5f83ebbae...
2025-06-09T11:40:14.084+08:00  INFO 4964 --- [data-synchronization] [http-nio-8080-exec-1] c.n.datasynchronization.utils.XmlUtils   : XML数据解析完成，共处理21条记录
2025-06-09T11:40:14.085+08:00  INFO 4964 --- [data-synchronization] [http-nio-8080-exec-1] c.n.d.s.impl.DataRetrievalServiceImpl    : 成功获取部门数据，共21条记录
2025-06-09T11:40:14.085+08:00  INFO 4964 --- [data-synchronization] [http-nio-8080-exec-1] c.n.d.c.PerformanceMonitoringAspect      : com.nercar.datasynchronization.service.impl.DataRetrievalServiceImpl#getDepartmentData 执行完成，耗时: 946ms
2025-06-09T11:40:14.085+08:00  INFO 4964 --- [data-synchronization] [http-nio-8080-exec-1] c.n.d.c.DataRetrievalController          : 成功返回部门数据，共21条记录
2025-06-09T11:43:10.209+08:00  INFO 4964 --- [data-synchronization] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-06-09T11:43:10.215+08:00  INFO 4964 --- [data-synchronization] [Thread-5] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-09T11:43:10.257+08:00  INFO 4964 --- [data-synchronization] [Thread-5] o.s.s.quartz.SchedulerFactoryBean        : Shutting down Quartz Scheduler
2025-06-09T11:43:10.258+08:00  INFO 4964 --- [data-synchronization] [Thread-5] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-06-09T11:43:10.259+08:00  INFO 4964 --- [data-synchronization] [Thread-5] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-09T11:43:10.259+08:00  INFO 4964 --- [data-synchronization] [Thread-5] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-06-09T11:43:10.262+08:00  INFO 4964 --- [data-synchronization] [Thread-5] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-09T11:43:10.268+08:00  INFO 4964 --- [data-synchronization] [Thread-5] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-06-09T11:43:10.274+08:00  INFO 4964 --- [data-synchronization] [Thread-5] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-06-09T11:43:10.796+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : Starting DataSynchronizationApplication using Java ******** with PID 4964 (E:\fushun\data-synchronization\target\classes started by Dell in E:\fushun\data-synchronization)
2025-06-09T11:43:10.796+08:00 DEBUG 4964 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-09T11:43:10.796+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : No active profile set, falling back to 1 default profile: "default"
2025-06-09T11:43:12.298+08:00  INFO 4964 --- [data-synchronization] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-09T11:43:12.424+08:00  INFO 4964 --- [data-synchronization] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 126 ms. Found 12 JPA repository interfaces.
2025-06-09T11:43:13.525+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-06-09T11:43:13.526+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-09T11:43:13.526+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-06-09T11:43:13.583+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-09T11:43:13.583+08:00  INFO 4964 --- [data-synchronization] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2781 ms
2025-06-09T11:43:14.565+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-09T11:43:14.576+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-09T11:43:14.590+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-09T11:43:14.594+08:00  INFO 4964 --- [data-synchronization] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-06-09T11:43:15.615+08:00  INFO 4964 --- [data-synchronization] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7e4b7c27
2025-06-09T11:43:15.616+08:00  INFO 4964 --- [data-synchronization] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-06-09T11:43:15.618+08:00  WARN 4964 --- [data-synchronization] [restartedMain] org.hibernate.orm.deprecation            : HHH90000025: MySQL8Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-09T11:43:15.618+08:00  WARN 4964 --- [data-synchronization] [restartedMain] org.hibernate.orm.deprecation            : HHH90000026: MySQL8Dialect has been deprecated; use org.hibernate.dialect.MySQLDialect instead
2025-06-09T11:43:16.062+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-09T11:43:16.600+08:00  INFO 4964 --- [data-synchronization] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-09T11:43:17.463+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 开始预加载员工缓存...
2025-06-09T11:43:18.538+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载员工ID缓存完成，共 506 条记录
2025-06-09T11:43:20.019+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载岗位ID缓存完成，共 1517 条记录
2025-06-09T11:43:21.079+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载职称ID缓存完成，共 1329 条记录
2025-06-09T11:43:21.842+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载系统标识ID缓存完成，共 548 条记录
2025-06-09T11:43:22.991+08:00  WARN 4964 --- [data-synchronization] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-09T11:43:24.107+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.impl.StdSchedulerFactory      : Using default implementation for ThreadExecutor
2025-06-09T11:43:24.109+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.core.SchedulerSignalerImpl    : Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-06-09T11:43:24.109+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : Quartz Scheduler v.2.3.2 created.
2025-06-09T11:43:24.111+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.simpl.RAMJobStore             : RAMJobStore initialized.
2025-06-09T11:43:24.111+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-06-09T11:43:24.111+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.impl.StdSchedulerFactory      : Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-06-09T11:43:24.111+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.impl.StdSchedulerFactory      : Quartz scheduler version: 2.3.2
2025-06-09T11:43:24.111+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@2c96c430
2025-06-09T11:43:25.264+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-06-09T11:43:25.289+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-06-09T11:43:25.377+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path ''
2025-06-09T11:43:25.377+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.s.s.quartz.SchedulerFactoryBean        : Starting Quartz Scheduler now
2025-06-09T11:43:25.377+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-06-09T11:43:25.404+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : Started DataSynchronizationApplication in 14.691 seconds (process running for 653.958)
2025-06-09T11:43:25.407+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:43:25.407+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 数据同步应用启动成功！
2025-06-09T11:43:25.407+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 应用访问地址: http://*************:8080
2025-06-09T11:43:25.407+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : Swagger文档地址: http://*************:8080/swagger-ui.html
2025-06-09T11:43:25.407+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:43:25.407+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : SOAP服务调用信息：
2025-06-09T11:43:25.408+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 组织数据服务地址: https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery
2025-06-09T11:43:25.408+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 接口方法: GetOrgInfoFromMDM
2025-06-09T11:43:25.409+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 人员数据服务地址: https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery
2025-06-09T11:43:25.409+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 接口方法: GetUserInfoFromMDM
2025-06-09T11:43:25.409+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:43:25.409+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : HTTP接口调用信息：
2025-06-09T11:43:25.409+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 岗位数据服务地址: http://172.28.98.74:9000/ds/dgc/getQualityPersonnelInfo
2025-06-09T11:43:25.409+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 接口方法: getQualityPersonnelInfo
2025-06-09T11:43:25.409+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:43:25.409+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 定时任务配置信息：
2025-06-09T11:43:25.409+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 数据同步任务执行时间: 0 0 3 * * ? (凌晨3点执行)
2025-06-09T11:43:25.409+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 数据同步范围: 昨天00:00:00至23:59:59的数据
2025-06-09T11:43:25.409+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:43:25.414+08:00  INFO 4964 --- [data-synchronization] [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-06-09T11:43:43.302+08:00  INFO 4964 --- [data-synchronization] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-06-09T11:43:43.304+08:00  INFO 4964 --- [data-synchronization] [Thread-7] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-09T11:43:43.331+08:00  INFO 4964 --- [data-synchronization] [Thread-7] o.s.s.quartz.SchedulerFactoryBean        : Shutting down Quartz Scheduler
2025-06-09T11:43:43.332+08:00  INFO 4964 --- [data-synchronization] [Thread-7] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-06-09T11:43:43.332+08:00  INFO 4964 --- [data-synchronization] [Thread-7] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-09T11:43:43.332+08:00  INFO 4964 --- [data-synchronization] [Thread-7] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-06-09T11:43:43.334+08:00  INFO 4964 --- [data-synchronization] [Thread-7] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-09T11:43:43.335+08:00  INFO 4964 --- [data-synchronization] [Thread-7] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-06-09T11:43:43.690+08:00  INFO 4964 --- [data-synchronization] [Thread-7] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-06-09T11:43:43.867+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : Starting DataSynchronizationApplication using Java ******** with PID 4964 (E:\fushun\data-synchronization\target\classes started by Dell in E:\fushun\data-synchronization)
2025-06-09T11:43:43.868+08:00 DEBUG 4964 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-09T11:43:43.868+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : No active profile set, falling back to 1 default profile: "default"
2025-06-09T11:43:44.764+08:00  INFO 4964 --- [data-synchronization] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-09T11:43:44.880+08:00  INFO 4964 --- [data-synchronization] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 116 ms. Found 12 JPA repository interfaces.
2025-06-09T11:43:45.827+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-06-09T11:43:45.828+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-09T11:43:45.829+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-06-09T11:43:45.875+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-09T11:43:45.875+08:00  INFO 4964 --- [data-synchronization] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2005 ms
2025-06-09T11:43:46.271+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-09T11:43:46.281+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-09T11:43:46.288+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-09T11:43:46.289+08:00  INFO 4964 --- [data-synchronization] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-3 - Starting...
2025-06-09T11:43:47.517+08:00  INFO 4964 --- [data-synchronization] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-3 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1afd49b7
2025-06-09T11:43:47.517+08:00  INFO 4964 --- [data-synchronization] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-3 - Start completed.
2025-06-09T11:43:47.518+08:00  WARN 4964 --- [data-synchronization] [restartedMain] org.hibernate.orm.deprecation            : HHH90000025: MySQL8Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-09T11:43:47.518+08:00  WARN 4964 --- [data-synchronization] [restartedMain] org.hibernate.orm.deprecation            : HHH90000026: MySQL8Dialect has been deprecated; use org.hibernate.dialect.MySQLDialect instead
2025-06-09T11:43:47.859+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-09T11:43:48.547+08:00  INFO 4964 --- [data-synchronization] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-09T11:43:49.232+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 开始预加载员工缓存...
2025-06-09T11:43:50.274+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载员工ID缓存完成，共 506 条记录
2025-06-09T11:43:51.846+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载岗位ID缓存完成，共 1517 条记录
2025-06-09T11:43:52.939+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载职称ID缓存完成，共 1329 条记录
2025-06-09T11:43:53.646+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载系统标识ID缓存完成，共 548 条记录
2025-06-09T11:43:54.561+08:00  WARN 4964 --- [data-synchronization] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-09T11:43:56.164+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.impl.StdSchedulerFactory      : Using default implementation for ThreadExecutor
2025-06-09T11:43:56.166+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.core.SchedulerSignalerImpl    : Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-06-09T11:43:56.166+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : Quartz Scheduler v.2.3.2 created.
2025-06-09T11:43:56.166+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.simpl.RAMJobStore             : RAMJobStore initialized.
2025-06-09T11:43:56.166+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-06-09T11:43:56.166+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.impl.StdSchedulerFactory      : Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-06-09T11:43:56.166+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.impl.StdSchedulerFactory      : Quartz scheduler version: 2.3.2
2025-06-09T11:43:56.166+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@31c1b7f6
2025-06-09T11:43:56.966+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-06-09T11:43:57.000+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-06-09T11:43:57.076+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path ''
2025-06-09T11:43:57.077+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.s.s.quartz.SchedulerFactoryBean        : Starting Quartz Scheduler now
2025-06-09T11:43:57.077+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-06-09T11:43:57.098+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : Started DataSynchronizationApplication in 13.285 seconds (process running for 685.652)
2025-06-09T11:43:57.102+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:43:57.103+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 数据同步应用启动成功！
2025-06-09T11:43:57.103+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 应用访问地址: http://*************:8080
2025-06-09T11:43:57.103+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : Swagger文档地址: http://*************:8080/swagger-ui.html
2025-06-09T11:43:57.104+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:43:57.104+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : SOAP服务调用信息：
2025-06-09T11:43:57.104+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 组织数据服务地址: https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery
2025-06-09T11:43:57.104+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 接口方法: GetOrgInfoFromMDM
2025-06-09T11:43:57.104+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 人员数据服务地址: https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery
2025-06-09T11:43:57.105+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 接口方法: GetUserInfoFromMDM
2025-06-09T11:43:57.105+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:43:57.105+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : HTTP接口调用信息：
2025-06-09T11:43:57.105+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 岗位数据服务地址: http://172.28.98.74:9000/ds/dgc/getQualityPersonnelInfo
2025-06-09T11:43:57.105+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 接口方法: getQualityPersonnelInfo
2025-06-09T11:43:57.105+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:43:57.105+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 定时任务配置信息：
2025-06-09T11:43:57.105+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 数据同步任务执行时间: 0 0 3 * * ? (凌晨3点执行)
2025-06-09T11:43:57.105+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 数据同步范围: 昨天00:00:00至23:59:59的数据
2025-06-09T11:43:57.105+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:43:57.110+08:00  INFO 4964 --- [data-synchronization] [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-06-09T11:44:17.709+08:00  INFO 4964 --- [data-synchronization] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-06-09T11:44:17.715+08:00  INFO 4964 --- [data-synchronization] [Thread-11] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-09T11:44:17.757+08:00  INFO 4964 --- [data-synchronization] [Thread-11] o.s.s.quartz.SchedulerFactoryBean        : Shutting down Quartz Scheduler
2025-06-09T11:44:17.758+08:00  INFO 4964 --- [data-synchronization] [Thread-11] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-06-09T11:44:17.758+08:00  INFO 4964 --- [data-synchronization] [Thread-11] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-09T11:44:17.758+08:00  INFO 4964 --- [data-synchronization] [Thread-11] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-06-09T11:44:17.761+08:00  INFO 4964 --- [data-synchronization] [Thread-11] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-09T11:44:17.763+08:00  INFO 4964 --- [data-synchronization] [Thread-11] com.zaxxer.hikari.HikariDataSource       : HikariPool-3 - Shutdown initiated...
2025-06-09T11:44:17.838+08:00  INFO 4964 --- [data-synchronization] [Thread-11] com.zaxxer.hikari.HikariDataSource       : HikariPool-3 - Shutdown completed.
2025-06-09T11:44:18.053+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : Starting DataSynchronizationApplication using Java ******** with PID 4964 (E:\fushun\data-synchronization\target\classes started by Dell in E:\fushun\data-synchronization)
2025-06-09T11:44:18.053+08:00 DEBUG 4964 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-09T11:44:18.053+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : No active profile set, falling back to 1 default profile: "default"
2025-06-09T11:44:18.954+08:00  INFO 4964 --- [data-synchronization] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-09T11:44:19.027+08:00  INFO 4964 --- [data-synchronization] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 71 ms. Found 12 JPA repository interfaces.
2025-06-09T11:44:19.523+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-06-09T11:44:19.523+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-09T11:44:19.523+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-06-09T11:44:19.553+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-09T11:44:19.553+08:00  INFO 4964 --- [data-synchronization] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1496 ms
2025-06-09T11:44:20.045+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-09T11:44:20.055+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-09T11:44:20.065+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-09T11:44:20.069+08:00  INFO 4964 --- [data-synchronization] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-4 - Starting...
2025-06-09T11:44:20.835+08:00  INFO 4964 --- [data-synchronization] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-4 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7cc478c2
2025-06-09T11:44:20.835+08:00  INFO 4964 --- [data-synchronization] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-4 - Start completed.
2025-06-09T11:44:20.836+08:00  WARN 4964 --- [data-synchronization] [restartedMain] org.hibernate.orm.deprecation            : HHH90000025: MySQL8Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-09T11:44:20.836+08:00  WARN 4964 --- [data-synchronization] [restartedMain] org.hibernate.orm.deprecation            : HHH90000026: MySQL8Dialect has been deprecated; use org.hibernate.dialect.MySQLDialect instead
2025-06-09T11:44:21.553+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-09T11:44:22.675+08:00  INFO 4964 --- [data-synchronization] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-09T11:44:23.501+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 开始预加载员工缓存...
2025-06-09T11:44:24.416+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载员工ID缓存完成，共 506 条记录
2025-06-09T11:44:25.843+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载岗位ID缓存完成，共 1517 条记录
2025-06-09T11:44:27.013+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载职称ID缓存完成，共 1329 条记录
2025-06-09T11:44:27.800+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载系统标识ID缓存完成，共 548 条记录
2025-06-09T11:44:29.403+08:00  WARN 4964 --- [data-synchronization] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-09T11:44:30.565+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.impl.StdSchedulerFactory      : Using default implementation for ThreadExecutor
2025-06-09T11:44:30.568+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.core.SchedulerSignalerImpl    : Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-06-09T11:44:30.568+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : Quartz Scheduler v.2.3.2 created.
2025-06-09T11:44:30.569+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.simpl.RAMJobStore             : RAMJobStore initialized.
2025-06-09T11:44:30.569+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-06-09T11:44:30.569+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.impl.StdSchedulerFactory      : Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-06-09T11:44:30.569+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.impl.StdSchedulerFactory      : Quartz scheduler version: 2.3.2
2025-06-09T11:44:30.569+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@29a832a0
2025-06-09T11:44:31.384+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-06-09T11:44:31.399+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-06-09T11:44:31.454+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path ''
2025-06-09T11:44:31.455+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.s.s.quartz.SchedulerFactoryBean        : Starting Quartz Scheduler now
2025-06-09T11:44:31.455+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-06-09T11:44:31.470+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : Started DataSynchronizationApplication in 13.48 seconds (process running for 720.023)
2025-06-09T11:44:31.471+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:44:31.471+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 数据同步应用启动成功！
2025-06-09T11:44:31.471+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 应用访问地址: http://*************:8080
2025-06-09T11:44:31.471+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : Swagger文档地址: http://*************:8080/swagger-ui.html
2025-06-09T11:44:31.471+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:44:31.471+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : SOAP服务调用信息：
2025-06-09T11:44:31.471+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 组织数据服务地址: https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery
2025-06-09T11:44:31.472+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 接口方法: GetOrgInfoFromMDM
2025-06-09T11:44:31.472+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 人员数据服务地址: https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery
2025-06-09T11:44:31.472+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 接口方法: GetUserInfoFromMDM
2025-06-09T11:44:31.472+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:44:31.472+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : HTTP接口调用信息：
2025-06-09T11:44:31.472+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 岗位数据服务地址: http://172.28.98.74:9000/ds/dgc/getQualityPersonnelInfo
2025-06-09T11:44:31.472+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 接口方法: getQualityPersonnelInfo
2025-06-09T11:44:31.472+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:44:31.472+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 定时任务配置信息：
2025-06-09T11:44:31.472+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 数据同步任务执行时间: 0 0 3 * * ? (凌晨3点执行)
2025-06-09T11:44:31.472+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 数据同步范围: 昨天00:00:00至23:59:59的数据
2025-06-09T11:44:31.472+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:44:31.475+08:00  INFO 4964 --- [data-synchronization] [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-06-09T11:45:08.432+08:00  INFO 4964 --- [data-synchronization] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-06-09T11:45:08.438+08:00  INFO 4964 --- [data-synchronization] [Thread-15] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-09T11:45:08.468+08:00  INFO 4964 --- [data-synchronization] [Thread-15] o.s.s.quartz.SchedulerFactoryBean        : Shutting down Quartz Scheduler
2025-06-09T11:45:08.468+08:00  INFO 4964 --- [data-synchronization] [Thread-15] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-06-09T11:45:08.468+08:00  INFO 4964 --- [data-synchronization] [Thread-15] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-09T11:45:08.468+08:00  INFO 4964 --- [data-synchronization] [Thread-15] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-06-09T11:45:08.471+08:00  INFO 4964 --- [data-synchronization] [Thread-15] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-09T11:45:08.472+08:00  INFO 4964 --- [data-synchronization] [Thread-15] com.zaxxer.hikari.HikariDataSource       : HikariPool-4 - Shutdown initiated...
2025-06-09T11:45:08.476+08:00  INFO 4964 --- [data-synchronization] [Thread-15] com.zaxxer.hikari.HikariDataSource       : HikariPool-4 - Shutdown completed.
2025-06-09T11:45:08.670+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : Starting DataSynchronizationApplication using Java ******** with PID 4964 (E:\fushun\data-synchronization\target\classes started by Dell in E:\fushun\data-synchronization)
2025-06-09T11:45:08.670+08:00 DEBUG 4964 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-09T11:45:08.670+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : No active profile set, falling back to 1 default profile: "default"
2025-06-09T11:45:09.395+08:00  INFO 4964 --- [data-synchronization] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-09T11:45:09.454+08:00  INFO 4964 --- [data-synchronization] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 59 ms. Found 12 JPA repository interfaces.
2025-06-09T11:45:09.903+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-06-09T11:45:09.903+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-09T11:45:09.903+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-06-09T11:45:09.928+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-09T11:45:09.928+08:00  INFO 4964 --- [data-synchronization] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1255 ms
2025-06-09T11:45:10.564+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-09T11:45:10.572+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-09T11:45:10.587+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-09T11:45:10.588+08:00  INFO 4964 --- [data-synchronization] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-5 - Starting...
2025-06-09T11:45:16.957+08:00  INFO 4964 --- [data-synchronization] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-5 - Added connection com.mysql.cj.jdbc.ConnectionImpl@bdc222d
2025-06-09T11:45:16.958+08:00  INFO 4964 --- [data-synchronization] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-5 - Start completed.
2025-06-09T11:45:16.959+08:00  WARN 4964 --- [data-synchronization] [restartedMain] org.hibernate.orm.deprecation            : HHH90000025: MySQL8Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-09T11:45:16.959+08:00  WARN 4964 --- [data-synchronization] [restartedMain] org.hibernate.orm.deprecation            : HHH90000026: MySQL8Dialect has been deprecated; use org.hibernate.dialect.MySQLDialect instead
2025-06-09T11:45:17.264+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-09T11:45:18.555+08:00  INFO 4964 --- [data-synchronization] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-09T11:45:19.136+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 开始预加载员工缓存...
2025-06-09T11:45:21.000+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载员工ID缓存完成，共 506 条记录
2025-06-09T11:45:23.037+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载岗位ID缓存完成，共 1517 条记录
2025-06-09T11:45:24.617+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载职称ID缓存完成，共 1329 条记录
2025-06-09T11:45:25.596+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载系统标识ID缓存完成，共 548 条记录
2025-06-09T11:45:26.621+08:00  WARN 4964 --- [data-synchronization] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-09T11:45:27.739+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.impl.StdSchedulerFactory      : Using default implementation for ThreadExecutor
2025-06-09T11:45:27.742+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.core.SchedulerSignalerImpl    : Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-06-09T11:45:27.742+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : Quartz Scheduler v.2.3.2 created.
2025-06-09T11:45:27.742+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.simpl.RAMJobStore             : RAMJobStore initialized.
2025-06-09T11:45:27.742+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-06-09T11:45:27.742+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.impl.StdSchedulerFactory      : Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-06-09T11:45:27.742+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.impl.StdSchedulerFactory      : Quartz scheduler version: 2.3.2
2025-06-09T11:45:27.742+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7719963
2025-06-09T11:45:28.374+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-06-09T11:45:28.391+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-06-09T11:45:28.455+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path ''
2025-06-09T11:45:28.456+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.s.s.quartz.SchedulerFactoryBean        : Starting Quartz Scheduler now
2025-06-09T11:45:28.456+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-06-09T11:45:28.476+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : Started DataSynchronizationApplication in 19.856 seconds (process running for 777.03)
2025-06-09T11:45:28.478+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:45:28.479+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 数据同步应用启动成功！
2025-06-09T11:45:28.479+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 应用访问地址: http://*************:8080
2025-06-09T11:45:28.479+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : Swagger文档地址: http://*************:8080/swagger-ui.html
2025-06-09T11:45:28.479+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:45:28.479+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : SOAP服务调用信息：
2025-06-09T11:45:28.479+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 组织数据服务地址: https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery
2025-06-09T11:45:28.479+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 接口方法: GetOrgInfoFromMDM
2025-06-09T11:45:28.480+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 人员数据服务地址: https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery
2025-06-09T11:45:28.480+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 接口方法: GetUserInfoFromMDM
2025-06-09T11:45:28.480+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:45:28.480+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : HTTP接口调用信息：
2025-06-09T11:45:28.480+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 岗位数据服务地址: http://172.28.98.74:9000/ds/dgc/getQualityPersonnelInfo
2025-06-09T11:45:28.480+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 接口方法: getQualityPersonnelInfo
2025-06-09T11:45:28.480+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:45:28.480+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 定时任务配置信息：
2025-06-09T11:45:28.480+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 数据同步任务执行时间: 0 0 3 * * ? (凌晨3点执行)
2025-06-09T11:45:28.480+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 数据同步范围: 昨天00:00:00至23:59:59的数据
2025-06-09T11:45:28.481+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:45:28.483+08:00  INFO 4964 --- [data-synchronization] [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-06-09T11:45:38.950+08:00  INFO 4964 --- [data-synchronization] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-06-09T11:45:38.953+08:00  INFO 4964 --- [data-synchronization] [Thread-19] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-09T11:45:38.976+08:00  INFO 4964 --- [data-synchronization] [Thread-19] o.s.s.quartz.SchedulerFactoryBean        : Shutting down Quartz Scheduler
2025-06-09T11:45:38.976+08:00  INFO 4964 --- [data-synchronization] [Thread-19] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-06-09T11:45:38.976+08:00  INFO 4964 --- [data-synchronization] [Thread-19] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-09T11:45:38.976+08:00  INFO 4964 --- [data-synchronization] [Thread-19] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-06-09T11:45:38.977+08:00  INFO 4964 --- [data-synchronization] [Thread-19] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-09T11:45:38.977+08:00  INFO 4964 --- [data-synchronization] [Thread-19] com.zaxxer.hikari.HikariDataSource       : HikariPool-5 - Shutdown initiated...
2025-06-09T11:45:38.979+08:00  INFO 4964 --- [data-synchronization] [Thread-19] com.zaxxer.hikari.HikariDataSource       : HikariPool-5 - Shutdown completed.
2025-06-09T11:45:39.171+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : Starting DataSynchronizationApplication using Java ******** with PID 4964 (E:\fushun\data-synchronization\target\classes started by Dell in E:\fushun\data-synchronization)
2025-06-09T11:45:39.171+08:00 DEBUG 4964 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-09T11:45:39.172+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : No active profile set, falling back to 1 default profile: "default"
2025-06-09T11:45:39.906+08:00  INFO 4964 --- [data-synchronization] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-09T11:45:39.965+08:00  INFO 4964 --- [data-synchronization] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 58 ms. Found 12 JPA repository interfaces.
2025-06-09T11:45:40.923+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-06-09T11:45:40.923+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-09T11:45:40.923+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-06-09T11:45:40.964+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-09T11:45:40.964+08:00  INFO 4964 --- [data-synchronization] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1789 ms
2025-06-09T11:45:41.472+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-09T11:45:41.477+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-09T11:45:41.482+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-09T11:45:41.483+08:00  INFO 4964 --- [data-synchronization] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-6 - Starting...
2025-06-09T11:45:42.777+08:00  INFO 4964 --- [data-synchronization] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-6 - Added connection com.mysql.cj.jdbc.ConnectionImpl@77f73763
2025-06-09T11:45:42.778+08:00  INFO 4964 --- [data-synchronization] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-6 - Start completed.
2025-06-09T11:45:42.778+08:00  WARN 4964 --- [data-synchronization] [restartedMain] org.hibernate.orm.deprecation            : HHH90000025: MySQL8Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-09T11:45:42.778+08:00  WARN 4964 --- [data-synchronization] [restartedMain] org.hibernate.orm.deprecation            : HHH90000026: MySQL8Dialect has been deprecated; use org.hibernate.dialect.MySQLDialect instead
2025-06-09T11:45:42.990+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-09T11:45:43.930+08:00  INFO 4964 --- [data-synchronization] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-09T11:45:44.688+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 开始预加载员工缓存...
2025-06-09T11:45:45.941+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载员工ID缓存完成，共 506 条记录
2025-06-09T11:45:47.501+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载岗位ID缓存完成，共 1517 条记录
2025-06-09T11:45:48.819+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载职称ID缓存完成，共 1329 条记录
2025-06-09T11:45:49.628+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载系统标识ID缓存完成，共 548 条记录
2025-06-09T11:45:50.675+08:00  WARN 4964 --- [data-synchronization] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-09T11:45:51.914+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.impl.StdSchedulerFactory      : Using default implementation for ThreadExecutor
2025-06-09T11:45:51.916+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.core.SchedulerSignalerImpl    : Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-06-09T11:45:51.916+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : Quartz Scheduler v.2.3.2 created.
2025-06-09T11:45:51.916+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.simpl.RAMJobStore             : RAMJobStore initialized.
2025-06-09T11:45:51.916+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-06-09T11:45:51.916+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.impl.StdSchedulerFactory      : Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-06-09T11:45:51.916+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.impl.StdSchedulerFactory      : Quartz scheduler version: 2.3.2
2025-06-09T11:45:51.916+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@15701454
2025-06-09T11:45:52.678+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-06-09T11:45:52.695+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-06-09T11:45:52.764+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path ''
2025-06-09T11:45:52.765+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.s.s.quartz.SchedulerFactoryBean        : Starting Quartz Scheduler now
2025-06-09T11:45:52.765+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-06-09T11:45:52.782+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : Started DataSynchronizationApplication in 13.664 seconds (process running for 801.335)
2025-06-09T11:45:52.784+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:45:52.785+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 数据同步应用启动成功！
2025-06-09T11:45:52.785+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 应用访问地址: http://*************:8080
2025-06-09T11:45:52.785+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : Swagger文档地址: http://*************:8080/swagger-ui.html
2025-06-09T11:45:52.785+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:45:52.785+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : SOAP服务调用信息：
2025-06-09T11:45:52.785+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 组织数据服务地址: https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery
2025-06-09T11:45:52.785+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 接口方法: GetOrgInfoFromMDM
2025-06-09T11:45:52.785+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 人员数据服务地址: https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery
2025-06-09T11:45:52.785+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 接口方法: GetUserInfoFromMDM
2025-06-09T11:45:52.785+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:45:52.785+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : HTTP接口调用信息：
2025-06-09T11:45:52.785+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 岗位数据服务地址: http://172.28.98.74:9000/ds/dgc/getQualityPersonnelInfo
2025-06-09T11:45:52.785+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 接口方法: getQualityPersonnelInfo
2025-06-09T11:45:52.785+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:45:52.785+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 定时任务配置信息：
2025-06-09T11:45:52.785+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 数据同步任务执行时间: 0 0 3 * * ? (凌晨3点执行)
2025-06-09T11:45:52.785+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 数据同步范围: 昨天00:00:00至23:59:59的数据
2025-06-09T11:45:52.785+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:45:52.789+08:00  INFO 4964 --- [data-synchronization] [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-06-09T11:46:06.547+08:00  INFO 4964 --- [data-synchronization] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-06-09T11:46:06.549+08:00  INFO 4964 --- [data-synchronization] [Thread-23] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-09T11:46:06.580+08:00  INFO 4964 --- [data-synchronization] [Thread-23] o.s.s.quartz.SchedulerFactoryBean        : Shutting down Quartz Scheduler
2025-06-09T11:46:06.580+08:00  INFO 4964 --- [data-synchronization] [Thread-23] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-06-09T11:46:06.580+08:00  INFO 4964 --- [data-synchronization] [Thread-23] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-09T11:46:06.580+08:00  INFO 4964 --- [data-synchronization] [Thread-23] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-06-09T11:46:06.582+08:00  INFO 4964 --- [data-synchronization] [Thread-23] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-09T11:46:06.584+08:00  INFO 4964 --- [data-synchronization] [Thread-23] com.zaxxer.hikari.HikariDataSource       : HikariPool-6 - Shutdown initiated...
2025-06-09T11:46:06.588+08:00  INFO 4964 --- [data-synchronization] [Thread-23] com.zaxxer.hikari.HikariDataSource       : HikariPool-6 - Shutdown completed.
2025-06-09T11:46:06.801+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : Starting DataSynchronizationApplication using Java ******** with PID 4964 (E:\fushun\data-synchronization\target\classes started by Dell in E:\fushun\data-synchronization)
2025-06-09T11:46:06.801+08:00 DEBUG 4964 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-09T11:46:06.801+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : No active profile set, falling back to 1 default profile: "default"
2025-06-09T11:46:07.699+08:00  INFO 4964 --- [data-synchronization] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-09T11:46:07.750+08:00  INFO 4964 --- [data-synchronization] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 50 ms. Found 12 JPA repository interfaces.
2025-06-09T11:46:08.145+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-06-09T11:46:08.146+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-09T11:46:08.146+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-06-09T11:46:08.168+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-09T11:46:08.168+08:00  INFO 4964 --- [data-synchronization] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1364 ms
2025-06-09T11:46:08.518+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-09T11:46:08.522+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-09T11:46:08.533+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-09T11:46:08.535+08:00  INFO 4964 --- [data-synchronization] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-7 - Starting...
2025-06-09T11:46:09.244+08:00  INFO 4964 --- [data-synchronization] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-7 - Added connection com.mysql.cj.jdbc.ConnectionImpl@c92a52e
2025-06-09T11:46:09.244+08:00  INFO 4964 --- [data-synchronization] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-7 - Start completed.
2025-06-09T11:46:09.245+08:00  WARN 4964 --- [data-synchronization] [restartedMain] org.hibernate.orm.deprecation            : HHH90000025: MySQL8Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-09T11:46:09.245+08:00  WARN 4964 --- [data-synchronization] [restartedMain] org.hibernate.orm.deprecation            : HHH90000026: MySQL8Dialect has been deprecated; use org.hibernate.dialect.MySQLDialect instead
2025-06-09T11:46:09.516+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-09T11:46:11.390+08:00  INFO 4964 --- [data-synchronization] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-09T11:46:12.022+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 开始预加载员工缓存...
2025-06-09T11:46:13.365+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载员工ID缓存完成，共 506 条记录
2025-06-09T11:46:14.958+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载岗位ID缓存完成，共 1517 条记录
2025-06-09T11:46:16.000+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载职称ID缓存完成，共 1329 条记录
2025-06-09T11:46:16.614+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.s.impl.EmployeeSyncServiceImpl     : 预加载系统标识ID缓存完成，共 548 条记录
2025-06-09T11:46:17.642+08:00  WARN 4964 --- [data-synchronization] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-09T11:46:18.561+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.impl.StdSchedulerFactory      : Using default implementation for ThreadExecutor
2025-06-09T11:46:18.562+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.core.SchedulerSignalerImpl    : Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-06-09T11:46:18.562+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : Quartz Scheduler v.2.3.2 created.
2025-06-09T11:46:18.562+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.simpl.RAMJobStore             : RAMJobStore initialized.
2025-06-09T11:46:18.562+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-06-09T11:46:18.562+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.impl.StdSchedulerFactory      : Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-06-09T11:46:18.562+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.impl.StdSchedulerFactory      : Quartz scheduler version: 2.3.2
2025-06-09T11:46:18.562+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@144ddcfe
2025-06-09T11:46:19.431+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-06-09T11:46:19.453+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-06-09T11:46:19.536+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path ''
2025-06-09T11:46:19.537+08:00  INFO 4964 --- [data-synchronization] [restartedMain] o.s.s.quartz.SchedulerFactoryBean        : Starting Quartz Scheduler now
2025-06-09T11:46:19.537+08:00  INFO 4964 --- [data-synchronization] [restartedMain] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-06-09T11:46:19.560+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.DataSynchronizationApplication     : Started DataSynchronizationApplication in 12.814 seconds (process running for 828.114)
2025-06-09T11:46:19.562+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:46:19.563+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 数据同步应用启动成功！
2025-06-09T11:46:19.563+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 应用访问地址: http://*************:8080
2025-06-09T11:46:19.563+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : Swagger文档地址: http://*************:8080/swagger-ui.html
2025-06-09T11:46:19.563+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:46:19.563+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : SOAP服务调用信息：
2025-06-09T11:46:19.563+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 组织数据服务地址: https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery
2025-06-09T11:46:19.563+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 接口方法: GetOrgInfoFromMDM
2025-06-09T11:46:19.563+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 人员数据服务地址: https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery
2025-06-09T11:46:19.563+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 接口方法: GetUserInfoFromMDM
2025-06-09T11:46:19.563+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:46:19.563+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : HTTP接口调用信息：
2025-06-09T11:46:19.563+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 岗位数据服务地址: http://172.28.98.74:9000/ds/dgc/getQualityPersonnelInfo
2025-06-09T11:46:19.563+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 接口方法: getQualityPersonnelInfo
2025-06-09T11:46:19.563+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:46:19.563+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 定时任务配置信息：
2025-06-09T11:46:19.563+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 数据同步任务执行时间: 0 0 3 * * ? (凌晨3点执行)
2025-06-09T11:46:19.564+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : 数据同步范围: 昨天00:00:00至23:59:59的数据
2025-06-09T11:46:19.564+08:00  INFO 4964 --- [data-synchronization] [restartedMain] c.n.d.l.ApplicationStartupListener       : =====================================================
2025-06-09T11:46:19.567+08:00  INFO 4964 --- [data-synchronization] [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-06-09T11:46:40.215+08:00  INFO 4964 --- [data-synchronization] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (1 addition, 0 deletions, 0 modifications)
2025-06-09T11:46:40.218+08:00  INFO 4964 --- [data-synchronization] [Thread-27] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-09T11:46:40.243+08:00  INFO 4964 --- [data-synchronization] [Thread-27] o.s.s.quartz.SchedulerFactoryBean        : Shutting down Quartz Scheduler
2025-06-09T11:46:40.243+08:00  INFO 4964 --- [data-synchronization] [Thread-27] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-06-09T11:46:40.243+08:00  INFO 4964 --- [data-synchronization] [Thread-27] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-09T11:46:40.243+08:00  INFO 4964 --- [data-synchronization] [Thread-27] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-06-09T11:46:40.245+08:00  INFO 4964 --- [data-synchronization] [Thread-27] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-09T11:46:40.245+08:00  INFO 4964 --- [data-synchronization] [Thread-27] com.zaxxer.hikari.HikariDataSource       : HikariPool-7 - Shutdown initiated...
2025-06-09T11:46:40.249+08:00  INFO 4964 --- [data-synchronization] [Thread-27] com.zaxxer.hikari.HikariDataSource       : HikariPool-7 - Shutdown completed.
