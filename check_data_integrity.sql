-- =====================================================
-- 部门数据完整性检查SQL
-- 检查department_sync_test表中的数据是否能构成完整的树结构
-- =====================================================

-- 1. 基础统计信息
SELECT 
    '总记录数' as 统计项,
    COUNT(*) as 数量
FROM department_sync_test
UNION ALL
SELECT 
    '正常状态记录数' as 统计项,
    COUNT(*) as 数量
FROM department_sync_test 
WHERE is_history = 0 AND user_predef_14 != 'D'
UNION ALL
SELECT 
    '历史记录数' as 统计项,
    COUNT(*) as 数量
FROM department_sync_test 
WHERE is_history = 1
UNION ALL
SELECT 
    '删除状态记录数' as 统计项,
    COUNT(*) as 数量
FROM department_sync_test 
WHERE user_predef_14 = 'D';

-- 2. 父级编码分布统计
SELECT 
    parent_code as 父级编码,
    COUNT(*) as 子部门数量
FROM department_sync_test 
WHERE is_history = 0 AND user_predef_14 != 'D'
GROUP BY parent_code 
ORDER BY COUNT(*) DESC
LIMIT 20;

-- 3. 检查孤儿节点（最关键的检查）
-- 找出parent_code不为空但在系统中找不到对应父节点的部门
SELECT 
    '孤儿节点检查' as 检查项,
    COUNT(*) as 问题数量
FROM department_sync_test d1 
WHERE d1.is_history = 0 
AND d1.user_predef_14 != 'D'
AND d1.parent_code IS NOT NULL 
AND d1.parent_code != '' 
AND d1.parent_code != '1'  -- 排除根节点标识符
AND d1.parent_code NOT IN (
    SELECT d2.org_code 
    FROM department_sync_test d2 
    WHERE d2.is_history = 0 AND d2.user_predef_14 != 'D'
);

-- 4. 详细列出孤儿节点（如果存在）
SELECT 
    d1.org_code as 组织编码,
    d1.org_name as 组织名称,
    d1.parent_code as 父级编码,
    '父级不存在' as 问题描述
FROM department_sync_test d1 
WHERE d1.is_history = 0 
AND d1.user_predef_14 != 'D'
AND d1.parent_code IS NOT NULL 
AND d1.parent_code != '' 
AND d1.parent_code != '1'  -- 排除根节点标识符
AND d1.parent_code NOT IN (
    SELECT d2.org_code 
    FROM department_sync_test d2 
    WHERE d2.is_history = 0 AND d2.user_predef_14 != 'D'
)
ORDER BY d1.parent_code, d1.org_code;

-- 5. 检查根节点情况
SELECT 
    '根节点统计' as 检查项,
    COUNT(*) as 数量
FROM department_sync_test 
WHERE is_history = 0 
AND user_predef_14 != 'D'
AND (parent_code IS NULL OR parent_code = '' OR parent_code = '1');

-- 6. 检查是否有循环引用
SELECT 
    '循环引用检查' as 检查项,
    COUNT(*) as 问题数量
FROM department_sync_test 
WHERE is_history = 0 
AND user_predef_14 != 'D'
AND org_code = parent_code;

-- 7. 检查特殊parent_code值的分布
SELECT 
    CASE 
        WHEN parent_code IS NULL THEN 'NULL'
        WHEN parent_code = '' THEN '空字符串'
        WHEN parent_code = '1' THEN '根节点标识符(1)'
        WHEN parent_code LIKE 'X%' THEN 'X开头编码'
        ELSE '其他编码'
    END as 父级编码类型,
    COUNT(*) as 数量
FROM department_sync_test 
WHERE is_history = 0 AND user_predef_14 != 'D'
GROUP BY 
    CASE 
        WHEN parent_code IS NULL THEN 'NULL'
        WHEN parent_code = '' THEN '空字符串'
        WHEN parent_code = '1' THEN '根节点标识符(1)'
        WHEN parent_code LIKE 'X%' THEN 'X开头编码'
        ELSE '其他编码'
    END
ORDER BY COUNT(*) DESC;

-- 8. 检查最深层级（树的深度）
WITH RECURSIVE dept_hierarchy AS (
    -- 根节点
    SELECT 
        org_code,
        org_name,
        parent_code,
        0 as level
    FROM department_sync_test 
    WHERE is_history = 0 
    AND user_predef_14 != 'D'
    AND (parent_code IS NULL OR parent_code = '' OR parent_code = '1')
    
    UNION ALL
    
    -- 递归查找子节点
    SELECT 
        d.org_code,
        d.org_name,
        d.parent_code,
        h.level + 1
    FROM department_sync_test d
    INNER JOIN dept_hierarchy h ON d.parent_code = h.org_code
    WHERE d.is_history = 0 
    AND d.user_predef_14 != 'D'
    AND h.level < 10  -- 防止无限递归
)
SELECT 
    '最大层级深度' as 统计项,
    MAX(level) as 数值
FROM dept_hierarchy;

-- 9. 按层级统计部门数量
WITH RECURSIVE dept_hierarchy AS (
    -- 根节点
    SELECT 
        org_code,
        org_name,
        parent_code,
        0 as level
    FROM department_sync_test 
    WHERE is_history = 0 
    AND user_predef_14 != 'D'
    AND (parent_code IS NULL OR parent_code = '' OR parent_code = '1')
    
    UNION ALL
    
    -- 递归查找子节点
    SELECT 
        d.org_code,
        d.org_name,
        d.parent_code,
        h.level + 1
    FROM department_sync_test d
    INNER JOIN dept_hierarchy h ON d.parent_code = h.org_code
    WHERE d.is_history = 0 
    AND d.user_predef_14 != 'D'
    AND h.level < 10  -- 防止无限递归
)
SELECT 
    level as 层级,
    COUNT(*) as 部门数量
FROM dept_hierarchy
GROUP BY level
ORDER BY level;

-- 10. 数据质量总结
SELECT 
    '数据质量评估' as 评估项,
    CASE 
        WHEN (
            SELECT COUNT(*) 
            FROM department_sync_test d1 
            WHERE d1.is_history = 0 
            AND d1.user_predef_14 != 'D'
            AND d1.parent_code IS NOT NULL 
            AND d1.parent_code != '' 
            AND d1.parent_code != '1'
            AND d1.parent_code NOT IN (
                SELECT d2.org_code 
                FROM department_sync_test d2 
                WHERE d2.is_history = 0 AND d2.user_predef_14 != 'D'
            )
        ) = 0 THEN '✅ 可以构成完整树结构'
        ELSE '❌ 存在孤儿节点，无法构成完整树结构'
    END as 评估结果;
