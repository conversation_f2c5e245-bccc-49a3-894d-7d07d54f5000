//package com.nercar.datasynchronization.service;
//
//import com.nercar.datasynchronization.entity.PositionInfo;
//
//import java.util.List;
//
///**
// * 岗位服务接口
// */
//public interface PositionService {
//
//    /**
//     * 获取所有岗位
//     */
//    List<PositionInfo> getAllPositions();
//
//    /**
//     * 根据ID获取岗位
//     */
//    PositionInfo getPositionById(String id);
//
//    /**
//     * 创建岗位
//     */
//    PositionInfo createPosition(PositionInfo position);
//
//    /**
//     * 更新岗位
//     */
//    PositionInfo updatePosition(PositionInfo position);
//
//    /**
//     * 删除岗位
//     */
//    boolean deletePosition(String id);
//
//    /**
//     * 根据部门ID获取岗位
//     */
//    List<PositionInfo> getPositionsByDepartmentId(Integer departmentId);
//
//    /**
//     * 根据员工ID获取岗位
//     */
//    List<PositionInfo> getPositionsByEmployeeId(Long employeeId);
//}