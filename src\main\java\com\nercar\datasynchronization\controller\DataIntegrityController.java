package com.nercar.datasynchronization.controller;

import com.nercar.datasynchronization.service.DataIntegrityCheckService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 数据完整性检查控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/data-integrity")
@RequiredArgsConstructor
public class DataIntegrityController {

    private final DataIntegrityCheckService dataIntegrityCheckService;

    /**
     * 执行完整的数据完整性检查
     */
    @GetMapping("/check")
    public ResponseEntity<Map<String, Object>> checkDataIntegrity() {
        try {
            log.info("开始执行数据完整性检查...");
            Map<String, Object> result = dataIntegrityCheckService.performFullIntegrityCheck();
            log.info("数据完整性检查完成");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("数据完整性检查失败", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "检查失败: " + e.getMessage()));
        }
    }
}
