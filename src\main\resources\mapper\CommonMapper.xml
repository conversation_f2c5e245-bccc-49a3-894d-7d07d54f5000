<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nercar.datasynchronization.mapper.CommonMapper">

    <insert id="insertBatchHrUserDetails" parameterType="java.util.List">
        INSERT INTO hr_user_details (
        id, education, sex, birthday, age, user_no, post, name,
        department, department_id, company_id, create_user_no,
        create_date_time, update_user_no, update_date_time,
        post_no, skill_level, work_exp, teams_groups
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id}, #{item.education}, #{item.sex}, #{item.birthday},
            #{item.age}, #{item.userNo}, #{item.post}, #{item.name},
            #{item.department}, #{item.departmentId}, #{item.companyId},
            #{item.createUserNo}, #{item.createDateTime}, #{item.updateUserNo},
            #{item.updateDateTime}, #{item.postNo}, #{item.skillLevel},
            #{item.workExp}, #{item.teamsGroups}
            )
        </foreach>
    </insert>

    <delete id="deleteHrDetails">
        DELETE FROM hr_user_details
    </delete>

    <select id="findHrDetailsByUserNo" resultType="com.nercar.datasynchronization.entity.HrUserDetails">
        SELECT * FROM hr_user_details WHERE user_no = #{userNo}
    </select>

    <update id="updateHrUserDetails" parameterType="com.nercar.datasynchronization.entity.HrUserDetails">
        UPDATE hr_user_details
        SET
            education = #{education},
            sex = #{sex},
            birthday = #{birthday},
            age = #{age},
            post = #{post},
            name = #{name},
            department = #{department},
            department_id = #{departmentId},
            company_id = #{companyId},
            update_user_no = #{updateUserNo},
            update_date_time = #{updateDateTime},
            post_no = #{postNo},
            skill_level = #{skillLevel},
            work_exp = #{workExp},
            teams_groups = #{teamsGroups}
        WHERE user_no = #{userNo}
    </update>
</mapper>