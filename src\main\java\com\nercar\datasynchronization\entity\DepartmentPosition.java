package com.nercar.datasynchronization.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.util.Date;

/**
 * 部门-岗位关联实体 - 存储部门与岗位的关联关系
 */
@Data
@Entity
@Table(name = "department_position", 
       indexes = {
           @Index(name = "idx_dept_pos_dept_id", columnList = "department_id"),
           @Index(name = "idx_dept_pos_position_code", columnList = "position_code")
       },
       uniqueConstraints = {
           @UniqueConstraint(name = "uk_dept_pos_dept_position", 
                            columnNames = {"department_code", "position_code"})
       })
@DynamicInsert
@DynamicUpdate
public class DepartmentPosition {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "department_id", nullable = false)
    private Integer departmentId;
    
    @Column(name = "department_code", nullable = false)
    private String departmentCode;
    
    @Column(name = "department_name")
    private String departmentName;
    
    @Column(name = "position_id")
    private String positionId;
    
    @Column(name = "position_code", nullable = false)
    private String positionCode;
    
    @Column(name = "position_name", nullable = false)
    private String positionName;
    
    @Column(name = "headcount_limit")
    private Integer headcountLimit;
    
    @Column(name = "is_active")
    private Boolean isActive;
    
    @Column(name = "created_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdTime;
    
    @Column(name = "updated_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedTime;
}