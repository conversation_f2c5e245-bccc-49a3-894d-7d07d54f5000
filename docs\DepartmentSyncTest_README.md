# 部门同步测试功能说明

## 📋 概述

基于ERP同步逻辑重新实现的部门同步功能，采用状态感知处理、特殊数据处理和关系修复机制，用于验证是否能获取完整的部门数据。

## 🎯 核心特性

### 1. **状态感知处理**
- **新增操作**：本地不存在 + ERP中ISHISTORY=0
- **更新操作**：本地已存在 + ERP中ISHISTORY=0  
- **删除操作**：本地已存在 + ERP中ISHISTORY=1
- **跳过操作**：本地不存在 + ERP中ISHISTORY=1

### 2. **特殊数据处理**
- 自动删除特殊组织编码的部门（如org_code="X"）
- 清理根节点异常数据

### 3. **关系修复机制**
- 检测父子关系异常
- 自动修复孤儿节点（设置为根节点）

### 4. **精简字段同步**
只同步必要的7个核心字段：
- `org_code` - 组织编码
- `org_name` - 组织名称
- `parent_code` - 父级编码
- `dept_uuid` - 部门UUID
- `full_name` - 组织全称
- `is_history` - 历史状态
- `user_predef_14` - 操作标识

## 🏗️ 数据表结构

### 测试表：`department_sync_test`
```sql
CREATE TABLE department_sync_test (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    org_code VARCHAR(50) NOT NULL COMMENT '组织编码',
    org_name VARCHAR(200) COMMENT '组织名称',
    parent_code VARCHAR(50) COMMENT '父级编码', 
    dept_uuid VARCHAR(50) COMMENT '部门UUID',
    full_name VARCHAR(500) COMMENT '组织全称',
    is_history TINYINT DEFAULT 0 COMMENT '历史状态',
    user_predef_14 VARCHAR(10) COMMENT '操作标识',
    update_time DATETIME COMMENT '更新时间',
    sync_date DATE COMMENT '同步日期',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 日志表：`department_sync_operation_log`
```sql
CREATE TABLE department_sync_operation_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    sync_date DATE COMMENT '同步日期',
    org_code VARCHAR(50) COMMENT '组织编码',
    operation_type VARCHAR(20) COMMENT '操作类型',
    is_history_value TINYINT COMMENT 'ISHISTORY字段值',
    user_predef_14_value VARCHAR(10) COMMENT 'USERPREDEF_14字段值',
    local_exists TINYINT COMMENT '本地是否已存在',
    operation_result VARCHAR(20) COMMENT '操作结果',
    error_message TEXT COMMENT '错误信息',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## 🚀 API接口

### 基础URL
```
http://localhost:8080/api/sync/test
```

### 主要接口

#### 1. 全量同步
```http
POST /departments/full?startDate=2024-01-01&endDate=2024-01-31
```

#### 2. 单天同步
```http
POST /departments/daily?syncDate=2024-01-01
```

#### 3. 数据统计
```http
GET /departments/statistics
```

#### 4. 数据对比
```http
GET /departments/compare
```

#### 5. 关系修复
```http
POST /departments/repair
```

#### 6. 同步日志
```http
GET /departments/logs?syncDate=2024-01-01
```

#### 7. 数据验证
```http
GET /departments/validate
```

#### 8. 清理数据
```http
DELETE /departments/clear
```

## 📊 使用步骤

### 第一步：清理测试环境
```bash
curl -X DELETE "http://localhost:8080/api/sync/test/departments/clear"
```

### 第二步：小范围测试
```bash
# 测试单天同步
curl -X POST "http://localhost:8080/api/sync/test/departments/daily?syncDate=2024-01-01"
```

### 第三步：查看统计结果
```bash
curl -X GET "http://localhost:8080/api/sync/test/departments/statistics"
```

### 第四步：验证数据完整性
```bash
curl -X GET "http://localhost:8080/api/sync/test/departments/validate"
```

### 第五步：扩大测试范围
```bash
# 测试一周的数据
curl -X POST "http://localhost:8080/api/sync/test/departments/full?startDate=2024-01-01&endDate=2024-01-07"
```

### 第六步：数据对比分析
```bash
curl -X GET "http://localhost:8080/api/sync/test/departments/compare"
```

## 📈 监控指标

### 同步统计
- `totalCount` - 总部门数量
- `normalCount` - 正常状态部门数量
- `historyCount` - 历史状态部门数量
- `deletedCount` - 删除标识部门数量
- `rootDepartmentCount` - 根部门数量
- `brokenRelationCount` - 关系异常部门数量

### 操作统计
- `insertCount` - 新增操作数量
- `updateCount` - 更新操作数量
- `deleteCount` - 删除操作数量
- `skipCount` - 跳过操作数量
- `repairedCount` - 修复操作数量

## ⚠️ 注意事项

### 1. **数据安全**
- 使用独立的测试表，不影响生产数据
- 所有操作都有详细日志记录
- 支持一键清理测试数据

### 2. **性能考虑**
- 按天循环同步，避免大批量操作
- 建议先小范围测试，确认无误后再扩大范围
- 监控同步过程中的性能指标

### 3. **错误处理**
- 单天同步失败不影响其他天的同步
- 详细的错误日志便于问题排查
- 支持重复执行（幂等性）

## 🔍 故障排查

### 查看同步日志
```sql
SELECT * FROM department_sync_operation_log 
WHERE sync_date = '2024-01-01' 
ORDER BY create_time DESC;
```

### 查看失败操作
```sql
SELECT * FROM department_sync_operation_log 
WHERE operation_result = 'FAILED' 
ORDER BY create_time DESC;
```

### 检查数据完整性
```sql
-- 检查父子关系异常
SELECT * FROM department_sync_test d1 
WHERE d1.parent_code IS NOT NULL 
AND d1.parent_code != '' 
AND d1.parent_code NOT IN (
    SELECT d2.org_code FROM department_sync_test d2
);
```

## 📝 测试建议

1. **从小范围开始**：先测试1-2天的数据
2. **逐步扩大**：确认无误后再测试更大范围
3. **对比验证**：与原有同步结果进行对比
4. **性能监控**：关注同步过程的性能表现
5. **日志分析**：定期检查同步日志，发现潜在问题
