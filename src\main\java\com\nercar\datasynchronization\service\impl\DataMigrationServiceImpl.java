//package com.nercar.datasynchronization.service.impl;
//
//import com.nercar.datasynchronization.entity.*;
//import com.nercar.datasynchronization.repository.*;
//import com.nercar.datasynchronization.service.DataMigrationService;
//import com.nercar.datasynchronization.utils.IDUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.data.jpa.repository.JpaRepository;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.text.SimpleDateFormat;
//import java.util.*;
//import java.util.concurrent.ExecutorService;
//import java.util.concurrent.Executors;
//import java.util.concurrent.Future;
//import java.util.stream.Collectors;
//
///**
// * 数据迁移服务实现类 - 负责将数据从旧表结构迁移到新表结构，并创建各实体间的关联关系
// */
//@Slf4j
//@Service
//public class DataMigrationServiceImpl implements DataMigrationService {
//
//    @Autowired
//    private EmployeeRepository employeeRepository;
//
//    @Autowired
//    private DepartmentRepository departmentRepository;
//
//    @Autowired
//    private HrUserDetailsRepository hrUserDetailsRepository;
//
//    @Autowired
//    private PositionInfoRepository positionInfoRepository;
//
//    @Autowired
//    private EmployeePositionRelationRepository employeePositionRelationRepository;
//
//    @Autowired
//    private DepartmentPositionRepository departmentPositionRepository;
//
//    @Autowired
//    private EmployeeDepartmentRepository employeeDepartmentRepository;
//
//    /**
//     * 初始化新表结构数据
//     */
//    @Override
//    @Transactional
//    public void initializeNewTables() {
//        log.info("开始初始化新表结构数据...");
//
//        try {
//            // 1. 先迁移岗位信息
//            migratePositionInfo();
//
////             2. 创建员工-部门关联
////            createEmployeeDepartmentRelations();
//
//            // 3. 创建部门-岗位关联
//            createDepartmentPositionRelations();
//
//            // 4. 创建员工-岗位关联
//            createEmployeePositionRelations();
//
//            log.info("所有表结构数据初始化完成");
//
//        } catch (Exception e) {
//            log.error("数据初始化过程中发生错误", e);
//            throw new RuntimeException("数据初始化失败: " + e.getMessage(), e);
//        }
//    }
//
//    /**
//     * 从hr_user_details表中提取岗位信息，并保存到position_info表
//     */
//    @Override
//    @Transactional
//    public void migratePositionInfo() {
//        log.info("开始迁移岗位信息...");
//
//        // 从hr_user_details表中提取所有不同的岗位名称
//        List<HrUserDetails> hrDetails = hrUserDetailsRepository.findAll();
//        log.info("从HR用户详情表中读取到{}条记录", hrDetails.size());
//
//        // 提取岗位并保存
//        Map<String, PositionInfo> positionMap = extractPositionInfo(hrDetails);
//        log.info("成功保存{}个岗位信息", positionMap.size());
//    }
//
//    /**
//     * 从HR用户详情中提取唯一岗位信息（增量方式）
//     */
//    @Override
//    public Map<String, PositionInfo> extractPositionInfo(List<HrUserDetails> details) {
//        log.info("开始提取唯一岗位信息");
//
//        Map<String, PositionInfo> positionMap = new HashMap<>();
//        int newCount = 0;
//        int updateCount = 0;
//        int skipCount = 0;
//
//        // 提取唯一岗位
//        for (HrUserDetails detail : details) {
//            if (detail.getPost() != null && !detail.getPost().trim().isEmpty()) {
//                String positionName = detail.getPost().trim();
//
//                if (!positionMap.containsKey(positionName)) {
//                    // 检查数据库中是否已存在此岗位
//                    String positionCode = "POS_" + Math.abs(positionName.hashCode());
//                    PositionInfo existingPosition = positionInfoRepository.findByPositionCode(positionCode);
//
//                    if (existingPosition != null) {
//                        // 岗位已存在，检查是否需要更新
//                        boolean needUpdate = false;
//
//                        // 统计使用此岗位的员工中最常见的技能等级
//                        Map<String, Integer> skillLevelCount = new HashMap<>();
//                        for (HrUserDetails d : details) {
//                            if (positionName.equals(d.getPost()) && d.getSkillLevel() != null) {
//                                String skillLevel = d.getSkillLevel();
//                                skillLevelCount.put(skillLevel, skillLevelCount.getOrDefault(skillLevel, 0) + 1);
//                            }
//                        }
//
//                        // 计算最常见的技能等级
//                        String mostCommonSkillLevel = skillLevelCount.entrySet().stream()
//                                .max(Map.Entry.comparingByValue())
//                                .map(Map.Entry::getKey)
//                                .orElse(null);
//
//                        // 检查是否需要更新技能等级
//                        if (mostCommonSkillLevel != null &&
//                                !mostCommonSkillLevel.equals(existingPosition.getSkillLevelCode())) {
//                            existingPosition.setSkillLevelCode(mostCommonSkillLevel);
//                            needUpdate = true;
//                        }
//
//                        // 如果需要更新，保存更新
//                        if (needUpdate) {
//                            existingPosition.setUpdatedTime(new Date());
//                            positionInfoRepository.save(existingPosition);
//                            updateCount++;
//                            log.debug("更新岗位 [{}] 信息", positionName);
//                        } else {
//                            skipCount++;
//                            log.debug("岗位 [{}] 无变化，跳过更新", positionName);
//                        }
//
//                        // 加入映射
//                        positionMap.put(positionName, existingPosition);
//                    } else {
//                        // 创建新的岗位记录
//                        PositionInfo position = new PositionInfo();
//                        position.setId(IDUtil.generateId());
//                        position.setPositionName(positionName);
//                        position.setPositionCode(positionCode);
//                        position.setStatus("ACTIVE");
//
//                        // 统计使用此岗位的员工中最常见的技能等级
//                        Map<String, Integer> skillLevelCount = new HashMap<>();
//                        for (HrUserDetails d : details) {
//                            if (positionName.equals(d.getPost()) && d.getSkillLevel() != null) {
//                                String skillLevel = d.getSkillLevel();
//                                skillLevelCount.put(skillLevel, skillLevelCount.getOrDefault(skillLevel, 0) + 1);
//                            }
//                        }
//
//                        // 如果有技能等级信息，使用出现最多的
//                        if (!skillLevelCount.isEmpty()) {
//                            String mostCommonSkillLevel = skillLevelCount.entrySet().stream()
//                                    .max(Map.Entry.comparingByValue())
//                                    .map(Map.Entry::getKey)
//                                    .orElse(null);
//
//                            position.setSkillLevelCode(mostCommonSkillLevel);
//                        } else {
//                            position.setSkillLevelCode(detail.getSkillLevel());
//                        }
//
//                        position.setCreatedTime(new Date());
//                        position.setUpdatedTime(new Date());
//
//                        // 保存新岗位
//                        positionInfoRepository.save(position);
//                        newCount++;
//
//                        // 加入映射
//                        positionMap.put(positionName, position);
//                    }
//                }
//            }
//        }
//
//        log.info("岗位信息处理完成: 新增 {} 个, 更新 {} 个, 跳过 {} 个", newCount, updateCount, skipCount);
//
//        return positionMap;
//    }
//
//    /**
//     * 创建员工与部门的关联关系 - 处理所有员工
//     * 注意：这个方法保留向后兼容性，新代码应使用带时间范围的方法
//     */
//    @Override
//    @Transactional
//    public void createEmployeeDepartmentRelations() {
//        log.info("开始创建员工-部门关联关系");
//
//        try {
//            // 先检查表是否已有数据，如果有则清空
//            long relationCount = employeeDepartmentRepository.count();
//            if (relationCount > 0) {
//                log.warn("员工-部门关联表已存在{}条数据，执行清空操作", relationCount);
//                employeeDepartmentRepository.deleteAll();
//                log.info("员工-部门关联表数据已清空");
//            }
//
//            // 查找所有员工
//            List<Employee> employees = employeeRepository.findAll();
//            log.info("找到 {} 名员工需要建立部门关联", employees.size());
//
//            List<EmployeeDepartment> relations = new ArrayList<>();
//            int successCount = 0;
//            int failedCount = 0;
//
//            for (Employee employee : employees) {
//                // 根据员工的orgCode查找对应部门
//                String orgCode = employee.getOrgCode();
//                if (orgCode == null || orgCode.isEmpty()) {
//                    log.warn("员工 [{}] 缺少部门代码，无法建立关联", employee.getEmployeeCode());
//                    failedCount++;
//                    continue;
//                }
//
//                Department department = departmentRepository.findByOrgCode(orgCode);
//                if (department == null) {
//                    log.warn("员工 [{}] 的部门代码 [{}] 未找到对应部门", employee.getEmployeeCode(), orgCode);
//                    failedCount++;
//                    continue;
//                }
//
//                // 创建关联记录
//                EmployeeDepartment relation = new EmployeeDepartment();
//                relation.setEmployeeId(employee.getId());
//                relation.setDepartmentId(department.getId());
//                relation.setRelationshipType("PRIMARY"); // 默认为主要部门关系
//                relation.setIsActive(1); // 设置为活动状态
//                relation.setCreatedTime(new Date());
//
//                relations.add(relation);
//                successCount++;
//
//                // 如果有上级部门，也可以建立次要关联
//                if (employee.getOrgLevel1() != null && !employee.getOrgLevel1().equals(orgCode)) {
//                    Department parentDept = departmentRepository.findByOrgCode(employee.getOrgLevel1());
//                    if (parentDept != null) {
//                        EmployeeDepartment secondaryRel = new EmployeeDepartment();
//                        secondaryRel.setEmployeeId(employee.getId());
//                        secondaryRel.setDepartmentId(parentDept.getId());
//                        secondaryRel.setRelationshipType("SECONDARY");
//                        secondaryRel.setIsActive(1);
//                        secondaryRel.setCreatedTime(new Date());
//                        relations.add(secondaryRel);
//                        successCount++;
//                    }
//                }
//
//                // 批量保存，避免一次性保存太多数据
//                if (relations.size() >= 500) {
//                    employeeDepartmentRepository.saveAll(relations);
//                    log.info("已保存 {} 条员工-部门关联关系", relations.size());
//                    relations.clear();
//                }
//            }
//
//            // 保存剩余的关联记录
//            if (!relations.isEmpty()) {
//                employeeDepartmentRepository.saveAll(relations);
//                log.info("已保存 {} 条员工-部门关联关系", relations.size());
//            }
//
//            log.info("员工-部门关联关系创建完成: 成功 {} 条, 失败 {} 条", successCount, failedCount);
//        } catch (Exception e) {
//            log.error("创建员工-部门关联关系失败", e);
//            throw new RuntimeException("创建员工-部门关联关系失败: " + e.getMessage(), e);
//        }
//    }
//
//    /**
//     * 创建员工与部门的关联关系 - 仅处理指定时间范围内的员工
//     * @param startTime 开始时间
//     * @param endTime 结束时间
//     */
//    @Override
//    @Transactional
//    public void createEmployeeDepartmentRelations(Date startTime, Date endTime) {
//        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        log.info("开始创建员工-部门关联关系，时间范围: {} 至 {}",
//                dateFormat.format(startTime), dateFormat.format(endTime));
//
//        try {
//            // 查找时间范围内新增或修改的员工
//            List<Employee> newEmployees = employeeRepository.findByCreatedTimeGreaterThanEqualAndCreatedTimeLessThanEqual(
//                    startTime, endTime);
//
//            // 查找时间范围内更新的员工
//            List<Employee> updatedEmployees = employeeRepository.findByUpdatedTimeGreaterThanEqualAndUpdatedTimeLessThanEqual(
//                    startTime, endTime);
//
//            // 合并两个列表，去重
//            Set<Long> processedIds = new HashSet<>();
//            List<Employee> employeesToProcess = new ArrayList<>();
//
//            for (Employee emp : newEmployees) {
//                if (!processedIds.contains(emp.getId())) {
//                    employeesToProcess.add(emp);
//                    processedIds.add(emp.getId());
//                }
//            }
//
//            for (Employee emp : updatedEmployees) {
//                if (!processedIds.contains(emp.getId())) {
//                    employeesToProcess.add(emp);
//                    processedIds.add(emp.getId());
//                }
//            }
//
//            log.info("在时间范围内找到 {} 名员工需要建立或更新部门关联", employeesToProcess.size());
//
//            if (employeesToProcess.isEmpty()) {
//                log.info("无需处理的员工数据，跳过关联创建");
//                return;
//            }
//
//            List<EmployeeDepartment> relations = new ArrayList<>();
//            int successCount = 0;
//            int failedCount = 0;
//            int existingCount = 0;
//
//            for (Employee employee : employeesToProcess) {
//                // 根据员工的orgCode查找对应部门
//                String orgCode = employee.getOrgCode();
//                if (orgCode == null || orgCode.isEmpty()) {
//                    log.warn("员工 [{}] 缺少部门代码，无法建立关联", employee.getEmployeeCode());
//                    failedCount++;
//                    continue;
//                }
//
//                Department department = departmentRepository.findByOrgCode(orgCode);
//                if (department == null) {
//                    log.warn("员工 [{}] 的部门代码 [{}] 未找到对应部门", employee.getEmployeeCode(), orgCode);
//                    failedCount++;
//                    continue;
//                }
//
//                // 检查是否已存在关联
//                boolean exists = employeeDepartmentRepository.existsByEmployeeIdAndDepartmentId(
//                        employee.getId(), department.getId());
//
//                if (!exists) {
//                    // 创建关联记录
//                    EmployeeDepartment relation = new EmployeeDepartment();
//                    relation.setEmployeeId(employee.getId());
//                    relation.setDepartmentId(department.getId());
//                    relation.setRelationshipType("PRIMARY"); // 默认为主要部门关系
//                    relation.setIsActive(1); // 设置为活动状态
//                    relation.setCreatedTime(new Date());
//
//                    relations.add(relation);
//                    successCount++;
//                } else {
//                    existingCount++;
//                }
//
//                // 如果有上级部门，也可以建立次要关联
//                if (employee.getOrgLevel1() != null && !employee.getOrgLevel1().equals(orgCode)) {
//                    Department parentDept = departmentRepository.findByOrgCode(employee.getOrgLevel1());
//                    if (parentDept != null) {
//                        // 检查是否已存在次要关联
//                        boolean parentExists = employeeDepartmentRepository.existsByEmployeeIdAndDepartmentId(
//                                employee.getId(), parentDept.getId());
//
//                        if (!parentExists) {
//                            EmployeeDepartment secondaryRel = new EmployeeDepartment();
//                            secondaryRel.setEmployeeId(employee.getId());
//                            secondaryRel.setDepartmentId(parentDept.getId());
//                            secondaryRel.setRelationshipType("SECONDARY");
//                            secondaryRel.setIsActive(1);
//                            secondaryRel.setCreatedTime(new Date());
//                            relations.add(secondaryRel);
//                            successCount++;
//                        } else {
//                            existingCount++;
//                        }
//                    }
//                }
//
//                // 批量保存，避免一次性保存太多数据
//                if (relations.size() >= 500) {
//                    employeeDepartmentRepository.saveAll(relations);
//                    log.info("已保存 {} 条员工-部门关联关系", relations.size());
//                    relations.clear();
//                }
//            }
//
//            // 保存剩余的关联记录
//            if (!relations.isEmpty()) {
//                employeeDepartmentRepository.saveAll(relations);
//                log.info("已保存 {} 条员工-部门关联关系", relations.size());
//            }
//
//            log.info("员工-部门关联关系创建完成: 创建 {} 条, 已存在 {} 条, 失败 {} 条",
//                    successCount, existingCount, failedCount);
//        } catch (Exception e) {
//            log.error("创建员工-部门关联关系失败", e);
//            throw new RuntimeException("创建员工-部门关联关系失败: " + e.getMessage(), e);
//        }
//    }
//
//    /**
//     * 从HR用户详情中提取岗位信息并创建关联关系 - 处理所有数据
//     * 采用增量更新方式，不删除现有数据
//     */
//    @Override
//    @Transactional
//    public void createPositionRelations() {
//        log.info("开始从HR用户详情中提取岗位信息并创建关联关系");
//
//        try {
//            // 查询所有HR用户详情
//            List<HrUserDetails> allDetails = hrUserDetailsRepository.findAll();
//            log.info("找到 {} 条HR用户详情记录", allDetails.size());
//
//            // 1. 提取并创建岗位信息（增量方式）
//            Map<String, PositionInfo> positionMap = extractPositionInfo(allDetails);
//
//            if (positionMap.isEmpty()) {
//                log.warn("未从HR用户详情中提取出任何岗位信息");
//                return;
//            }
//
//            log.info("从HR用户详情中提取出 {} 个唯一岗位", positionMap.size());
//
//            // 2. 创建员工-岗位关联关系（增量方式）
//            createEmployeePositionRelations(allDetails, positionMap);
//
//            // 3. 创建部门-岗位关联关系（增量方式）
//            createDepartmentPositionRelations(allDetails, positionMap);
//
//            log.info("岗位信息提取和关联关系建立完成");
//        } catch (Exception e) {
//            log.error("从HR用户详情中提取岗位信息并创建关联关系失败", e);
//            throw new RuntimeException("岗位关系建立失败: " + e.getMessage(), e);
//        }
//    }
//
//    /**
//     * 从HR用户详情中提取岗位信息并创建关联关系 - 处理指定时间范围内的数据
//     * 实际上忽略时间范围，处理所有岗位数据
//     * @param startTime 开始时间（仅用于记录）
//     * @param endTime 结束时间（仅用于记录）
//     */
//    @Override
//    @Transactional
//    public void createPositionRelations(Date startTime, Date endTime) {
//        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        log.info("开始从HR用户详情中提取岗位信息并创建关联关系，时间范围（仅供记录）: {} 至 {}",
//                dateFormat.format(startTime), dateFormat.format(endTime));
//
//        // 直接调用无时间限制的方法，处理所有岗位数据
//        createPositionRelations();
//    }
//
//    /**
//     * 创建员工岗位关联关系
//     */
//    @Override
//    @Transactional
//    public void createEmployeePositionRelations() {
//        log.info("开始创建员工-岗位关联关系");
//
//        try {
//            // 查询所有HR用户详情
//            List<HrUserDetails> allDetails = hrUserDetailsRepository.findAll();
//            log.info("找到 {} 条HR用户详情记录", allDetails.size());
//
//            // 获取所有岗位
//            List<PositionInfo> allPositions = positionInfoRepository.findAll();
//            Map<String, PositionInfo> positionMap = allPositions.stream()
//                    .collect(Collectors.toMap(PositionInfo::getPositionName, position -> position));
//
//            createEmployeePositionRelations(allDetails, positionMap);
//        } catch (Exception e) {
//            log.error("创建员工-岗位关联关系失败", e);
//            throw new RuntimeException("创建员工-岗位关联失败: " + e.getMessage(), e);
//        }
//    }
//
//    /**
//     * 创建员工与岗位的关联关系
//     */
//    /**
//     * 创建员工与岗位的关联关系
//     */
//    private void createEmployeePositionRelations(List<HrUserDetails> details, Map<String, PositionInfo> positionMap) {
//        log.info("开始创建员工-岗位关联关系");
//
//        // 预先加载所有员工，避免循环中的数据库查询
//        log.info("预加载员工数据...");
//        Map<String, Employee> employeeMap = employeeRepository.findAll().stream()
//                .filter(e -> e.getEmployeeCode() != null) // 过滤掉无工号的员工
//                .collect(Collectors.toMap(
//                        Employee::getEmployeeCode,
//                        e -> e,
//                        (e1, e2) -> e1) // 如果有重复的工号，保留第一个
//                );
//        log.info("预加载 {} 个员工记录", employeeMap.size());
//
//        // 预先加载所有部门，避免循环中的数据库查询
//        log.info("预加载部门数据...");
//        Map<String, Department> departmentMap = departmentRepository.findAll().stream()
//                .filter(d -> d.getOrgCode() != null) // 过滤掉无组织代码的部门
//                .collect(Collectors.toMap(
//                        Department::getOrgCode,
//                        d -> d,
//                        (d1, d2) -> d1) // 如果有重复的组织代码，保留第一个
//                );
//        log.info("预加载 {} 个部门记录", departmentMap.size());
//
//        // 预加载现有的员工-岗位关联关系
//        log.info("预加载现有员工-岗位关联关系...");
//        Map<String, EmployeePositionRelation> existingRelationsMap = new HashMap<>();
//        employeePositionRelationRepository.findAll().forEach(rel -> {
//            String key = rel.getEmployeeId() + "_" + rel.getPositionId();
//            existingRelationsMap.put(key, rel);
//        });
//        log.info("预加载 {} 个现有员工-岗位关联关系", existingRelationsMap.size());
//
//        List<EmployeePositionRelation> newRelations = new ArrayList<>();
//        List<EmployeePositionRelation> updateRelations = new ArrayList<>();
//        int successCount = 0;
//        int updatedCount = 0;
//        int skippedCount = 0;
//        int failedCount = 0;
//
//        for (HrUserDetails detail : details) {
//            if (detail.getPost() == null || detail.getUserNo() == null) {
//                continue;
//            }
//
//            String positionName = detail.getPost().trim();
//            PositionInfo position = positionMap.get(positionName);
//
//            if (position == null) {
//                log.warn("岗位 [{}] 未找到对应记录", positionName);
//                failedCount++;
//                continue;
//            }
//
//            // 查找员工 - 使用内存缓存
//            Employee employee = employeeMap.get(detail.getUserNo().trim());
//            if (employee == null) {
//                log.warn("员工工号 [{}] 未找到对应记录", detail.getUserNo());
//                failedCount++;
//                continue;
//            }
//
//            // 查找员工所在部门 - 使用内存缓存
//            Department department = departmentMap.get(employee.getOrgCode());
//            if (department == null) {
//                log.warn("员工 [{}] 的部门代码 [{}] 未找到对应部门", employee.getEmployeeCode(), employee.getOrgCode());
//                failedCount++;
//                continue;
//            }
//
//            // 检查是否已存在相同的关联记录 - 使用内存缓存
//            String relationKey = employee.getId() + "_" + position.getId();
//            EmployeePositionRelation existingRelation = existingRelationsMap.get(relationKey);
//
//            if (existingRelation != null) {
//                // 已存在关联记录，可以选择更新或跳过
//                boolean needUpdate = false;
//
//                // 更新工作经验等可能变化的字段
//                if (detail.getWorkExp() != null && !detail.getWorkExp().isEmpty()) {
//                    try {
//                        int workExp = Integer.parseInt(detail.getWorkExp());
//                        if (existingRelation.getWorkExp() == null || existingRelation.getWorkExp() != workExp) {
//                            existingRelation.setWorkExp(workExp);
//                            needUpdate = true;
//                        }
//                    } catch (NumberFormatException e) {
//                        log.warn("员工 [{}] 的工作经验 [{}] 格式错误", employee.getEmployeeCode(), detail.getWorkExp());
//                    }
//                }
//
//                if (needUpdate) {
//                    existingRelation.setUpdatedTime(new Date());
//                    updateRelations.add(existingRelation);
//                    updatedCount++;
//                } else {
//                    skippedCount++;
//                }
//            } else {
//                // 创建新的关联记录
//                EmployeePositionRelation relation = new EmployeePositionRelation();
//                relation.setId(Long.parseLong(IDUtil.generateId()));
//                relation.setEmployeeId(employee.getId());
//                relation.setEmployeeCode(employee.getEmployeeCode());
//                relation.setPositionId(position.getId());
//                relation.setDepartmentId(department.getId());
//                relation.setIsPrimary(1); // 设置为主要岗位
//
//                // 设置工作经验
//                if (detail.getWorkExp() != null && !detail.getWorkExp().isEmpty()) {
//                    try {
//                        relation.setWorkExp(Integer.parseInt(detail.getWorkExp()));
//                    } catch (NumberFormatException e) {
//                        log.warn("员工 [{}] 的工作经验 [{}] 格式错误", employee.getEmployeeCode(), detail.getWorkExp());
//                    }
//                }
//
//                relation.setCreatedTime(new Date());
//                newRelations.add(relation);
//                successCount++;
//            }
//        }
//
//        // 批量保存新关联
//        if (!newRelations.isEmpty()) {
//            // 分批保存以提高性能
//            batchSaveEntityList(newRelations, 500, employeePositionRelationRepository);
//            log.info("批量保存 {} 条新员工-岗位关联关系", newRelations.size());
//        }
//
//        // 批量更新关联
//        if (!updateRelations.isEmpty()) {
//            // 分批保存以提高性能
//            batchSaveEntityList(updateRelations, 500, employeePositionRelationRepository);
//            log.info("批量更新 {} 条员工-岗位关联关系", updateRelations.size());
//        }
//
//        log.info("员工-岗位关联关系处理完成: 新增 {} 条, 更新 {} 条, 跳过 {} 条, 失败 {} 条",
//                successCount, updatedCount, skippedCount, failedCount);
//    }
//
//    /**
//     * 批量保存实体列表的通用方法
//     */
//    private <T> void batchSaveEntityList(List<T> entities, int batchSize, JpaRepository<T, ?> repository) {
//        int totalSize = entities.size();
//        for (int i = 0; i < totalSize; i += batchSize) {
//            int endIndex = Math.min(i + batchSize, totalSize);
//            List<T> batch = entities.subList(i, endIndex);
//            repository.saveAll(batch);
//            log.debug("已保存批次 {}/{}, 大小: {}", (i / batchSize) + 1, (int) Math.ceil(totalSize / (double) batchSize), batch.size());
//        }
//    }
//
//    /**
//     * 仅创建部门-岗位关联
//     */
//    @Override
//    @Transactional
//    public void createDepartmentPositionRelations() {
//        log.info("开始创建部门-岗位关联关系");
//
//        try {
//            // 查询所有HR用户详情
//            List<HrUserDetails> allDetails = hrUserDetailsRepository.findAll();
//            log.info("找到 {} 条HR用户详情记录", allDetails.size());
//
//            // 获取所有岗位
//            List<PositionInfo> allPositions = positionInfoRepository.findAll();
//            Map<String, PositionInfo> positionMap = allPositions.stream()
//                    .collect(Collectors.toMap(PositionInfo::getPositionName, position -> position));
//
//            createDepartmentPositionRelations(allDetails, positionMap);
//        } catch (Exception e) {
//            log.error("创建部门-岗位关联关系失败", e);
//            throw new RuntimeException("创建部门-岗位关联失败: " + e.getMessage(), e);
//        }
//    }
//
//    /**
//     * 创建部门与岗位的关联关系
//     */
//    private void createDepartmentPositionRelations(List<HrUserDetails> details, Map<String, PositionInfo> positionMap) {
//        log.info("开始处理部门-岗位关联关系");
//
//        // 预先加载部门
//        Map<String, Department> departmentMapById = departmentRepository.findAll().stream()
//                .collect(Collectors.toMap(d -> String.valueOf(d.getId()), d -> d, (d1, d2) -> d1));
//        log.info("预加载 {} 个部门记录", departmentMapById.size());
//
//        // 预加载现有部门-岗位关联
//        Map<String, DepartmentPosition> existingRelationsMap = new HashMap<>();
//        departmentPositionRepository.findAll().forEach(rel -> {
//            String key = rel.getDepartmentId() + "_" + rel.getPositionId();
//            existingRelationsMap.put(key, rel);
//        });
//        log.info("预加载 {} 个现有部门-岗位关联关系", existingRelationsMap.size());
//
//        // 用于记录部门-岗位关系和计数
//        Map<String, Map<String, Integer>> deptPositionCount = new HashMap<>();
//
//        // 统计每个部门下各岗位的人数
//        for (HrUserDetails detail : details) {
//            if (detail.getPost() == null || detail.getUserNo() == null || detail.getDepartmentId() == null) {
//                continue;
//            }
//
//            String positionName = detail.getPost().trim();
//            String departmentId = detail.getDepartmentId().trim();
//
//            // 确保岗位存在
//            if (!positionMap.containsKey(positionName)) {
//                continue;
//            }
//
//            // 更新部门-岗位统计
//            if (!deptPositionCount.containsKey(departmentId)) {
//                deptPositionCount.put(departmentId, new HashMap<>());
//            }
//
//            Map<String, Integer> positionCounts = deptPositionCount.get(departmentId);
//            positionCounts.put(positionName, positionCounts.getOrDefault(positionName, 0) + 1);
//        }
//
//        // 创建或更新部门-岗位关联
//        List<DepartmentPosition> newRelations = new ArrayList<>();
//        List<DepartmentPosition> updateRelations = new ArrayList<>();
//        int createCount = 0;
//        int updateCount = 0;
//        int failedCount = 0;
//
//        for (Map.Entry<String, Map<String, Integer>> deptEntry : deptPositionCount.entrySet()) {
//            String departmentId = deptEntry.getKey();
//            Map<String, Integer> positionCounts = deptEntry.getValue();
//
//            // 假设departmentId是字符串形式的数值
//            Integer deptId;
//            try {
//                deptId = Integer.parseInt(departmentId);
//            } catch (NumberFormatException e) {
//                log.warn("部门ID [{}] 格式错误，跳过", departmentId);
//                failedCount += positionCounts.size();
//                continue;
//            }
//
//            // 检查部门是否存在
//            if (!departmentMapById.containsKey(String.valueOf(deptId)) && !departmentRepository.existsById(deptId)) {
//                log.warn("部门ID [{}] 在部门表中不存在，跳过", departmentId);
//                failedCount += positionCounts.size();
//                continue;
//            }
//
//            for (Map.Entry<String, Integer> posEntry : positionCounts.entrySet()) {
//                String positionName = posEntry.getKey();
//                Integer count = posEntry.getValue();
//
//                PositionInfo position = positionMap.get(positionName);
//                if (position == null) {
//                    log.warn("岗位 [{}] 未找到对应记录", positionName);
//                    failedCount++;
//                    continue;
//                }
//
//                // 检查是否已存在部门-岗位关联
//                String relationKey = deptId + "_" + position.getId();
//                DepartmentPosition existingRelation = existingRelationsMap.get(relationKey);
//
//                if (existingRelation != null) {
//                    // 更新已存在的关联
//                    boolean needUpdate = false;
//
//                    // 更新岗位人数限制
//                    if (existingRelation.getHeadcountLimit() == null || !existingRelation.getHeadcountLimit().equals(count)) {
//                        existingRelation.setHeadcountLimit(count);
//                        needUpdate = true;
//                    }
//
//                    if (needUpdate) {
//                        existingRelation.setUpdatedTime(new Date());
//                        updateRelations.add(existingRelation);
//                        updateCount++;
//                    }
//                } else {
//                    // 创建新关联
//                    DepartmentPosition relation = new DepartmentPosition();
//                    relation.setId(Long.parseLong(IDUtil.generateId()));
//                    relation.setDepartmentId(deptId);
//                    relation.setPositionId(position.getId());
//                    relation.setHeadcountLimit(count);
//                    relation.setIsActive(1); // 设置为活动状态
//                    relation.setCreatedTime(new Date());
//                    relation.setUpdatedTime(new Date());
//
//                    newRelations.add(relation);
//                    createCount++;
//                }
//            }
//        }
//
//        // 批量保存新关联
//        if (!newRelations.isEmpty()) {
//            batchSaveEntityList(newRelations, 500, departmentPositionRepository);
//            log.info("批量保存 {} 条新部门-岗位关联关系", newRelations.size());
//        }
//
//        // 批量更新关联
//        if (!updateRelations.isEmpty()) {
//            batchSaveEntityList(updateRelations, 500, departmentPositionRepository);
//            log.info("批量更新 {} 条部门-岗位关联关系", updateRelations.size());
//        }
//
//        log.info("部门-岗位关联关系处理完成: 新增 {} 条, 更新 {} 条, 失败 {} 条",
//                createCount, updateCount, failedCount);
//    }
//
//
//
//    /**
//     * 并行创建岗位关联关系 - 处理所有数据
//     */
//    @Override
//    @Transactional
//    public void createPositionRelationsParallel() {
//        log.info("开始并行处理岗位信息和关联关系");
//
//        try {
//            // 查询所有HR用户详情
//            List<HrUserDetails> allDetails = hrUserDetailsRepository.findAll();
//            log.info("找到 {} 条HR用户详情记录", allDetails.size());
//
//            // 1. 提取并创建岗位信息（增量方式）
//            Map<String, PositionInfo> positionMap = extractPositionInfo(allDetails);
//
//            if (positionMap.isEmpty()) {
//                log.warn("未从HR用户详情中提取出任何岗位信息");
//                return;
//            }
//
//            log.info("从HR用户详情中提取出 {} 个唯一岗位", positionMap.size());
//
//            // 2. 并行创建员工-岗位关联关系
//            createEmployeePositionRelationsParallel(allDetails, positionMap);
//
//            // 3. 并行创建部门-岗位关联关系
//            createDepartmentPositionRelationsParallel(allDetails, positionMap);
//
//            log.info("岗位信息提取和关联关系建立完成");
//        } catch (Exception e) {
//            log.error("并行处理岗位信息和关联关系失败", e);
//            throw new RuntimeException("岗位关系建立失败: " + e.getMessage(), e);
//        }
//    }
//
//    /**
//     * 并行创建员工-岗位关联关系
//     */
//    private void createEmployeePositionRelationsParallel(List<HrUserDetails> details, Map<String, PositionInfo> positionMap) {
//        log.info("开始并行创建员工-岗位关联关系");
//
//        // 确定并行度
//        int parallelism = Runtime.getRuntime().availableProcessors();
//        log.info("使用 {} 个线程并行处理员工-岗位关联", parallelism);
//
//        // 预先加载所有员工
//        Map<String, Employee> employeeMap = employeeRepository.findAll().stream()
//                .filter(e -> e.getEmployeeCode() != null)
//                .collect(Collectors.toMap(
//                        Employee::getEmployeeCode,
//                        e -> e,
//                        (e1, e2) -> e1
//                ));
//        log.info("预加载 {} 个员工记录", employeeMap.size());
//
//        // 预先加载所有部门
//        Map<String, Department> departmentMap = departmentRepository.findAll().stream()
//                .filter(d -> d.getOrgCode() != null)
//                .collect(Collectors.toMap(
//                        Department::getOrgCode,
//                        d -> d,
//                        (d1, d2) -> d1
//                ));
//        log.info("预加载 {} 个部门记录", departmentMap.size());
//
//        // 预加载现有的员工-岗位关联关系
//        Map<String, EmployeePositionRelation> existingRelationsMap = new HashMap<>();
//        employeePositionRelationRepository.findAll().forEach(rel -> {
//            String key = rel.getEmployeeId() + "_" + rel.getPositionId();
//            existingRelationsMap.put(key, rel);
//        });
//        log.info("预加载 {} 个现有员工-岗位关联关系", existingRelationsMap.size());
//
//        // 将数据分成多个批次
//        int batchSize = (int) Math.ceil(details.size() / (double) parallelism);
//        List<List<HrUserDetails>> batches = new ArrayList<>();
//        for (int i = 0; i < details.size(); i += batchSize) {
//            batches.add(details.subList(i, Math.min(i + batchSize, details.size())));
//        }
//        log.info("将 {} 条记录分成 {} 批处理", details.size(), batches.size());
//
//        // 创建线程池
//        ExecutorService executor = Executors.newFixedThreadPool(parallelism);
//
//        // 用于收集结果的列表
//        List<Future<ProcessResult>> futures = new ArrayList<>();
//
//        // 提交任务
//        for (int i = 0; i < batches.size(); i++) {
//            final int batchIndex = i;
//            List<HrUserDetails> batch = batches.get(i);
//
//            futures.add(executor.submit(() -> processEmployeePositionBatch(
//                    batch, positionMap, employeeMap, departmentMap, existingRelationsMap, batchIndex)));
//        }
//
//        // 收集结果
//        List<EmployeePositionRelation> newRelations = new ArrayList<>();
//        List<EmployeePositionRelation> updateRelations = new ArrayList<>();
//        int successCount = 0;
//        int updatedCount = 0;
//        int skippedCount = 0;
//        int failedCount = 0;
//
//        for (Future<ProcessResult> future : futures) {
//            try {
//                ProcessResult result = future.get();
//                newRelations.addAll(result.getNewRelations());
//                updateRelations.addAll(result.getUpdateRelations());
//                successCount += result.getSuccessCount();
//                updatedCount += result.getUpdatedCount();
//                skippedCount += result.getSkippedCount();
//                failedCount += result.getFailedCount();
//            } catch (Exception e) {
//                log.error("获取线程处理结果失败", e);
//            }
//        }
//
//        // 关闭线程池
//        executor.shutdown();
//
//        // 批量保存新关联
//        if (!newRelations.isEmpty()) {
//            batchSaveEntityList(newRelations, 500, employeePositionRelationRepository);
//            log.info("批量保存 {} 条新员工-岗位关联关系", newRelations.size());
//        }
//
//        // 批量更新关联
//        if (!updateRelations.isEmpty()) {
//            batchSaveEntityList(updateRelations, 500, employeePositionRelationRepository);
//            log.info("批量更新 {} 条员工-岗位关联关系", updateRelations.size());
//        }
//
//        log.info("员工-岗位关联关系处理完成: 新增 {} 条, 更新 {} 条, 跳过 {} 条, 失败 {} 条",
//                successCount, updatedCount, skippedCount, failedCount);
//    }
//
//    /**
//     * 处理员工-岗位关联批次
//     */
//    private ProcessResult processEmployeePositionBatch(
//            List<HrUserDetails> batch,
//            Map<String, PositionInfo> positionMap,
//            Map<String, Employee> employeeMap,
//            Map<String, Department> departmentMap,
//            Map<String, EmployeePositionRelation> existingRelationsMap,
//            int batchIndex) {
//
//        log.info("批次 {} 开始处理 {} 条记录", batchIndex, batch.size());
//
//        List<EmployeePositionRelation> newRelations = new ArrayList<>();
//        List<EmployeePositionRelation> updateRelations = new ArrayList<>();
//        int successCount = 0;
//        int updatedCount = 0;
//        int skippedCount = 0;
//        int failedCount = 0;
//
//        for (HrUserDetails detail : batch) {
//            if (detail.getPost() == null || detail.getUserNo() == null) {
//                continue;
//            }
//
//            String positionName = detail.getPost().trim();
//            PositionInfo position = positionMap.get(positionName);
//
//            if (position == null) {
//                log.warn("岗位 [{}] 未找到对应记录", positionName);
//                failedCount++;
//                continue;
//            }
//
//            // 查找员工 - 使用内存缓存
//            Employee employee = employeeMap.get(detail.getUserNo().trim());
//            if (employee == null) {
//                log.warn("员工工号 [{}] 未找到对应记录", detail.getUserNo());
//                failedCount++;
//                continue;
//            }
//
//            // 查找员工所在部门 - 使用内存缓存
//            Department department = departmentMap.get(employee.getOrgCode());
//            if (department == null) {
//                log.warn("员工 [{}] 的部门代码 [{}] 未找到对应部门", employee.getEmployeeCode(), employee.getOrgCode());
//                failedCount++;
//                continue;
//            }
//
//            // 检查是否已存在相同的关联记录 - 使用内存缓存
//            String relationKey = employee.getId() + "_" + position.getId();
//            EmployeePositionRelation existingRelation = existingRelationsMap.get(relationKey);
//
//            if (existingRelation != null) {
//                // 已存在关联记录，可以选择更新或跳过
//                boolean needUpdate = false;
//
//                // 更新工作经验等可能变化的字段
//                if (detail.getWorkExp() != null && !detail.getWorkExp().isEmpty()) {
//                    try {
//                        int workExp = Integer.parseInt(detail.getWorkExp());
//                        if (existingRelation.getWorkExp() == null || existingRelation.getWorkExp() != workExp) {
//                            existingRelation.setWorkExp(workExp);
//                            needUpdate = true;
//                        }
//                    } catch (NumberFormatException e) {
//                        log.warn("员工 [{}] 的工作经验 [{}] 格式错误", employee.getEmployeeCode(), detail.getWorkExp());
//                    }
//                }
//
//                if (needUpdate) {
//                    existingRelation.setUpdatedTime(new Date());
//                    updateRelations.add(existingRelation);
//                    updatedCount++;
//                } else {
//                    skippedCount++;
//                }
//            } else {
//                // 创建新的关联记录
//                EmployeePositionRelation relation = new EmployeePositionRelation();
//                relation.setId(Long.parseLong(IDUtil.generateId()));
//                relation.setEmployeeId(employee.getId());
//                relation.setEmployeeCode(employee.getEmployeeCode());
//                relation.setPositionId(position.getId());
//                relation.setDepartmentId(department.getId());
//                relation.setIsPrimary(1); // 设置为主要岗位
//
//                // 设置工作经验
//                if (detail.getWorkExp() != null && !detail.getWorkExp().isEmpty()) {
//                    try {
//                        relation.setWorkExp(Integer.parseInt(detail.getWorkExp()));
//                    } catch (NumberFormatException e) {
//                        log.warn("员工 [{}] 的工作经验 [{}] 格式错误", employee.getEmployeeCode(), detail.getWorkExp());
//                    }
//                }
//
//                relation.setCreatedTime(new Date());
//                newRelations.add(relation);
//                successCount++;
//            }
//        }
//
//        log.info("批次 {} 处理完成: 新增 {}, 更新 {}, 跳过 {}, 失败 {}",
//                batchIndex, successCount, updatedCount, skippedCount, failedCount);
//
//        return new ProcessResult(newRelations, updateRelations, successCount, updatedCount, skippedCount, failedCount);
//    }
//
//    /**
//     * 处理结果类
//     */
//    private static class ProcessResult {
//        private final List<EmployeePositionRelation> newRelations;
//        private final List<EmployeePositionRelation> updateRelations;
//        private final int successCount;
//        private final int updatedCount;
//        private final int skippedCount;
//        private final int failedCount;
//
//        public ProcessResult(
//                List<EmployeePositionRelation> newRelations,
//                List<EmployeePositionRelation> updateRelations,
//                int successCount,
//                int updatedCount,
//                int skippedCount,
//                int failedCount) {
//            this.newRelations = newRelations;
//            this.updateRelations = updateRelations;
//            this.successCount = successCount;
//            this.updatedCount = updatedCount;
//            this.skippedCount = skippedCount;
//            this.failedCount = failedCount;
//        }
//
//        public List<EmployeePositionRelation> getNewRelations() {
//            return newRelations;
//        }
//
//        public List<EmployeePositionRelation> getUpdateRelations() {
//            return updateRelations;
//        }
//
//        public int getSuccessCount() {
//            return successCount;
//        }
//
//        public int getUpdatedCount() {
//            return updatedCount;
//        }
//
//        public int getSkippedCount() {
//            return skippedCount;
//        }
//
//        public int getFailedCount() {
//            return failedCount;
//        }
//    }
//
//    /**
//     * 并行创建部门-岗位关联关系
//     */
//    private void createDepartmentPositionRelationsParallel(List<HrUserDetails> details, Map<String, PositionInfo> positionMap) {
//        log.info("开始并行创建部门-岗位关联关系");
//
//        // 确定并行度
//        int parallelism = Runtime.getRuntime().availableProcessors();
//        log.info("使用 {} 个线程并行处理部门-岗位关联", parallelism);
//
//        // 预先加载部门
//        Map<String, Department> departmentMapById = departmentRepository.findAll().stream()
//                .collect(Collectors.toMap(d -> String.valueOf(d.getId()), d -> d, (d1, d2) -> d1));
//        log.info("预加载 {} 个部门记录", departmentMapById.size());
//
//        // 预加载现有部门-岗位关联
//        Map<String, DepartmentPosition> existingRelationsMap = new HashMap<>();
//        departmentPositionRepository.findAll().forEach(rel -> {
//            String key = rel.getDepartmentId() + "_" + rel.getPositionId();
//            existingRelationsMap.put(key, rel);
//        });
//        log.info("预加载 {} 个现有部门-岗位关联关系", existingRelationsMap.size());
//
//        // 用于记录部门-岗位关系和计数
//        Map<String, Map<String, Integer>> deptPositionCount = new HashMap<>();
//
//        // 统计每个部门下各岗位的人数
//        for (HrUserDetails detail : details) {
//            if (detail.getPost() == null || detail.getUserNo() == null || detail.getDepartmentId() == null) {
//                continue;
//            }
//
//            String positionName = detail.getPost().trim();
//            String departmentId = detail.getDepartmentId().trim();
//
//            // 确保岗位存在
//            if (!positionMap.containsKey(positionName)) {
//                continue;
//            }
//
//            // 更新部门-岗位统计
//            if (!deptPositionCount.containsKey(departmentId)) {
//                deptPositionCount.put(departmentId, new HashMap<>());
//            }
//
//            Map<String, Integer> positionCounts = deptPositionCount.get(departmentId);
//            positionCounts.put(positionName, positionCounts.getOrDefault(positionName, 0) + 1);
//        }
//
//        // 将数据分成多个批次
//        List<Map.Entry<String, Map<String, Integer>>> entries = new ArrayList<>(deptPositionCount.entrySet());
//        int entryCount = entries.size();
//        int batchSize = (int) Math.ceil(entryCount / (double) parallelism);
//        List<List<Map.Entry<String, Map<String, Integer>>>> batches = new ArrayList<>();
//
//        for (int i = 0; i < entryCount; i += batchSize) {
//            batches.add(entries.subList(i, Math.min(i + batchSize, entryCount)));
//        }
//        log.info("将 {} 个部门-岗位关系分成 {} 批处理", entryCount, batches.size());
//
//        // 创建线程池
//        ExecutorService executor = Executors.newFixedThreadPool(parallelism);
//
//        // 用于收集结果的列表
//        List<Future<DepartmentPositionResult>> futures = new ArrayList<>();
//
//        // 提交任务
//        for (int i = 0; i < batches.size(); i++) {
//            final int batchIndex = i;
//            List<Map.Entry<String, Map<String, Integer>>> batch = batches.get(i);
//
//            futures.add(executor.submit(() -> processDepartmentPositionBatch(
//                    batch, positionMap, departmentMapById, existingRelationsMap, batchIndex)));
//        }
//
//        // 收集结果
//        List<DepartmentPosition> newRelations = new ArrayList<>();
//        List<DepartmentPosition> updateRelations = new ArrayList<>();
//        int createCount = 0;
//        int updateCount = 0;
//        int failedCount = 0;
//
//        for (Future<DepartmentPositionResult> future : futures) {
//            try {
//                DepartmentPositionResult result = future.get();
//                newRelations.addAll(result.getNewRelations());
//                updateRelations.addAll(result.getUpdateRelations());
//                createCount += result.getCreateCount();
//                updateCount += result.getUpdateCount();
//                failedCount += result.getFailedCount();
//            } catch (Exception e) {
//                log.error("获取线程处理结果失败", e);
//            }
//        }
//
//        // 关闭线程池
//        executor.shutdown();
//
//        // 批量保存新关联
//        if (!newRelations.isEmpty()) {
//            batchSaveEntityList(newRelations, 500, departmentPositionRepository);
//            log.info("批量保存 {} 条新部门-岗位关联关系", newRelations.size());
//        }
//
//        // 批量更新关联
//        if (!updateRelations.isEmpty()) {
//            batchSaveEntityList(updateRelations, 500, departmentPositionRepository);
//            log.info("批量更新 {} 条部门-岗位关联关系", updateRelations.size());
//        }
//
//        log.info("部门-岗位关联关系处理完成: 新增 {} 条, 更新 {} 条, 失败 {} 条",
//                createCount, updateCount, failedCount);
//    }
//
//    /**
//     * 处理部门-岗位关联批次
//     */
//    private DepartmentPositionResult processDepartmentPositionBatch(
//            List<Map.Entry<String, Map<String, Integer>>> batch,
//            Map<String, PositionInfo> positionMap,
//            Map<String, Department> departmentMapById,
//            Map<String, DepartmentPosition> existingRelationsMap,
//            int batchIndex) {
//
//        log.info("批次 {} 开始处理 {} 个部门的岗位关联", batchIndex, batch.size());
//
//        List<DepartmentPosition> newRelations = new ArrayList<>();
//        List<DepartmentPosition> updateRelations = new ArrayList<>();
//        int createCount = 0;
//        int updateCount = 0;
//        int failedCount = 0;
//
//        for (Map.Entry<String, Map<String, Integer>> deptEntry : batch) {
//            String departmentId = deptEntry.getKey();
//            Map<String, Integer> positionCounts = deptEntry.getValue();
//
//            // 假设departmentId是字符串形式的数值
//            Integer deptId;
//            try {
//                deptId = Integer.parseInt(departmentId);
//            } catch (NumberFormatException e) {
//                log.warn("部门ID [{}] 格式错误，跳过", departmentId);
//                failedCount += positionCounts.size();
//                continue;
//            }
//
//            // 检查部门是否存在
//            if (!departmentMapById.containsKey(String.valueOf(deptId)) && !departmentRepository.existsById(deptId)) {
//                log.warn("部门ID [{}] 在部门表中不存在，跳过", departmentId);
//                failedCount += positionCounts.size();
//                continue;
//            }
//
//            for (Map.Entry<String, Integer> posEntry : positionCounts.entrySet()) {
//                String positionName = posEntry.getKey();
//                Integer count = posEntry.getValue();
//
//                PositionInfo position = positionMap.get(positionName);
//                if (position == null) {
//                    log.warn("岗位 [{}] 未找到对应记录", positionName);
//                    failedCount++;
//                    continue;
//                }
//
//                // 检查是否已存在部门-岗位关联
//                String relationKey = deptId + "_" + position.getId();
//                DepartmentPosition existingRelation = existingRelationsMap.get(relationKey);
//
//                if (existingRelation != null) {
//                    // 更新已存在的关联
//                    boolean needUpdate = false;
//
//                    // 更新岗位人数限制
//                    if (existingRelation.getHeadcountLimit() == null || !existingRelation.getHeadcountLimit().equals(count)) {
//                        existingRelation.setHeadcountLimit(count);
//                        needUpdate = true;
//                    }
//
//                    if (needUpdate) {
//                        existingRelation.setUpdatedTime(new Date());
//                        updateRelations.add(existingRelation);
//                        updateCount++;
//                    }
//                } else {
//                    // 创建新关联
//                    DepartmentPosition relation = new DepartmentPosition();
//                    relation.setId(Long.parseLong(IDUtil.generateId()));
//                    relation.setDepartmentId(deptId);
//                    relation.setPositionId(position.getId());
//                    relation.setHeadcountLimit(count);
//                    relation.setIsActive(1); // 设置为活动状态
//                    relation.setCreatedTime(new Date());
//                    relation.setUpdatedTime(new Date());
//
//                    newRelations.add(relation);
//                    createCount++;
//                }
//            }
//        }
//
//        log.info("批次 {} 处理完成: 新增 {}, 更新 {}, 失败 {}",
//                batchIndex, createCount, updateCount, failedCount);
//
//        return new DepartmentPositionResult(newRelations, updateRelations, createCount, updateCount, failedCount);
//    }
//
//    /**
//     * 部门-岗位处理结果类
//     */
//    private static class DepartmentPositionResult {
//        private final List<DepartmentPosition> newRelations;
//        private final List<DepartmentPosition> updateRelations;
//        private final int createCount;
//        private final int updateCount;
//        private final int failedCount;
//
//        public DepartmentPositionResult(
//                List<DepartmentPosition> newRelations,
//                List<DepartmentPosition> updateRelations,
//                int createCount,
//                int updateCount,
//                int failedCount) {
//            this.newRelations = newRelations;
//            this.updateRelations = updateRelations;
//            this.createCount = createCount;
//            this.updateCount = updateCount;
//            this.failedCount = failedCount;
//        }
//
//        public List<DepartmentPosition> getNewRelations() {
//            return newRelations;
//        }
//
//        public List<DepartmentPosition> getUpdateRelations() {
//            return updateRelations;
//        }
//
//        public int getCreateCount() {
//            return createCount;
//        }
//
//        public int getUpdateCount() {
//            return updateCount;
//        }
//
//        public int getFailedCount() {
//            return failedCount;
//        }
//    }
//
//
//}