package com.nercar.datasynchronization.repository;

import com.nercar.datasynchronization.entity.HrUserDetails;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface HrUserDetailsRepository extends JpaRepository<HrUserDetails, String> {

    /**
     * 根据用户工号查找用户详情
     */
    HrUserDetails findByUserNo(String userNo);
    
    /**
     * 根据部门ID查找用户详情列表
     */
    List<HrUserDetails> findByDepartmentId(String departmentId);
    
    /**
     * 根据部门ID和岗位名称查找用户详情
     */
    List<HrUserDetails> findByDepartmentIdAndPost(String departmentId, String post);
    
    /**
     * 根据部门ID和岗位名称统计人数
     */
    long countByDepartmentIdAndPost(String departmentId, String post);
    
    /**
     * 根据名称查找用户详情
     */
    List<HrUserDetails> findByName(String name);
    
    /**
     * 根据名称模糊查询
     */
    List<HrUserDetails> findByNameContaining(String nameKeyword);
}