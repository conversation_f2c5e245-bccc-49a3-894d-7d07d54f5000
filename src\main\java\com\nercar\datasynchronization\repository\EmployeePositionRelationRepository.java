package com.nercar.datasynchronization.repository;

import com.nercar.datasynchronization.entity.EmployeePositionRelation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EmployeePositionRelationRepository extends JpaRepository<EmployeePositionRelation, Long> {
    
    List<EmployeePositionRelation> findByEmployeeId(Long employeeId);
    
    List<EmployeePositionRelation> findByEmployeeCode(String employeeCode);
    
    List<EmployeePositionRelation> findByPositionCode(String positionCode);
    
    EmployeePositionRelation findByEmployeeCodeAndPositionCode(String employeeCode, String positionCode);
}