package com.nercar.datasynchronization.service;

import com.nercar.datasynchronization.dto.DepartmentDataDTO;
import com.nercar.datasynchronization.dto.EmployeeDataDTO;

import java.util.Date;
import java.util.List;

/**
 * 数据获取服务接口
 * 用于为外部系统提供数据获取功能，不涉及数据库操作
 */
public interface DataRetrievalService {
    
    /**
     * 获取部门数据
     * 从远程SOAP接口获取部门数据并解析为DTO对象
     * 
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 部门数据列表
     */
    List<DepartmentDataDTO> getDepartmentData(Date startDate, Date endDate);
    
    /**
     * 获取员工数据
     * 从远程SOAP接口获取员工数据并解析为DTO对象
     * 
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 员工数据列表
     */
    List<EmployeeDataDTO> getEmployeeData(Date startDate, Date endDate);
}
