CREATE TABLE `employee_system` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `employee_mdm_id` varchar(50) DEFAULT NULL,
  `ngtyysb_udef9` varchar(255) DEFAULT NULL,
  `ngtyysb_lyxtdatanm` varchar(255) DEFAULT NULL,
  `ngtyysb_udef6` varchar(255) DEFAULT NULL,
  `ngtyysb_guid` varchar(255) DEFAULT NULL,
  `ngtyysb_udef5` varchar(255) DEFAULT NULL,
  `ngtyysb_udef8` varchar(255) DEFAULT NULL,
  `ngtyysb_udef7` varchar(255) DEFAULT NULL,
  `ngtyysb_lyxtbh` varchar(255) DEFAULT NULL,
  `ngtyysb_udef13` varchar(255) DEFAULT NULL,
  `ngtyysb_udef12` varchar(255) DEFAULT NULL,
  `ngtyysb_udef11` varchar(255) DEFAULT NULL,
  `ngtyysb_udef10` varchar(255) DEFAULT NULL,
  `ngtyysb_udef2` varchar(255) DEFAULT NULL,
  `ngtyysb_udef1` varchar(255) DEFAULT NULL,
  `ngtyysb_udef4` varchar(255) DEFAULT NULL,
  `ngtyysb_udef3` varchar(255) DEFAULT NULL,
  `ngtyysb_udef15` varchar(255) DEFAULT NULL,
  `ngtyysb_zbnm` varchar(255) DEFAULT NULL,
  `ngtyysb_udef14` varchar(255) DEFAULT NULL,
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  INDEX `idx_employee_mdm_id` (`employee_mdm_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
