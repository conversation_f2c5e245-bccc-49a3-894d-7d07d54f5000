package com.nercar.datasynchronization.repository;

import com.nercar.datasynchronization.entity.DepartmentPosition;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DepartmentPositionRepository extends JpaRepository<DepartmentPosition, Long> {
    
    List<DepartmentPosition> findByDepartmentId(Integer departmentId);
    
    List<DepartmentPosition> findByPositionCode(String positionCode);
    
    DepartmentPosition findByDepartmentCodeAndPositionCode(String departmentCode, String positionCode);
}