package com.nercar.datasynchronization.service;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 部门同步测试服务接口
 * 基于ERP同步逻辑的部门同步服务
 */
public interface DepartmentSyncTestService {

    /**
     * 全量同步：按天循环（参考ERP逻辑）
     * 从指定开始日期到结束日期，逐天进行同步
     * 
     * @param startDate 开始日期 (yyyy-MM-dd)
     * @param endDate 结束日期 (yyyy-MM-dd)
     * @return 同步结果统计
     */
    Map<String, Object> syncAllDepartmentsByDay(String startDate, String endDate);

    /**
     * 单天增量同步（核心逻辑）
     * 参考ERP代码的状态感知处理逻辑
     * 
     * @param syncDate 同步日期 (yyyy-MM-dd)
     * @return 同步结果统计
     */
    Map<String, Object> syncDepartmentsByDay(String syncDate);

    /**
     * 特殊数据处理（参考ERP的根节点处理）
     * 删除特殊组织编码的部门，如 org_code="X"
     * 
     * @return 被删除的部门组织编码列表
     */
    List<String> handleSpecialDepartments();

    /**
     * 父子关系修复（参考ERP的关系修复）
     * 修复父级部门不存在的异常关系
     * 
     * @param deletedOrgCodes 已删除的部门组织编码列表
     * @return 修复的部门数量
     */
    int repairDepartmentRelations(List<String> deletedOrgCodes);

    /**
     * 数据统计和对比
     * 
     * @return 统计信息
     */
    Map<String, Object> getDataStatistics();

    /**
     * 对比测试表与原表的数据差异
     * 
     * @return 对比结果
     */
    Map<String, Object> compareWithOriginalData();

    /**
     * 获取同步操作日志统计
     * 
     * @param syncDate 同步日期
     * @return 日志统计信息
     */
    Map<String, Object> getSyncLogStatistics(String syncDate);

    /**
     * 清理测试数据
     * 清空测试表和日志表的数据
     */
    void clearTestData();

    /**
     * 验证数据完整性
     * 检查层级关系、数据一致性等
     * 
     * @return 验证结果
     */
    Map<String, Object> validateDataIntegrity();
}
