package com.nercar.datasynchronization.service;

/**
 * 组织架构迁移服务接口
 * 负责将MySQL中的department表数据迁移到PostgreSQL中的t_org_structure表
 */
public interface OrgStructureMigrationService {

    /**
     * 执行组织架构数据迁移
     * 将department表中的正常状态部门数据迁移到t_org_structure表
     */
    void migrateToOrgStructure();

    /**
     * 执行组织架构数据迁移
     * @param clearExisting 是否清理现有的同步数据（data_source=2的记录）
     */
    void migrateToOrgStructure(boolean clearExisting);

    /**
     * 清理组织架构同步数据
     * 删除data_source=2的所有记录，保留手工录入的数据
     */
    void clearOrgStructureData();

    /**
     * 验证迁移结果
     * 检查数据完整性和层级关系正确性
     * @return 迁移的记录数，如果验证失败返回负数
     */
    int validateMigrationResult();

    /**
     * 获取迁移统计信息
     * @return 包含迁移统计信息的字符串
     */
    String getMigrationStatistics();

    /**
     * 检查PostgreSQL连接状态
     * @return true表示连接正常，false表示连接异常
     */
    boolean checkPostgreSQLConnection();
}
