//package com.nercar.datasynchronization.controller;
//
//import com.nercar.datasynchronization.entity.PositionInfo;
//import com.nercar.datasynchronization.service.PositionService;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.http.ResponseEntity;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
///**
// * 岗位管理控制器
// */
//@Slf4j
//@RestController
//@RequestMapping("/api/position")
//public class PositionController {
//
//    @Autowired
//    private PositionService positionService;
//
//    /**
//     * 获取所有岗位
//     */
//    @GetMapping
//    public ResponseEntity<List<PositionInfo>> getAllPositions() {
//        log.info("接收到获取所有岗位的请求");
//        List<PositionInfo> positions = positionService.getAllPositions();
//        return ResponseEntity.ok(positions);
//    }
//
//    /**
//     * 根据ID获取岗位
//     */
//    @GetMapping("/{id}")
//    public ResponseEntity<PositionInfo> getPositionById(@PathVariable String id) {
//        log.info("接收到获取岗位的请求，ID: {}", id);
//
//        PositionInfo position = positionService.getPositionById(id);
//        if (position == null) {
//            return ResponseEntity.notFound().build();
//        }
//
//        return ResponseEntity.ok(position);
//    }
//
//    /**
//     * 新增岗位
//     */
//    @PostMapping
//    public ResponseEntity<PositionInfo> createPosition(@RequestBody PositionInfo position) {
//        log.info("接收到新增岗位的请求");
//        PositionInfo createdPosition = positionService.createPosition(position);
//        return ResponseEntity.ok(createdPosition);
//    }
//
//    /**
//     * 更新岗位
//     */
//    @PutMapping("/{id}")
//    public ResponseEntity<PositionInfo> updatePosition(
//            @PathVariable String id,
//            @RequestBody PositionInfo position) {
//        log.info("接收到更新岗位的请求，ID: {}", id);
//
//        position.setId(id);
//        PositionInfo updatedPosition = positionService.updatePosition(position);
//
//        if (updatedPosition == null) {
//            return ResponseEntity.notFound().build();
//        }
//
//        return ResponseEntity.ok(updatedPosition);
//    }
//
//    /**
//     * 删除岗位
//     */
//    @DeleteMapping("/{id}")
//    public ResponseEntity<Map<String, Object>> deletePosition(@PathVariable String id) {
//        log.info("接收到删除岗位的请求，ID: {}", id);
//
//        Map<String, Object> response = new HashMap<>();
//
//        boolean deleted = positionService.deletePosition(id);
//        if (!deleted) {
//            response.put("success", false);
//            response.put("message", "岗位不存在或无法删除");
//            return ResponseEntity.badRequest().body(response);
//        }
//
//        response.put("success", true);
//        response.put("message", "岗位删除成功");
//        return ResponseEntity.ok(response);
//    }
//
//    /**
//     * 根据部门ID获取该部门下的所有岗位
//     */
//    @GetMapping("/by-department/{departmentId}")
//    public ResponseEntity<List<PositionInfo>> getPositionsByDepartment(@PathVariable Integer departmentId) {
//        log.info("接收到获取部门下所有岗位的请求，部门ID: {}", departmentId);
//
//        List<PositionInfo> positions = positionService.getPositionsByDepartmentId(departmentId);
//        return ResponseEntity.ok(positions);
//    }
//
//    /**
//     * 根据员工ID获取该员工的所有岗位
//     */
//    @GetMapping("/by-employee/{employeeId}")
//    public ResponseEntity<List<PositionInfo>> getPositionsByEmployee(@PathVariable Long employeeId) {
//        log.info("接收到获取员工所有岗位的请求，员工ID: {}", employeeId);
//
//        List<PositionInfo> positions = positionService.getPositionsByEmployeeId(employeeId);
//        return ResponseEntity.ok(positions);
//    }
//}