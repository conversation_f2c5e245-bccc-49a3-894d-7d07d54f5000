package com.nercar.datasynchronization.mapper;

import com.nercar.datasynchronization.entity.HrUserDetails;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 通用Mapper接口
 */
@Mapper
public interface CommonMapper {
    /**
     * 批量插入HR用户详情
     * @param list 用户详情列表
     * @return 影响行数
     */
    int insertBatchHrUserDetails(List<HrUserDetails> list);
    
    /**
     * 删除所有HR用户详情
     * @return 影响行数
     */
    int deleteHrDetails();
    
    /**
     * 根据用户工号查找HR用户详情
     * @param userNo 用户工号
     * @return HR用户详情
     */
    HrUserDetails findHrDetailsByUserNo(String userNo);
    
    /**
     * 更新HR用户详情
     * @param detail HR用户详情
     * @return 影响行数
     */
    int updateHrUserDetails(HrUserDetails detail);
}