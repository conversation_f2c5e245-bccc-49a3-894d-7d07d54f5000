package com.nercar.datasynchronization.service.impl;

import com.nercar.datasynchronization.client.SoapClient;
import com.nercar.datasynchronization.config.RetryConfig;
import com.nercar.datasynchronization.entity.DepartmentSyncOperationLog;
import com.nercar.datasynchronization.entity.DepartmentSyncTest;
import com.nercar.datasynchronization.repository.DepartmentSyncOperationLogRepository;
import com.nercar.datasynchronization.repository.DepartmentSyncTestRepository;
import com.nercar.datasynchronization.service.DepartmentSyncTestService;
import com.nercar.datasynchronization.utils.RetryUtils;
import com.nercar.datasynchronization.utils.XmlUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 部门同步测试服务实现类
 * 完全参考ERP同步代码的核心逻辑
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DepartmentSyncTestServiceImpl implements DepartmentSyncTestService {

    private final DepartmentSyncTestRepository departmentSyncTestRepository;
    private final DepartmentSyncOperationLogRepository operationLogRepository;
    private final SoapClient soapClient;
    private final RetryConfig retryConfig;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Override
    @Transactional
    public Map<String, Object> syncAllDepartmentsByDay(String startDate, String endDate) {
        log.info("开始全量同步部门数据，时间范围：{} 到 {}", startDate, endDate);
        
        Map<String, Object> result = new HashMap<>();
        List<String> successDates = new ArrayList<>();
        List<String> failedDates = new ArrayList<>();
        int totalProcessedDays = 0;
        
        try {
            // 参考ERP代码的循环逻辑，修复边界条件：包含结束日期
            String currentDate = startDate;
            while (currentDate.compareTo(endDate) <= 0) {
                totalProcessedDays++;
                try {
                    log.info("开始同步日期: {}", currentDate);
                    Map<String, Object> dayResult = syncDepartmentsByDay(currentDate);
                    successDates.add(currentDate);
                    log.info("日期 {} 同步成功，处理结果：{}", currentDate, dayResult);
                } catch (Exception e) {
                    log.error("同步日期 {} 失败", currentDate, e);
                    failedDates.add(currentDate + ": " + e.getMessage());
                }
                
                // 计算下一天
                currentDate = getNextDay(currentDate);
            }
            
            result.put("success", true);
            result.put("message", "全量同步完成");
            result.put("totalProcessedDays", totalProcessedDays);
            result.put("successDates", successDates);
            result.put("failedDates", failedDates);
            result.put("successCount", successDates.size());
            result.put("failedCount", failedDates.size());
            
        } catch (Exception e) {
            log.error("全量同步过程中发生异常", e);
            result.put("success", false);
            result.put("message", "全量同步失败：" + e.getMessage());
        }
        
        return result;
    }

    @Override
    @Transactional
    public Map<String, Object> syncDepartmentsByDay(String syncDate) {
        log.info("开始同步日期 {} 的部门数据", syncDate);
        
        LocalDate syncLocalDate = LocalDate.parse(syncDate, DATE_FORMATTER);
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 1. 特殊数据处理（参考ERP逻辑：先删除特殊数据）
            List<String> deletedOrgCodes = handleSpecialDepartments();
            log.info("特殊数据处理完成，删除了 {} 个特殊部门", deletedOrgCodes.size());
            
            // 2. 获取ERP数据
            String xmlData = getErpDataByDate(syncDate);
            if (xmlData == null || xmlData.trim().isEmpty()) {
                log.warn("日期 {} 没有获取到ERP数据", syncDate);
                result.put("success", true);
                result.put("message", "没有数据需要同步");
                result.put("processedCount", 0);
                return result;
            }
            
            // 3. 解析XML数据
            List<Map<String, String>> erpDepartments = parseXmlData(xmlData);
            log.info("解析到 {} 条ERP部门数据", erpDepartments.size());
            
            // 4. 状态感知处理（完全参考ERP逻辑）
            int insertCount = 0, updateCount = 0, deleteCount = 0, skipCount = 0;
            
            for (Map<String, String> erpDept : erpDepartments) {
                try {
                    String orgCode = erpDept.get("ORGCODE");
                    if (orgCode == null || orgCode.trim().isEmpty()) {
                        log.warn("跳过组织编码为空的数据");
                        continue;
                    }
                    
                    List<DepartmentSyncTest> localDepts = departmentSyncTestRepository.findByOrgCode(orgCode);
                    String operationType = processErpDepartment(erpDept, localDepts, syncLocalDate);
                    
                    // 统计操作类型
                    switch (operationType) {
                        case "INSERT": insertCount++; break;
                        case "UPDATE": updateCount++; break;
                        case "DELETE": deleteCount++; break;
                        case "SKIP": skipCount++; break;
                    }
                    
                } catch (Exception e) {
                    log.error("处理部门数据失败，orgCode: {}", erpDept.get("ORGCODE"), e);
                    // 记录失败日志
                    logOperation(syncLocalDate, erpDept.get("ORGCODE"), "ERROR", 
                               getIntValue(erpDept.get("ISHISTORY")), erpDept.get("USERPREDEF_14"), 
                               false, "FAILED", e.getMessage());
                }
            }
            
            // 5. 关系修复
            int repairedCount = repairDepartmentRelations(deletedOrgCodes);
            log.info("关系修复完成，修复了 {} 个部门的父子关系", repairedCount);
            
            result.put("success", true);
            result.put("message", "同步完成");
            result.put("syncDate", syncDate);
            result.put("processedCount", erpDepartments.size());
            result.put("insertCount", insertCount);
            result.put("updateCount", updateCount);
            result.put("deleteCount", deleteCount);
            result.put("skipCount", skipCount);
            result.put("repairedCount", repairedCount);
            result.put("deletedSpecialCount", deletedOrgCodes.size());
            
        } catch (Exception e) {
            log.error("同步日期 {} 的数据时发生异常", syncDate, e);
            result.put("success", false);
            result.put("message", "同步失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 处理单个ERP部门数据（核心逻辑，完全参考ERP代码）
     */
    private String processErpDepartment(Map<String, String> erpDept, List<DepartmentSyncTest> localDepts, LocalDate syncDate) {
        String orgCode = erpDept.get("ORGCODE");
        String isHistoryStr = erpDept.get("ISHISTORY");
        String userPredef14 = erpDept.get("USERPREDEF_14");
        Integer isHistory = getIntValue(isHistoryStr);
        
        // 参考ERP代码的核心逻辑
        if (localDepts.size() > 0) {
            // 本地已存在
            if (isHistory != null && isHistory == 1) {
                // 删除操作
                for (DepartmentSyncTest dept : localDepts) {
                    departmentSyncTestRepository.delete(dept);
                    log.debug("删除部门：{} - {}", dept.getOrgCode(), dept.getOrgName());
                }
                logOperation(syncDate, orgCode, "DELETE", isHistory, userPredef14, true, "SUCCESS", null);
                return "DELETE";
            } else if (isHistory != null && isHistory == 0) {
                // 更新操作
                DepartmentSyncTest existingDept = localDepts.get(0);
                updateDepartmentFromErp(existingDept, erpDept, syncDate);
                departmentSyncTestRepository.save(existingDept);
                logOperation(syncDate, orgCode, "UPDATE", isHistory, userPredef14, true, "SUCCESS", null);
                return "UPDATE";
            }
        } else {
            // 本地不存在
            if (isHistory != null && isHistory == 0) {
                // 新增操作
                DepartmentSyncTest newDept = createDepartmentFromErp(erpDept, syncDate);
                departmentSyncTestRepository.save(newDept);
                logOperation(syncDate, orgCode, "INSERT", isHistory, userPredef14, false, "SUCCESS", null);
                return "INSERT";
            } else {
                // 跳过（本地不存在且ERP中为删除状态）
                logOperation(syncDate, orgCode, "SKIP", isHistory, userPredef14, false, "SUCCESS", "本地不存在且ERP为删除状态");
                return "SKIP";
            }
        }
        
        return "SKIP";
    }

    @Override
    @Transactional
    public List<String> handleSpecialDepartments() {
        // 参考ERP代码：删除特殊组织编码的部门
        List<String> specialOrgCodes = Arrays.asList("X"); // 可以扩展更多特殊编码
        List<String> deletedOrgCodes = new ArrayList<>();

        try {
            List<DepartmentSyncTest> specialDepts = departmentSyncTestRepository.findByOrgCodeIn(specialOrgCodes);
            for (DepartmentSyncTest dept : specialDepts) {
                log.info("删除特殊部门：{} - {}", dept.getOrgCode(), dept.getOrgName());
                departmentSyncTestRepository.delete(dept);
                deletedOrgCodes.add(dept.getOrgCode());
            }
        } catch (Exception e) {
            log.error("处理特殊部门数据时发生异常", e);
        }

        return deletedOrgCodes;
    }

    @Override
    @Transactional
    public int repairDepartmentRelations(List<String> deletedOrgCodes) {
        // 参考ERP代码：修复父子关系异常
        int repairedCount = 0;

        try {
            // 查找父子关系异常的部门
            List<DepartmentSyncTest> brokenDepts = departmentSyncTestRepository.findDepartmentsWithBrokenRelations();

            for (DepartmentSyncTest dept : brokenDepts) {
                if (deletedOrgCodes.contains(dept.getParentCode())) {
                    // 如果父级被删除，设置为根节点
                    dept.setParentCode("1");
                    departmentSyncTestRepository.save(dept);
                    repairedCount++;
                    log.debug("修复部门 {} 的父子关系，设置为根节点", dept.getOrgCode());
                } else {
                    // 尝试查找正确的父级
                    Optional<DepartmentSyncTest> parentOpt = departmentSyncTestRepository.findFirstByOrgCode(dept.getParentCode());
                    if (parentOpt.isPresent()) {
                        // 父级存在，关系正常，可能是查询时机问题
                        log.debug("部门 {} 的父级关系实际正常", dept.getOrgCode());
                    } else {
                        // 父级确实不存在，设置为根节点
                        dept.setParentCode("1");
                        departmentSyncTestRepository.save(dept);
                        repairedCount++;
                        log.debug("修复部门 {} 的父子关系，父级不存在，设置为根节点", dept.getOrgCode());
                    }
                }
            }
        } catch (Exception e) {
            log.error("修复父子关系时发生异常", e);
        }

        return repairedCount;
    }

    @Override
    public Map<String, Object> getDataStatistics() {
        Map<String, Object> stats = new HashMap<>();

        try {
            // 基本统计
            long totalCount = departmentSyncTestRepository.count();
            long normalCount = departmentSyncTestRepository.countByIsHistory(0);
            long historyCount = departmentSyncTestRepository.countByIsHistory(1);
            long deletedCount = departmentSyncTestRepository.countByUserPredef14("D");

            // 层级统计
            List<DepartmentSyncTest> rootDepts = departmentSyncTestRepository.findRootDepartments();
            List<DepartmentSyncTest> brokenDepts = departmentSyncTestRepository.findDepartmentsWithBrokenRelations();

            stats.put("totalCount", totalCount);
            stats.put("normalCount", normalCount);
            stats.put("historyCount", historyCount);
            stats.put("deletedCount", deletedCount);
            stats.put("rootDepartmentCount", rootDepts.size());
            stats.put("brokenRelationCount", brokenDepts.size());

            // 最近同步时间
            // 这里可以添加更多统计信息

        } catch (Exception e) {
            log.error("获取数据统计时发生异常", e);
            stats.put("error", e.getMessage());
        }

        return stats;
    }

    @Override
    public Map<String, Object> compareWithOriginalData() {
        // TODO: 实现与原始department表的数据对比
        Map<String, Object> comparison = new HashMap<>();

        try {
            // 这里需要查询原始的department表进行对比
            // 由于需要访问原始表，这里先返回基本信息
            long testTableCount = departmentSyncTestRepository.count();

            comparison.put("testTableCount", testTableCount);
            comparison.put("message", "数据对比功能待完善");

        } catch (Exception e) {
            log.error("对比数据时发生异常", e);
            comparison.put("error", e.getMessage());
        }

        return comparison;
    }

    @Override
    public Map<String, Object> getSyncLogStatistics(String syncDate) {
        Map<String, Object> logStats = new HashMap<>();

        try {
            LocalDate date = LocalDate.parse(syncDate, DATE_FORMATTER);

            // 按操作类型统计
            List<Object[]> operationStats = operationLogRepository.countOperationsByTypeAndDate(date);
            Map<String, Long> operationCounts = new HashMap<>();
            for (Object[] stat : operationStats) {
                operationCounts.put((String) stat[0], (Long) stat[1]);
            }

            // 按结果统计
            List<Object[]> resultStats = operationLogRepository.countOperationsByResultAndDate(date);
            Map<String, Long> resultCounts = new HashMap<>();
            for (Object[] stat : resultStats) {
                resultCounts.put((String) stat[0], (Long) stat[1]);
            }

            // 失败的操作
            List<DepartmentSyncOperationLog> failedLogs = operationLogRepository.findByOperationResultAndSyncDate("FAILED", date);

            logStats.put("syncDate", syncDate);
            logStats.put("operationCounts", operationCounts);
            logStats.put("resultCounts", resultCounts);
            logStats.put("failedOperations", failedLogs.size());
            logStats.put("failedDetails", failedLogs);

        } catch (Exception e) {
            log.error("获取同步日志统计时发生异常", e);
            logStats.put("error", e.getMessage());
        }

        return logStats;
    }

    @Override
    @Transactional
    public void clearTestData() {
        try {
            log.info("开始清理测试数据");
            operationLogRepository.deleteAll();
            departmentSyncTestRepository.deleteAll();
            log.info("测试数据清理完成");
        } catch (Exception e) {
            log.error("清理测试数据时发生异常", e);
            throw new RuntimeException("清理测试数据失败", e);
        }
    }

    @Override
    public Map<String, Object> validateDataIntegrity() {
        Map<String, Object> validation = new HashMap<>();
        List<String> issues = new ArrayList<>();

        try {
            // 检查父子关系完整性
            List<DepartmentSyncTest> brokenDepts = departmentSyncTestRepository.findDepartmentsWithBrokenRelations();
            if (!brokenDepts.isEmpty()) {
                issues.add("发现 " + brokenDepts.size() + " 个部门的父子关系异常");
            }

            // 检查重复的组织编码
            // TODO: 添加更多完整性检查

            validation.put("isValid", issues.isEmpty());
            validation.put("issues", issues);
            validation.put("brokenRelationCount", brokenDepts.size());

        } catch (Exception e) {
            log.error("验证数据完整性时发生异常", e);
            validation.put("error", e.getMessage());
        }

        return validation;
    }

    // ==================== 辅助方法 ====================

    /**
     * 获取下一天的日期字符串
     */
    private String getNextDay(String dateStr) {
        LocalDate date = LocalDate.parse(dateStr, DATE_FORMATTER);
        return date.plusDays(1).format(DATE_FORMATTER);
    }

    /**
     * 根据日期获取ERP数据（带重试机制）
     */
    private String getErpDataByDate(String syncDate) {
        try {
            // 构造日期范围（当天）
            LocalDate date = LocalDate.parse(syncDate, DATE_FORMATTER);
            Date startDate = java.sql.Date.valueOf(date);
            Date endDate = java.sql.Date.valueOf(date.plusDays(1));

            String xmlData;
            if (retryConfig.isEnabled()) {
                // 使用重试机制获取数据
                xmlData = RetryUtils.executeWithRetry(() -> {
                    // 调用现有的SOAP接口获取原始响应
                    String soapResponse = soapClient.getOrgInfo(startDate, endDate);
                    log.debug("获取到SOAP响应，长度: {} 字符", soapResponse != null ? soapResponse.length() : 0);

                    // 提取XML数据（参考现有DataRetrievalServiceImpl的逻辑）
                    String extractedXml = XmlUtils.extractXmlData(soapResponse, "GetOrgInfoFromMDMResult");
                    log.debug("提取到原始XML数据，长度: {} 字符", extractedXml != null ? extractedXml.length() : 0);

                    if (extractedXml != null) {
                        // HTML解码（XML数据被HTML编码了）
                        String decodedXml = extractedXml.replace("&lt;", "<").replace("&gt;", ">").replace("&amp;", "&");
                        log.debug("HTML解码后XML数据，长度: {} 字符", decodedXml.length());

                        if (decodedXml.length() > 200) {
                            log.debug("解码后XML数据前200字符: {}", decodedXml.substring(0, 200));
                        }
                        return decodedXml;
                    }
                    return null;
                }, retryConfig.getMaxRetries(), retryConfig.getRetryDelay(), "获取部门同步数据-" + syncDate);
            } else {
                // 不使用重试机制
                String soapResponse = soapClient.getOrgInfo(startDate, endDate);
                log.debug("获取到SOAP响应，长度: {} 字符", soapResponse != null ? soapResponse.length() : 0);

                String extractedXml = XmlUtils.extractXmlData(soapResponse, "GetOrgInfoFromMDMResult");
                log.debug("提取到原始XML数据，长度: {} 字符", extractedXml != null ? extractedXml.length() : 0);

                if (extractedXml != null) {
                    xmlData = extractedXml.replace("&lt;", "<").replace("&gt;", ">").replace("&amp;", "&");
                    log.debug("HTML解码后XML数据，长度: {} 字符", xmlData.length());

                    if (xmlData.length() > 200) {
                        log.debug("解码后XML数据前200字符: {}", xmlData.substring(0, 200));
                    }
                } else {
                    xmlData = null;
                }
            }

            return xmlData;
        } catch (Exception e) {
            log.error("获取日期 {} 的ERP数据失败", syncDate, e);
            return null;
        }
    }

    /**
     * 解析XML数据为Map列表
     * 完全参考现有DataRetrievalServiceImpl.parseDepartmentElement的字段映射
     */
    private List<Map<String, String>> parseXmlData(String xmlData) {
        List<Map<String, String>> departments = new ArrayList<>();

        try {
            // 使用SAX解析器处理XML数据
            XmlUtils.parseXmlWithSAX(xmlData, "O_DATA", element -> {
                try {
                    // 获取MDM ID（部门UUID），与现有代码逻辑一致
                    String deptUuid = XmlUtils.getElementText(element, "NM");
                    if (deptUuid == null || deptUuid.isEmpty()) {
                        log.warn("部门MDM ID为空，跳过处理");
                        return;
                    }

                    // 获取组织编码，必填字段
                    String orgCode = XmlUtils.getElementText(element, "ORGCODE");
                    if (orgCode == null || orgCode.isEmpty()) {
                        log.warn("组织编码为空，跳过处理，部门ID: {}", deptUuid);
                        return;
                    }

                    Map<String, String> dept = new HashMap<>();

                    // 核心字段映射（参考现有实现）
                    dept.put("ORGCODE", orgCode);
                    dept.put("ORGNAME", XmlUtils.getElementText(element, "ORGNAME"));
                    dept.put("PNODECODE", XmlUtils.getElementText(element, "PNODECODE"));
                    dept.put("ORAALLNAME", XmlUtils.getElementText(element, "ORAALLNAME"));
                    dept.put("ISHISTORY", XmlUtils.getElementText(element, "ISHISTORY"));
                    dept.put("USERPREDEF_14", XmlUtils.getElementText(element, "USERPREDEF_14"));
                    dept.put("MDMID", deptUuid); // 使用正确的NM字段值

                    // 添加其他可能需要的字段（参考现有实现）
                    dept.put("PNODEID", XmlUtils.getElementText(element, "PNODEID"));
                    dept.put("USERPREDEF_11", XmlUtils.getElementText(element, "USERPREDEF_11"));
                    dept.put("USERPREDEF_12", XmlUtils.getElementText(element, "USERPREDEF_12"));
                    dept.put("USERPREDEF_13", XmlUtils.getElementText(element, "USERPREDEF_13"));
                    dept.put("USERPREDEF_18", XmlUtils.getElementText(element, "USERPREDEF_18"));

                    departments.add(dept);
                    log.debug("成功解析部门: {} ({}), ISHISTORY: {}, USERPREDEF_14: {}",
                             dept.get("ORGNAME"), orgCode, dept.get("ISHISTORY"), dept.get("USERPREDEF_14"));

                } catch (Exception e) {
                    log.error("解析单个部门数据失败", e);
                }
            });

            log.info("XML数据解析完成，成功解析 {} 条部门记录", departments.size());

        } catch (Exception e) {
            log.error("解析XML数据失败", e);
        }

        return departments;
    }

    /**
     * 从ERP数据创建新的部门实体
     */
    private DepartmentSyncTest createDepartmentFromErp(Map<String, String> erpDept, LocalDate syncDate) {
        DepartmentSyncTest dept = new DepartmentSyncTest();
        dept.setOrgCode(erpDept.get("ORGCODE"));
        dept.setOrgName(erpDept.get("ORGNAME"));
        dept.setParentCode(erpDept.get("PNODECODE"));
        dept.setFullName(erpDept.get("ORAALLNAME"));
        dept.setDeptUuid(erpDept.get("MDMID"));
        dept.setIsHistory(getIntValue(erpDept.get("ISHISTORY")));
        dept.setUserPredef14(erpDept.get("USERPREDEF_14"));
        dept.setSyncDate(syncDate);
        dept.setUpdateTime(LocalDateTime.now());
        return dept;
    }

    /**
     * 用ERP数据更新现有部门实体
     */
    private void updateDepartmentFromErp(DepartmentSyncTest dept, Map<String, String> erpDept, LocalDate syncDate) {
        dept.setOrgName(erpDept.get("ORGNAME"));
        dept.setParentCode(erpDept.get("PNODECODE"));
        dept.setFullName(erpDept.get("ORAALLNAME"));
        dept.setDeptUuid(erpDept.get("MDMID"));
        dept.setIsHistory(getIntValue(erpDept.get("ISHISTORY")));
        dept.setUserPredef14(erpDept.get("USERPREDEF_14"));
        dept.setSyncDate(syncDate);
        dept.setUpdateTime(LocalDateTime.now());
    }

    /**
     * 记录操作日志
     */
    private void logOperation(LocalDate syncDate, String orgCode, String operationType,
                             Integer isHistoryValue, String userPredef14Value,
                             boolean localExists, String result, String message) {
        try {
            DepartmentSyncOperationLog log;
            if ("SUCCESS".equals(result)) {
                log = DepartmentSyncOperationLog.createSuccessLog(syncDate, orgCode, operationType,
                        isHistoryValue, userPredef14Value, localExists, message);
            } else {
                log = DepartmentSyncOperationLog.createFailedLog(syncDate, orgCode, operationType,
                        isHistoryValue, userPredef14Value, localExists, message);
            }
            operationLogRepository.save(log);
        } catch (Exception e) {
            log.error("记录操作日志失败", e);
        }
    }

    /**
     * 安全地将字符串转换为Integer
     */
    private Integer getIntValue(String str) {
        if (str == null || str.trim().isEmpty()) {
            return null;
        }
        try {
            return Integer.parseInt(str.trim());
        } catch (NumberFormatException e) {
            log.warn("无法解析整数值: {}", str);
            return null;
        }
    }
}
