package com.nercar.datasynchronization.utils;

import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Map;

@Slf4j
public class HttpUtil {
    
    /**
     * 默认超时时间（毫秒）
     */
    private static final int DEFAULT_TIMEOUT = 30000;
    
    /**
     * 发送HTTP GET请求
     * @param urlStr 请求URL
     * @param timeout 超时时间（毫秒）
     * @return 响应内容
     * @throws Exception 如果请求失败
     */
    public static String get(String urlStr, int timeout) throws Exception {
        log.debug("发送HTTP GET请求到: {}", urlStr);
        HttpURLConnection connection = null;
        try {
            URL url = new URL(urlStr);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(timeout);
            connection.setReadTimeout(timeout);
            connection.setDoInput(true);
            
            log.debug("设置连接超时: {}ms, 读取超时: {}ms", timeout, timeout);
            
            int responseCode = connection.getResponseCode();
            log.debug("收到响应状态码: {}", responseCode);
            
            if (responseCode != HttpURLConnection.HTTP_OK) {
                String errorMessage = String.format("HTTP请求失败，状态码: %d", responseCode);
                log.error(errorMessage);
                throw new RuntimeException(errorMessage);
            }
            
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                String responseStr = response.toString();
                log.debug("收到响应数据，长度: {}", responseStr.length());
                return responseStr;
            }
            
        } catch (Exception e) {
            log.error("发送HTTP GET请求异常", e);
            throw e;
        } finally {
            if (connection != null) {
                connection.disconnect();
                log.debug("关闭HTTP连接");
            }
        }
    }
    
    /**
     * 发送HTTP GET请求，使用默认超时时间
     * @param urlStr 请求URL
     * @return 响应内容
     * @throws Exception 如果请求失败
     */
    public static String get(String urlStr) throws Exception {
        return get(urlStr, DEFAULT_TIMEOUT);
    }
    
    /**
     * 发送HTTP GET请求，URL后附加查询参数
     * @param urlStr 基础URL
     * @param params 查询参数Map
     * @return 响应内容
     * @throws Exception 如果请求失败
     */
    public static String get(String urlStr, Map<String, String> params) throws Exception {
        return get(urlStr, params, DEFAULT_TIMEOUT);
    }
    
    /**
     * 发送HTTP GET请求，URL后附加查询参数，指定超时时间
     * @param urlStr 基础URL
     * @param params 查询参数Map
     * @param timeout 超时时间（毫秒）
     * @return 响应内容
     * @throws Exception 如果请求失败
     */
    public static String get(String urlStr, Map<String, String> params, int timeout) throws Exception {
        StringBuilder sb = new StringBuilder(urlStr);
        
        // 添加查询参数
        if (params != null && !params.isEmpty()) {
            if (!urlStr.contains("?")) {
                sb.append("?");
            } else if (!urlStr.endsWith("&") && !urlStr.endsWith("?")) {
                sb.append("&");
            }
            
            int i = 0;
            for (Map.Entry<String, String> entry : params.entrySet()) {
                if (i > 0) {
                    sb.append("&");
                }
                sb.append(entry.getKey()).append("=").append(entry.getValue());
                i++;
            }
        }
        
        String fullUrl = sb.toString();
        log.debug("构建带参数的完整URL: {}", fullUrl);
        
        return get(fullUrl, timeout);
    }
}