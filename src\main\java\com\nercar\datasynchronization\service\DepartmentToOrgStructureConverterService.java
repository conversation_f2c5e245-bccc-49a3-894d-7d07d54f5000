package com.nercar.datasynchronization.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Service;

import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

/**
 * 将 department_sync_test 数据转换为 t_org_structure 格式的服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DepartmentToOrgStructureConverterService {

    private final JdbcTemplate jdbcTemplate;

    /**
     * 将 department_sync_test 数据转换为 t_org_structure 格式的SQL文件
     */
    public String convertToOrgStructureSQL(String outputFilePath) {
        try {
            log.info("开始转换部门数据为 t_org_structure 格式，输出文件: {}", outputFilePath);
            
            // 1. 获取所有有效部门数据
            List<DepartmentRecord> departments = getAllValidDepartments();
            log.info("获取到 {} 条有效部门记录", departments.size());
            
            // 2. 构建部门映射
            Map<String, DepartmentRecord> deptMap = new HashMap<>();
            for (DepartmentRecord dept : departments) {
                deptMap.put(dept.orgCode, dept);
            }
            
            // 3. 生成 t_org_structure 格式的SQL
            generateOrgStructureSQL(departments, deptMap, outputFilePath);
            
            log.info("转换完成，生成文件: {}", outputFilePath);
            return String.format("成功转换 %d 条部门记录为 t_org_structure 格式", departments.size());
            
        } catch (Exception e) {
            log.error("转换失败", e);
            throw new RuntimeException("转换失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取所有有效部门
     */
    private List<DepartmentRecord> getAllValidDepartments() {
        String sql = """
            SELECT org_code, org_name, parent_code, dept_uuid, full_name, is_history, user_predef_14
            FROM department_sync_test 
            WHERE is_history = 0 AND user_predef_14 != 'D'
            ORDER BY 
                CASE WHEN parent_code = '1' THEN 0 ELSE 1 END,
                org_code
            """;
        
        return jdbcTemplate.query(sql, new DepartmentRecordRowMapper());
    }

    /**
     * 生成 t_org_structure 格式的SQL文件
     */
    private void generateOrgStructureSQL(List<DepartmentRecord> departments, 
                                       Map<String, DepartmentRecord> deptMap, 
                                       String outputFilePath) throws IOException {
        
        try (PrintWriter writer = new PrintWriter(new FileWriter(outputFilePath))) {
            // 写入文件头
            writeFileHeader(writer, departments.size());
            
            // 写入清理语句
            writer.println("-- 清理现有同步数据");
            writer.println("DELETE FROM t_org_structure WHERE data_source = 2;");
            writer.println();
            
            // 写入插入语句
            writer.println("-- 插入转换后的部门层级数据");
            writer.println("INSERT INTO t_org_structure (id, organ_name, pre_id, order_info, is_del, create_time, modify_time, data_source) VALUES");
            
            // 生成插入数据
            for (int i = 0; i < departments.size(); i++) {
                DepartmentRecord dept = departments.get(i);
                
                // 生成唯一ID
                Long id = generateUniqueId(dept.orgCode);
                
                // 确定父级ID
                Long preId = determineParentId(dept, deptMap);
                
                // 写入数据行
                writer.printf("(%d, '%s', %s, %d, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 2)",
                    id,
                    escapeString(dept.orgName),
                    preId != null ? preId.toString() : "NULL",
                    i + 1 // order_info
                );
                
                if (i < departments.size() - 1) {
                    writer.println(",");
                } else {
                    writer.println(";");
                }
            }
            
            // 写入验证查询
            writeValidationQueries(writer);
        }
    }

    /**
     * 写入文件头信息
     */
    private void writeFileHeader(PrintWriter writer, int totalCount) {
        writer.println("-- =====================================================");
        writer.println("-- 部门数据转换为 t_org_structure 格式");
        writer.println("-- 生成时间: " + new Date());
        writer.println("-- 数据来源: department_sync_test (修正后的层级关系)");
        writer.println("-- 总记录数: " + totalCount);
        writer.println("-- 目标表: t_org_structure");
        writer.println("-- =====================================================");
        writer.println();
    }

    /**
     * 确定父级ID
     */
    private Long determineParentId(DepartmentRecord dept, Map<String, DepartmentRecord> deptMap) {
        if ("1".equals(dept.parentCode) || dept.parentCode == null) {
            return null; // 一级部门
        }
        
        // 查找父级部门
        DepartmentRecord parentDept = deptMap.get(dept.parentCode);
        if (parentDept != null) {
            return generateUniqueId(parentDept.orgCode);
        }
        
        log.warn("找不到父级部门: {} -> {}", dept.orgName, dept.parentCode);
        return null;
    }

    /**
     * 生成唯一ID
     */
    private Long generateUniqueId(String orgCode) {
        if (orgCode.startsWith("X")) {
            try {
                // 移除X前缀，转换为数字
                String numericPart = orgCode.substring(1);
                return Long.parseLong(numericPart);
            } catch (NumberFormatException e) {
                // 如果转换失败，使用hashCode
                return Math.abs((long) orgCode.hashCode());
            }
        } else if (orgCode.startsWith("AUTO")) {
            try {
                // 移除AUTO前缀，转换为数字
                String numericPart = orgCode.substring(4);
                return Long.parseLong(numericPart);
            } catch (NumberFormatException e) {
                return Math.abs((long) orgCode.hashCode());
            }
        } else {
            try {
                return Long.parseLong(orgCode);
            } catch (NumberFormatException e) {
                return Math.abs((long) orgCode.hashCode());
            }
        }
    }

    /**
     * 转义字符串中的单引号
     */
    private String escapeString(String str) {
        if (str == null) {
            return "";
        }
        return str.replace("'", "''");
    }

    /**
     * 写入验证查询
     */
    private void writeValidationQueries(PrintWriter writer) {
        writer.println();
        writer.println("-- =====================================================");
        writer.println("-- 数据验证查询");
        writer.println("-- =====================================================");
        writer.println();
        
        writer.println("-- 1. 统计总记录数");
        writer.println("SELECT '总记录数' as 统计项, COUNT(*) as 数量 FROM t_org_structure WHERE data_source = 2;");
        writer.println();
        
        writer.println("-- 2. 统计一级部门数量");
        writer.println("SELECT '一级部门数' as 统计项, COUNT(*) as 数量 FROM t_org_structure WHERE pre_id IS NULL AND data_source = 2;");
        writer.println();
        
        writer.println("-- 3. 统计各层级部门数量");
        writer.println("""
            WITH RECURSIVE dept_levels AS (
                SELECT id, organ_name, pre_id, 1 as level
                FROM t_org_structure 
                WHERE pre_id IS NULL AND data_source = 2
                
                UNION ALL
                
                SELECT o.id, o.organ_name, o.pre_id, dl.level + 1
                FROM t_org_structure o
                JOIN dept_levels dl ON o.pre_id = dl.id
                WHERE o.data_source = 2
            )
            SELECT level as 层级, COUNT(*) as 部门数量
            FROM dept_levels 
            GROUP BY level 
            ORDER BY level;
            """);
        writer.println();
        
        writer.println("-- 4. 检查孤儿节点");
        writer.println("""
            SELECT '孤儿节点' as 问题类型, organ_name as 部门名称, id as 部门ID
            FROM t_org_structure 
            WHERE pre_id IS NOT NULL 
            AND pre_id NOT IN (SELECT id FROM t_org_structure WHERE data_source = 2)
            AND data_source = 2;
            """);
        writer.println();
        
        writer.println("-- 5. 显示部分层级结构示例");
        writer.println("""
            WITH RECURSIVE dept_tree AS (
                SELECT id, organ_name, pre_id, organ_name as path, 1 as level
                FROM t_org_structure 
                WHERE pre_id IS NULL AND data_source = 2
                LIMIT 5
                
                UNION ALL
                
                SELECT o.id, o.organ_name, o.pre_id, 
                       CONCAT(dt.path, ' -> ', o.organ_name) as path, 
                       dt.level + 1
                FROM t_org_structure o
                JOIN dept_tree dt ON o.pre_id = dt.id
                WHERE o.data_source = 2 AND dt.level < 4
            )
            SELECT level as 层级, path as 层级路径
            FROM dept_tree 
            ORDER BY path
            LIMIT 20;
            """);
    }

    // 数据记录类
    public static class DepartmentRecord {
        public String orgCode;
        public String orgName;
        public String parentCode;
        public String deptUuid;
        public String fullName;
        public Integer isHistory;
        public String userPredef14;
    }

    // RowMapper 实现
    private static class DepartmentRecordRowMapper implements RowMapper<DepartmentRecord> {
        @Override
        public DepartmentRecord mapRow(ResultSet rs, int rowNum) throws SQLException {
            DepartmentRecord record = new DepartmentRecord();
            record.orgCode = rs.getString("org_code");
            record.orgName = rs.getString("org_name");
            record.parentCode = rs.getString("parent_code");
            record.deptUuid = rs.getString("dept_uuid");
            record.fullName = rs.getString("full_name");
            record.isHistory = rs.getInt("is_history");
            record.userPredef14 = rs.getString("user_predef_14");
            return record;
        }
    }
}
