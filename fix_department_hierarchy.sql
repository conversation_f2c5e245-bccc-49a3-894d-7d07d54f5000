-- 部门层级关系矫正脚本
-- 通过fullname智能分析和重建正确的parent_code关系

-- 第一步：创建临时表存储分析结果
DROP TABLE IF EXISTS temp_dept_analysis;
CREATE TABLE temp_dept_analysis (
    org_code VARCHAR(50),
    org_name VA<PERSON>HAR(200),
    full_name VA<PERSON><PERSON><PERSON>(500),
    current_parent_code VARCHAR(50),
    suggested_parent_code VARCHAR(50),
    parent_name VARCHAR(200),
    analysis_method VARCHAR(50),
    confidence_level INT,
    notes TEXT
);

-- 第二步：创建部门名称映射表（用于快速查找）
DROP TABLE IF EXISTS temp_dept_mapping;
CREATE TABLE temp_dept_mapping (
    dept_name VARCHAR(200),
    org_code VARCHAR(50),
    full_name VARCHAR(500),
    PRIMARY KEY (dept_name, org_code)
);

-- 插入所有部门名称映射
INSERT INTO temp_dept_mapping (dept_name, org_code, full_name)
SELECT DISTINCT org_name, org_code, full_name 
FROM department_sync_test 
WHERE org_name IS NOT NULL AND org_name != '';

-- 第三步：分析函数 - 查找最长匹配的上级部门
-- 这个查询会找到每个部门可能的上级部门

-- 处理简单情况：fullname包含多个部门名称的情况
INSERT INTO temp_dept_analysis (
    org_code, org_name, full_name, current_parent_code, 
    suggested_parent_code, parent_name, analysis_method, confidence_level
)
SELECT 
    d.org_code,
    d.org_name,
    d.full_name,
    d.parent_code as current_parent_code,
    m.org_code as suggested_parent_code,
    m.dept_name as parent_name,
    'LONGEST_MATCH' as analysis_method,
    90 as confidence_level
FROM department_sync_test d
JOIN temp_dept_mapping m ON (
    -- 查找fullname中包含的其他部门名称，且该部门名称不是当前部门
    d.full_name LIKE CONCAT('%', m.dept_name, '%') 
    AND m.dept_name != d.org_name
    AND LENGTH(m.dept_name) > 3  -- 避免匹配过短的名称
    -- 确保找到的是前缀匹配（上级部门）
    AND LOCATE(m.dept_name, d.full_name) = 1
)
WHERE d.parent_code = '1'  -- 只处理parent_code错误的记录
ORDER BY d.org_code, LENGTH(m.dept_name) DESC;-- 第四步：处理复杂情况 - 智能分词匹配
-- 对于"第一炼铁厂生产科"这种情况，需要特殊处理

-- 创建常见部门后缀表
DROP TABLE IF EXISTS temp_dept_suffixes;
CREATE TABLE temp_dept_suffixes (
    suffix VARCHAR(50),
    level_type VARCHAR(20)
);

INSERT INTO temp_dept_suffixes VALUES
('科', '科室'),
('室', '科室'), 
('车间', '车间'),
('厂', '厂'),
('部', '事业部'),
('中心', '中心'),
('班', '班组'),
('组', '班组'),
('站', '站'),
('处', '处');

-- 智能分词分析：处理复合部门名称
INSERT INTO temp_dept_analysis (
    org_code, org_name, full_name, current_parent_code, 
    suggested_parent_code, parent_name, analysis_method, confidence_level, notes
)
SELECT 
    d.org_code,
    d.org_name,
    d.full_name,
    d.parent_code as current_parent_code,
    parent_dept.org_code as suggested_parent_code,
    parent_dept.org_name as parent_name,
    'SMART_SPLIT' as analysis_method,
    85 as confidence_level,
    CONCAT('从"', d.org_name, '"中分离出上级"', parent_dept.org_name, '"') as notes
FROM department_sync_test d
JOIN temp_dept_suffixes s ON d.org_name LIKE CONCAT('%', s.suffix)
JOIN department_sync_test parent_dept ON (
    -- 查找可能的上级部门：当前部门名去掉后缀后，能匹配到的其他部门
    parent_dept.org_name = SUBSTRING(d.org_name, 1, LENGTH(d.org_name) - LENGTH(s.suffix))
    AND parent_dept.org_code != d.org_code
)
WHERE d.parent_code = '1'  -- 只处理parent_code错误的记录
  AND d.org_code NOT IN (SELECT org_code FROM temp_dept_analysis)  -- 避免重复分析
  AND LENGTH(parent_dept.org_name) >= 4;  -- 确保上级部门名称不会太短

-- 第五步：处理特殊情况 - 基于已知正确层级关系推断
-- 利用已经正确的层级关系来推断其他部门

INSERT INTO temp_dept_analysis (
    org_code, org_name, full_name, current_parent_code, 
    suggested_parent_code, parent_name, analysis_method, confidence_level, notes
)
SELECT 
    d.org_code,
    d.org_name,
    d.full_name,
    d.parent_code as current_parent_code,
    correct_parent.parent_code as suggested_parent_code,
    parent_dept.org_name as parent_name,
    'PATTERN_INFERENCE' as analysis_method,
    75 as confidence_level,
    '基于相似部门的正确层级关系推断' as notes
FROM department_sync_test d
JOIN department_sync_test correct_parent ON (
    -- 查找有相似fullname模式且parent_code正确的部门
    correct_parent.parent_code != '1'
    AND correct_parent.full_name LIKE CONCAT(SUBSTRING_INDEX(d.full_name, d.org_name, 1), '%')
    AND correct_parent.org_code != d.org_code
)
JOIN department_sync_test parent_dept ON parent_dept.org_code = correct_parent.parent_code
WHERE d.parent_code = '1'
  AND d.org_code NOT IN (SELECT org_code FROM temp_dept_analysis)
  AND LENGTH(SUBSTRING_INDEX(d.full_name, d.org_name, 1)) > 2;-- 第六步：数据清理和优化
-- 移除置信度低或可能错误的建议

DELETE FROM temp_dept_analysis 
WHERE confidence_level < 70
   OR suggested_parent_code = org_code  -- 避免自己指向自己
   OR suggested_parent_code IS NULL;

-- 对于有多个建议的部门，选择置信度最高的
DELETE t1 FROM temp_dept_analysis t1
JOIN temp_dept_analysis t2 ON t1.org_code = t2.org_code 
WHERE t1.confidence_level < t2.confidence_level
   OR (t1.confidence_level = t2.confidence_level AND t1.analysis_method > t2.analysis_method);

-- 第七步：生成分析报告
SELECT 
    '=== 部门层级关系矫正分析报告 ===' as report_section,
    '' as org_code, '' as details, '' as action;

SELECT 
    '总体统计' as report_section,
    'TOTAL' as org_code,
    CONCAT('需要矫正的部门总数: ', COUNT(*)) as details,
    '执行UPDATE语句进行矫正' as action
FROM temp_dept_analysis;

SELECT 
    '按分析方法统计' as report_section,
    analysis_method as org_code,
    CONCAT('数量: ', COUNT(*), ', 平均置信度: ', ROUND(AVG(confidence_level), 1)) as details,
    '' as action
FROM temp_dept_analysis 
GROUP BY analysis_method;

-- 第八步：显示具体的矫正建议（前20条作为示例）
SELECT 
    '矫正建议详情' as report_section,
    org_code,
    CONCAT(org_name, ' -> ', COALESCE(parent_name, 'NULL')) as details,
    CONCAT('UPDATE department_sync_test SET parent_code = ''', suggested_parent_code, ''' WHERE org_code = ''', org_code, ''';') as action
FROM temp_dept_analysis 
ORDER BY confidence_level DESC, org_code
LIMIT 20;

-- 第九步：生成完整的UPDATE语句
SELECT 
    '执行矫正的UPDATE语句' as report_section,
    '' as org_code,
    '-- 以下是所有需要矫正的UPDATE语句' as details,
    '' as action;

SELECT 
    '' as report_section,
    org_code,
    CONCAT('-- ', org_name, ' -> ', parent_name, ' (', analysis_method, ', 置信度:', confidence_level, ')') as details,
    CONCAT('UPDATE department_sync_test SET parent_code = ''', suggested_parent_code, ''' WHERE org_code = ''', org_code, ''';') as action
FROM temp_dept_analysis 
ORDER BY confidence_level DESC, org_code;

-- 第十步：验证脚本
SELECT 
    '验证脚本' as report_section,
    '' as org_code,
    '-- 执行UPDATE后，运行以下查询验证结果' as details,
    '' as action;

SELECT 
    '' as report_section,
    '' as org_code,
    '' as details,
    'SELECT d.org_code, d.org_name, d.parent_code, p.org_name as parent_name FROM department_sync_test d LEFT JOIN department_sync_test p ON d.parent_code = p.org_code WHERE d.org_code IN (SELECT org_code FROM temp_dept_analysis) ORDER BY d.org_code;' as action;-- 清理临时表
-- DROP TABLE IF EXISTS temp_dept_analysis;
-- DROP TABLE IF EXISTS temp_dept_mapping; 
-- DROP TABLE IF EXISTS temp_dept_suffixes;

-- 注意：临时表暂时保留，以便查看分析结果和验证矫正效果
-- 确认矫正无误后，可以取消注释上面的DROP语句来清理临时表