//package com.nercar.datasynchronization.service.impl;
//
//import com.nercar.datasynchronization.entity.HrUserDetails;
//import com.nercar.datasynchronization.entity.SyncTask;
//import com.nercar.datasynchronization.mapper.CommonMapper;
//import com.nercar.datasynchronization.repository.SyncTaskRepository;
//import com.nercar.datasynchronization.service.DataMigrationService;
//import com.nercar.datasynchronization.service.ParallelHrUserDetailsService;
//import com.nercar.datasynchronization.utils.HttpUtil;
//import com.nercar.datasynchronization.utils.IDUtil;
//import cn.hutool.json.JSONArray;
//import cn.hutool.json.JSONObject;
//import cn.hutool.json.JSONUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.text.SimpleDateFormat;
//import java.time.Year;
//import java.util.*;
//import java.util.concurrent.*;
//import java.util.concurrent.atomic.AtomicInteger;
//import java.util.stream.Collectors;
//
//@Slf4j
//@Service
//public class ParallelHrUserDetailsServiceImpl implements ParallelHrUserDetailsService {
//
//    @Autowired
//    private CommonMapper commonMapper;
//
//    @Autowired
//    private SyncTaskRepository syncTaskRepository;
//
//    @Value("${sync.http.position-url}")
//    private String positionServiceUrl;
//
//    @Value("${sync.http.timeout:30000}")
//    private int timeout;
//
//    @Value("${sync.http.retry-count:3}")
//    private int retryCount;
//
//    @Autowired
//    private DataMigrationService dataMigrationService;
//
//    // 并行处理的线程数
//    private static final int PARALLEL_THREADS = 4;
//
//    // 每页数据量
//    private static final int PAGE_SIZE = 1000;
//
//    // 批处理大小
//    private static final int BATCH_SIZE = 500;
//
//    @Override
//    @Transactional
//    public void updateAllUserDetailFromErpParallel() {
//        // 调用新方法实现全量同步，不指定时间范围表示处理所有数据
//        updateUserDetailFromErpParallel(null, null);
//    }
//
//    @Override
//    @Transactional
//    public void updateUserDetailFromErpParallel(Date startDate, Date endDate) {
//        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//
//        if (startDate != null && endDate != null) {
//            log.info("开始从ERP系统多线程同步岗位数据，URL: {}, 记录时间范围: {} 至 {}",
//                    positionServiceUrl,
//                    dateFormat.format(startDate),
//                    dateFormat.format(endDate));
//        } else {
//            log.info("开始从ERP系统多线程同步所有岗位数据，URL: {}", positionServiceUrl);
//        }
//
//        // 记录同步开始时间和任务ID
//        Date syncStartTime = new Date();
//        String taskId = UUID.randomUUID().toString().replace("-", "");
//
//        try {
//            // 创建并保存同步任务记录
//            SyncTask syncTask = new SyncTask();
//            syncTask.setTaskId(taskId);
//            syncTask.setTaskType("HR_POSITION");
//            syncTask.setStartDate(startDate != null ? startDate : syncStartTime);
//            syncTask.setStatus("RUNNING");
//            syncTask.setParallelism(PARALLEL_THREADS); // 设置并行度
//            syncTask.setCreatedTime(syncStartTime);
//            syncTask.setLastUpdatedTime(syncStartTime);
//            syncTaskRepository.save(syncTask);
//
//            // 第一步：获取总记录数
//            int totalRecords = getTotalRecordsCount();
//            log.info("数据总量: {} 条", totalRecords);
//
//            // 计算总页数
//            int totalPages = (int) Math.ceil(totalRecords / (double)PAGE_SIZE);
//            log.info("总页数: {}, 每页大小: {}", totalPages, PAGE_SIZE);
//
//            // 初始化统计计数器
//            AtomicInteger totalProcessedCount = new AtomicInteger(0);
//            AtomicInteger newCount = new AtomicInteger(0);
//            AtomicInteger updateCount = new AtomicInteger(0);
//            AtomicInteger skipCount = new AtomicInteger(0);
//
//            // 创建线程池
//            ExecutorService executor = Executors.newFixedThreadPool(PARALLEL_THREADS);
//
//            // 创建任务列表
//            List<Future<PageResult>> futures = new ArrayList<>();
//            for (int page = 0; page < totalPages; page++) {
//                final int pageNum = page;
//                futures.add(executor.submit(() -> processPage(pageNum, PAGE_SIZE)));
//            }
//
//            // 等待所有任务完成并累计处理结果
//            for (Future<PageResult> future : futures) {
//                try {
//                    PageResult result = future.get();
//                    totalProcessedCount.addAndGet(result.getTotalProcessed());
//                    newCount.addAndGet(result.getNewRecords());
//                    updateCount.addAndGet(result.getUpdatedRecords());
//                    skipCount.addAndGet(result.getSkippedRecords());
//
//                    // 更新同步任务状态
//                    syncTask.setCompletedChunks(syncTask.getCompletedChunks() + 1);
//                    syncTask.setLastUpdatedTime(new Date());
//                    syncTaskRepository.save(syncTask);
//                } catch (InterruptedException | ExecutionException e) {
//                    log.error("线程执行异常", e);
//                }
//            }
//
//            // 关闭线程池
//            executor.shutdown();
//
//            // 记录同步结束时间
//            Date syncEndTime = new Date();
//
//            // 更新同步任务完成状态
//            syncTask.setEndDate(endDate != null ? endDate : syncEndTime);
//            syncTask.setStatus("COMPLETED");
//            syncTask.setLastUpdatedTime(syncEndTime);
//            syncTask.setTotalChunks(totalPages);
//            syncTask.setChunkDetails("{\"totalRecords\":" + totalProcessedCount.get() +
//                    ",\"newRecords\":" + newCount.get() +
//                    ",\"updatedRecords\":" + updateCount.get() +
//                    ",\"skippedRecords\":" + skipCount.get() + "}");
//            syncTaskRepository.save(syncTask);
//
//            log.info("岗位数据同步完成，共处理 {} 条记录，新增 {} 条，更新 {} 条，跳过 {} 条",
//                    totalProcessedCount.get(), newCount.get(), updateCount.get(), skipCount.get());
//
//            // 在同步完成后创建岗位关联关系，不限制时间范围，使用所有数据
//            log.info("开始提取岗位信息并创建关联关系");
//            dataMigrationService.createPositionRelations();  // 使用无参版本，处理所有数据
//            log.info("岗位信息提取和关联关系创建完成");
//
//        } catch (Exception e) {
//            log.error("同步岗位数据失败", e);
//
//            // 更新同步任务失败状态
//            try {
//                Optional<SyncTask> taskOpt = syncTaskRepository.findById(Long.parseLong(taskId));
//                if (taskOpt.isPresent()) {
//                    SyncTask syncTask = taskOpt.get();
//                    syncTask.setStatus("FAILED");
//                    syncTask.setLastUpdatedTime(new Date());
//                    syncTask.setChunkDetails("{\"error\":\"" + e.getMessage().replace("\"", "'") + "\"}");
//                    syncTaskRepository.save(syncTask);
//                }
//            } catch (Exception ex) {
//                log.error("更新同步任务状态失败", ex);
//            }
//
//            throw new RuntimeException("同步岗位数据失败: " + e.getMessage(), e);
//        }
//    }
//
//    /**
//     * 获取总记录数
//     */
//    private int getTotalRecordsCount() throws Exception {
//        Map<String, String> paramMap = new HashMap<>(2);
//        paramMap.put("limit", "1");
//        paramMap.put("offset", "0");
//
//        String jsonString = HttpUtil.get(positionServiceUrl, paramMap, timeout);
//        if (jsonString == null || jsonString.trim().isEmpty()) {
//            throw new RuntimeException("获取总记录数失败，API返回空数据");
//        }
//
//        JSONObject jsonObject = JSONUtil.parseObj(jsonString);
//        if (jsonObject.containsKey("total")) {
//            return jsonObject.getInt("total");
//        } else {
//            // 如果API不返回总数，默认返回一个较大值，确保数据同步完整
//            return 50000;
//        }
//    }
//
//    /**
//     * 处理单个页面的数据
//     */
//    private PageResult processPage(int pageNum, int pageSize) {
//        log.info("线程 [{}] 开始处理第 {} 页数据", Thread.currentThread().getName(), pageNum + 1);
//
//        int offset = pageNum * pageSize;
//        Map<String, String> paramMap = new HashMap<>(2);
//        paramMap.put("limit", String.valueOf(pageSize));
//        paramMap.put("offset", String.valueOf(offset));
//
//        int totalProcessed = 0;
//        int newRecordsCount = 0;
//        int updatedRecordsCount = 0;
//        int skippedRecordsCount = 0;
//
//        try {
//            // 获取数据
//            String jsonString = HttpUtil.get(positionServiceUrl, paramMap, timeout);
//            if (jsonString == null || jsonString.trim().isEmpty()) {
//                log.error("线程 [{}] 第 {} 页数据返回为空", Thread.currentThread().getName(), pageNum + 1);
//                return new PageResult(0, 0, 0, 0);
//            }
//
//            // 解析JSON数据
//            JSONObject jsonObject = JSONUtil.parseObj(jsonString);
//            JSONArray jsonArray = jsonObject.getJSONArray("data");
//            if (jsonArray == null || jsonArray.size() == 0) {
//                log.info("线程 [{}] 第 {} 页没有数据", Thread.currentThread().getName(), pageNum + 1);
//                return new PageResult(0, 0, 0, 0);
//            }
//
//            totalProcessed = jsonArray.size();
//
//            // 新增记录批处理列表
//            List<HrUserDetails> newRecords = new ArrayList<>();
//            // 需要更新的记录列表
//            List<HrUserDetails> recordsToUpdate = new ArrayList<>();
//
//            // 处理每条记录
//            for (Object object : jsonArray) {
//                try {
//                    HrUserDetails newDetail = JSONUtil.toBean(object.toString(), HrUserDetails.class);
//
//                    // 计算年龄
//                    if (newDetail.getBirthday() != null && !newDetail.getBirthday().isEmpty()) {
//                        try {
//                            String birthYear = newDetail.getBirthday().split("-")[0];
//                            Year currentYear = Year.now();
//                            int age = currentYear.getValue() - Integer.parseInt(birthYear);
//                            newDetail.setAge(String.valueOf(age));
//                        } catch (Exception e) {
//                            log.warn("计算用户 [{}] 年龄失败: {}", newDetail.getUserNo(), e.getMessage());
//                        }
//                    }
//
//                    // 检查用户工号是否为空
//                    if (newDetail.getUserNo() == null || newDetail.getUserNo().trim().isEmpty()) {
//                        log.warn("用户工号为空，跳过处理");
//                        continue;
//                    }
//
//                    // 查找现有记录
//                    HrUserDetails existingDetail = commonMapper.findHrDetailsByUserNo(newDetail.getUserNo());
//
//                    if (existingDetail != null) {
//                        // 现有记录存在，检查是否需要更新
//                        boolean needUpdate = isDetailChanged(existingDetail, newDetail);
//
//                        if (needUpdate) {
//                            // 保留原ID
//                            newDetail.setId(existingDetail.getId());
//                            recordsToUpdate.add(newDetail);
//                            updatedRecordsCount++;
//                        } else {
//                            // 无需更新
//                            skippedRecordsCount++;
//                        }
//                    } else {
//                        // 新记录，添加到批量插入列表
//                        newDetail.setId(IDUtil.generateId());
//                        newRecords.add(newDetail);
//                        newRecordsCount++;
//                    }
//                } catch (Exception e) {
//                    log.error("处理用户详情数据异常: {}", e.getMessage());
//                }
//            }
//
//            // 批量插入新记录
//            if (!newRecords.isEmpty()) {
//                // 批量分组插入
//                List<List<HrUserDetails>> batches = splitList(newRecords, BATCH_SIZE);
//                for (List<HrUserDetails> batch : batches) {
//                    try {
//                        int insertCount = commonMapper.insertBatchHrUserDetails(batch);
//                        log.info("线程 [{}] 批量插入 {} 条新岗位数据", Thread.currentThread().getName(), insertCount);
//                    } catch (Exception e) {
//                        log.error("批量插入岗位数据失败: {}", e.getMessage());
//                        throw e;
//                    }
//                }
//            }
//
//            // 批量更新记录
//            if (!recordsToUpdate.isEmpty()) {
//                for (HrUserDetails detail : recordsToUpdate) {
//                    try {
//                        commonMapper.updateHrUserDetails(detail);
//                    } catch (Exception e) {
//                        log.error("更新岗位数据失败: {}", e.getMessage());
//                    }
//                }
//                log.info("线程 [{}] 更新 {} 条岗位数据", Thread.currentThread().getName(), recordsToUpdate.size());
//            }
//
//            log.info("线程 [{}] 处理第 {} 页完成: 总处理 {} 条, 新增 {} 条, 更新 {} 条, 跳过 {} 条",
//                    Thread.currentThread().getName(), pageNum + 1, totalProcessed, newRecordsCount, updatedRecordsCount, skippedRecordsCount);
//
//            return new PageResult(totalProcessed, newRecordsCount, updatedRecordsCount, skippedRecordsCount);
//
//        } catch (Exception e) {
//            log.error("线程 [{}] 处理第 {} 页异常: {}", Thread.currentThread().getName(), pageNum + 1, e.getMessage());
//            return new PageResult(totalProcessed, newRecordsCount, updatedRecordsCount, skippedRecordsCount);
//        }
//    }
//
//    /**
//     * 将列表分割成指定大小的批次
//     */
//    private <T> List<List<T>> splitList(List<T> list, int batchSize) {
//        List<List<T>> batches = new ArrayList<>();
//        for (int i = 0; i < list.size(); i += batchSize) {
//            batches.add(list.subList(i, Math.min(i + batchSize, list.size())));
//        }
//        return batches;
//    }
//
//    /**
//     * 检查岗位信息是否发生变化
//     */
//    private boolean isDetailChanged(HrUserDetails oldDetail, HrUserDetails newDetail) {
//        // 检查关键字段是否有变化
//        if (!Objects.equals(oldDetail.getPost(), newDetail.getPost())) {
//            return true;
//        }
//
//        if (!Objects.equals(oldDetail.getDepartment(), newDetail.getDepartment()) ||
//                !Objects.equals(oldDetail.getDepartmentId(), newDetail.getDepartmentId())) {
//            return true;
//        }
//
//        if (!Objects.equals(oldDetail.getName(), newDetail.getName()) ||
//                !Objects.equals(oldDetail.getSex(), newDetail.getSex())) {
//            return true;
//        }
//
//        if (!Objects.equals(oldDetail.getSkillLevel(), newDetail.getSkillLevel()) ||
//                !Objects.equals(oldDetail.getWorkExp(), newDetail.getWorkExp())) {
//            return true;
//        }
//
//        if (!Objects.equals(oldDetail.getEducation(), newDetail.getEducation())) {
//            return true;
//        }
//
//        return false;
//    }
//
//    /**
//     * 页面处理结果类
//     */
//    private static class PageResult {
//        private final int totalProcessed;
//        private final int newRecords;
//        private final int updatedRecords;
//        private final int skippedRecords;
//
//        public PageResult(int totalProcessed, int newRecords, int updatedRecords, int skippedRecords) {
//            this.totalProcessed = totalProcessed;
//            this.newRecords = newRecords;
//            this.updatedRecords = updatedRecords;
//            this.skippedRecords = skippedRecords;
//        }
//
//        public int getTotalProcessed() {
//            return totalProcessed;
//        }
//
//        public int getNewRecords() {
//            return newRecords;
//        }
//
//        public int getUpdatedRecords() {
//            return updatedRecords;
//        }
//
//        public int getSkippedRecords() {
//            return skippedRecords;
//        }
//    }
//}