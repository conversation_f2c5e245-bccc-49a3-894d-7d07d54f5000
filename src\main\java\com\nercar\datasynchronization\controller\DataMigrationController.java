//package com.nercar.datasynchronization.controller;
//
//import com.nercar.datasynchronization.service.DataMigrationService;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.http.ResponseEntity;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import java.util.HashMap;
//import java.util.Map;
//
///**
// * 数据迁移控制器 - 负责数据从旧结构到新结构的迁移
// */
//@Slf4j
//@RestController
//@RequestMapping("/api/migration")
//public class DataMigrationController {
//
//    @Autowired
//    private DataMigrationService dataMigrationService;
//
//    /**
//     * 初始化所有新表数据
//     */
//    @PostMapping("/init-all")
//    public ResponseEntity<Map<String, Object>> initializeAllData() {
//        log.info("接收到初始化所有新表数据的请求");
//
//        Map<String, Object> response = new HashMap<>();
//
//        try {
//            dataMigrationService.initializeNewTables();
//
//            response.put("success", true);
//            response.put("message", "所有新表数据初始化成功");
//            return ResponseEntity.ok(response);
//
//        } catch (Exception e) {
//            log.error("初始化数据失败", e);
//
//            response.put("success", false);
//            response.put("message", "初始化数据失败: " + e.getMessage());
//            return ResponseEntity.badRequest().body(response);
//        }
//    }
//
//    /**
//     * 初始化岗位数据
//     */
//    @PostMapping("/init-positions")
//    public ResponseEntity<Map<String, Object>> initializePositionData() {
//        log.info("接收到初始化岗位数据的请求");
//
//        Map<String, Object> response = new HashMap<>();
//
//        try {
//            dataMigrationService.migratePositionInfo();
//
//            response.put("success", true);
//            response.put("message", "岗位数据初始化成功");
//            return ResponseEntity.ok(response);
//
//        } catch (Exception e) {
//            log.error("初始化岗位数据失败", e);
//
//            response.put("success", false);
//            response.put("message", "初始化岗位数据失败: " + e.getMessage());
//            return ResponseEntity.badRequest().body(response);
//        }
//    }
//
//    /**
//     * 初始化员工-部门关联数据
//     */
//    @PostMapping("/init-employee-department")
//    public ResponseEntity<Map<String, Object>> initializeEmployeeDepartment() {
//        log.info("接收到初始化员工-部门关联数据的请求");
//
//        Map<String, Object> response = new HashMap<>();
//
//        try {
//            dataMigrationService.createEmployeeDepartmentRelations();
//
//            response.put("success", true);
//            response.put("message", "员工-部门关联数据初始化成功");
//            return ResponseEntity.ok(response);
//
//        } catch (Exception e) {
//            log.error("初始化员工-部门关联数据失败", e);
//
//            response.put("success", false);
//            response.put("message", "初始化员工-部门关联数据失败: " + e.getMessage());
//            return ResponseEntity.badRequest().body(response);
//        }
//    }
//
//    /**
//     * 初始化部门-岗位关联数据
//     */
//    @PostMapping("/init-department-position")
//    public ResponseEntity<Map<String, Object>> initializeDepartmentPosition() {
//        log.info("接收到初始化部门-岗位关联数据的请求");
//
//        Map<String, Object> response = new HashMap<>();
//
//        try {
//            dataMigrationService.createDepartmentPositionRelations();
//
//            response.put("success", true);
//            response.put("message", "部门-岗位关联数据初始化成功");
//            return ResponseEntity.ok(response);
//
//        } catch (Exception e) {
//            log.error("初始化部门-岗位关联数据失败", e);
//
//            response.put("success", false);
//            response.put("message", "初始化部门-岗位关联数据失败: " + e.getMessage());
//            return ResponseEntity.badRequest().body(response);
//        }
//    }
//
//    /**
//     * 初始化员工-岗位关联数据
//     */
//    @PostMapping("/init-employee-position")
//    public ResponseEntity<Map<String, Object>> initializeEmployeePosition() {
//        log.info("接收到初始化员工-岗位关联数据的请求");
//
//        Map<String, Object> response = new HashMap<>();
//
//        try {
//            dataMigrationService.createEmployeePositionRelations();
//
//            response.put("success", true);
//            response.put("message", "员工-岗位关联数据初始化成功");
//            return ResponseEntity.ok(response);
//
//        } catch (Exception e) {
//            log.error("初始化员工-岗位关联数据失败", e);
//
//            response.put("success", false);
//            response.put("message", "初始化员工-岗位关联数据失败: " + e.getMessage());
//            return ResponseEntity.badRequest().body(response);
//        }
//    }
//}