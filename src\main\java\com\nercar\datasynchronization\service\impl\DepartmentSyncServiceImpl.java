package com.nercar.datasynchronization.service.impl;

import com.nercar.datasynchronization.entity.Department;
import com.nercar.datasynchronization.entity.DepartmentChild;
import com.nercar.datasynchronization.repository.DepartmentChildRepository;
import com.nercar.datasynchronization.repository.DepartmentRepository;
import com.nercar.datasynchronization.service.DepartmentSyncService;
import com.nercar.datasynchronization.utils.XmlUtils;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.Element;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.text.SimpleDateFormat;

@Slf4j
@Service
public class DepartmentSyncServiceImpl implements DepartmentSyncService {

    @Autowired
    private DepartmentRepository departmentRepository;

    @Autowired
    private DepartmentChildRepository departmentChildRepository;

    // 批处理大小
    private static final int BATCH_SIZE = 500;

    // 创建缓存，避免重复数据库查询
    private final Map<String, Integer> departmentIdCache = new ConcurrentHashMap<>();
    private final Map<String, Integer> childIdCache = new ConcurrentHashMap<>();

    @Override
    @Transactional(rollbackFor = Exception.class, timeout = 300,
                  propagation = Propagation.REQUIRED,
                  isolation = Isolation.READ_COMMITTED)
    public void syncDepartments(String xmlData) {
        // 调用带时间参数的重载方法，使用当前时间
        syncDepartments(xmlData, new Date(), new Date());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, timeout = 300,
                  propagation = Propagation.REQUIRED,
                  isolation = Isolation.READ_COMMITTED)
    public void syncDepartments(String xmlData, Date startDate, Date endDate) {
        try {
            long startTime = System.currentTimeMillis();
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            // 检查数据量大小，如果过大建议使用分片接口
            if (xmlData.length() > 50 * 1024 * 1024) { // 50MB
                log.warn("部门数据量过大 ({} MB)，建议使用分片接口 /api/sync/departments/chunked 进行同步",
                    xmlData.length() / (1024 * 1024));
            }

            log.info("开始同步部门数据，数据长度: {} 字符 ({} MB), 时间范围: {} 至 {}",
                    xmlData.length(),
                    String.format("%.2f", xmlData.length() / (1024.0 * 1024.0)),
                    dateFormat.format(startDate),
                    dateFormat.format(endDate));

            // 使用方法内的局部变量，避免共享状态
            List<Department> departmentBatch = new ArrayList<>(BATCH_SIZE);
            List<DepartmentChild> childBatch = new ArrayList<>(BATCH_SIZE);

            // 使用SAX解析器流式处理XML数据，传入局部变量批处理列表和时间参数
            XmlUtils.parseXmlWithSAX(xmlData, "O_DATA",
                element -> processDepartment(element, departmentBatch, childBatch, startDate, endDate));

            // 处理剩余的批次
            saveRemainingBatches(departmentBatch, childBatch);

            long endTime = System.currentTimeMillis();
            log.info("部门数据同步完成，耗时: {}ms", (endTime - startTime));
        } catch (Exception e) {
            // 根据异常类型提供更具体的错误信息
            if (e.getMessage() != null && e.getMessage().contains("XML 文档结构必须从头至尾包含在同一个实体内")) {
                log.error("XML解析失败：数据可能不完整或被截断。数据长度: {} 字符。建议：1) 检查网络连接稳定性 2) 缩小时间范围 3) 使用分片接口 /api/sync/departments/chunked",
                    xmlData.length());
                throw new RuntimeException("XML数据解析失败，可能是数据传输不完整。建议使用分片接口或缩小时间范围", e);
            } else if (e.getMessage() != null && e.getMessage().contains("响应数据不完整")) {
                log.error("SOAP响应数据不完整，建议缩小时间范围或使用分片接口");
                throw new RuntimeException("SOAP响应数据不完整，建议缩小时间范围或使用分片接口", e);
            } else {
                log.error("同步部门数据失败", e);
                throw new RuntimeException("同步部门数据失败", e);
            }
        }
    }

    // 保存剩余未满批次的数据
    private void saveRemainingBatches(List<Department> departmentBatch, List<DepartmentChild> childBatch) {
        if (!departmentBatch.isEmpty()) {
            saveDepartmentBatch(departmentBatch);
            departmentBatch.clear();
        }

        if (!childBatch.isEmpty()) {
            saveChildBatch(childBatch);
            childBatch.clear();
        }
    }

    // 将部门批处理保存操作封装为独立事务
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void saveDepartmentBatch(List<Department> departments) {
        try {
            departmentRepository.saveAll(departments);
        } catch (Exception e) {
            log.error("保存部门数据批次失败", e);
            throw e;
        }
    }

    // 将部门子表批处理保存操作封装为独立事务
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void saveChildBatch(List<DepartmentChild> children) {
        try {
            departmentChildRepository.saveAll(children);
        } catch (Exception e) {
            log.error("保存部门子表数据批次失败", e);
            throw e;
        }
    }

    private void processDepartment(Element dataElement,
                                   List<Department> departmentBatch,
                                   List<DepartmentChild> childBatch,
                                   Date startDate,
                                   Date endDate) {
        try {
            // 获取MDM ID（主数据系统内码）
            String mdmId = XmlUtils.getElementText(dataElement, "NM");
            if (mdmId == null || mdmId.isEmpty()) {
                log.warn("部门MDM ID为空，跳过处理");
                return;
            }

            // 解析部门基本信息
            Department department = new Department();
            department.setDeptUuid(mdmId);

            // 先检查缓存中是否已存在
            Integer departmentId = departmentIdCache.get(mdmId);
            if (departmentId == null) {
                // 缓存未命中，查询数据库
                Department existingDepartment = departmentRepository.findByDeptUuid(mdmId);
                if (existingDepartment != null) {
                    departmentId = existingDepartment.getId();
                    departmentIdCache.put(mdmId, departmentId);
                    department.setId(departmentId);
                    // 更新已存在记录
                    department.setUpdateTime(endDate);
                } else {
                    department.setUpdateTime(endDate);        // 新记录，只设置更新时间                    department.setUpdateTime(startDate);
                }
            } else {
                department.setId(departmentId);
                // 更新已存在记录
                department.setUpdateTime(endDate);
            }

            // 设置基本字段，使用全大写与XML匹配
            String orgCode = XmlUtils.getElementText(dataElement, "ORGCODE");
            if (orgCode == null || orgCode.isEmpty()) {
                log.warn("组织编码为空，跳过处理，部门ID: {}", mdmId);
                return;
            }
            department.setOrgCode(orgCode);  // 组织编号
            department.setOrgName(XmlUtils.getElementText(dataElement, "ORGNAME"));  // 组织名称
            department.setParentCode(XmlUtils.getElementText(dataElement, "PNODECODE"));  // 父级编码
            department.setFullName(XmlUtils.getElementText(dataElement, "ORAALLNAME"));  // 组织全称

            // 设置是否停用状态
            String isHistoryStr = XmlUtils.getElementText(dataElement, "ISHISTORY");
            if (isHistoryStr != null && !isHistoryStr.isEmpty()) {
                try {
                    department.setIsHistory(Integer.parseInt(isHistoryStr));
                } catch (NumberFormatException e) {
                    department.setIsHistory(0); // 默认值为启用
                }
            } else {
                department.setIsHistory(0); // 默认值为启用
            }

            // 设置其他字段
            department.setDescription(XmlUtils.getElementText(dataElement, "ORGDESC"));  // 简介
            department.setFax(XmlUtils.getElementText(dataElement, "FAX"));  // 传真
            department.setWebAddress(XmlUtils.getElementText(dataElement, "WEBADDR"));  // 网址
            department.setOrgManager(XmlUtils.getElementText(dataElement, "ORGFRDB"));  // 法人代表
            department.setPostCode(XmlUtils.getElementText(dataElement, "POSTCODE"));  // 邮编
            department.setUserPredef13(XmlUtils.getElementText(dataElement, "USERPREDEF_13"));  // 判重项目值域
            department.setUserPredef14(XmlUtils.getElementText(dataElement, "USERPREDEF_14"));  // 操作标识
            department.setUserPredef18(XmlUtils.getElementText(dataElement, "USERPREDEF_18"));  // 预留3

            // 添加新字段处理
            department.setParentNodeId(XmlUtils.getElementText(dataElement, "PNODEID"));  // 父级节点ID
            department.setOrgTypeCode(XmlUtils.getElementText(dataElement, "USERPREDEF_11"));  // 组织类型编码
            department.setBudgetCurrency(XmlUtils.getElementText(dataElement, "USERPREDEF_12"));  // 预算币种编码
            department.setUserPredef10(XmlUtils.getElementText(dataElement, "USERPREDEF_10"));  // 预留字段10
            department.setUserPredef17(XmlUtils.getElementText(dataElement, "USERPREDEF_17"));  // 预留字段17
            department.setIsLegalEntity(XmlUtils.getElementText(dataElement, "USERPREDEF_26"));  // 是否企业法人
            department.setRemarks(XmlUtils.getElementText(dataElement, "REMARKS"));  // 备注

            // 添加到批处理列表
            departmentBatch.add(department);

            // 批量保存
            if (departmentBatch.size() >= BATCH_SIZE) {
                saveDepartmentBatch(departmentBatch);
                departmentBatch.clear();
            }

            // 处理部门子表信息
            Element childElements = dataElement.element("O_CHILDS1");
            if (childElements != null) {
                List<Element> children = childElements.elements("O_CHILD");
                for (Element child : children) {
                    processDepartmentChild(child, mdmId, childBatch, startDate, endDate);
                }
            }
        } catch (Exception e) {
            log.error("处理部门元素失败: {}", e.getMessage(), e);
            // 不抛出异常，继续处理其他元素
        }
    }

    private void processDepartmentChild(Element element, String deptUuid, List<DepartmentChild> childBatch, Date startDate, Date endDate) {
        try {
            String guid = XmlUtils.getElementText(element, "NGTYYSB_GUID");
            if (guid == null || guid.isEmpty()) {
                return;
            }

            DepartmentChild child = new DepartmentChild();
            child.setGuid(guid);

            // 先检查缓存中是否已存在
            Integer childId = childIdCache.get(guid);
            if (childId == null) {
                // 缓存未命中，查询数据库
                DepartmentChild existingChild = departmentChildRepository.findByGuid(guid);
                if (existingChild != null) {
                    childId = existingChild.getId();
                    childIdCache.put(guid, childId);
                    child.setId(childId);
                    // 更新已存在记录
//                    child.setUpdateTime(endDate);
                } else {
                    // 新记录，同时设置创建时间和更新时间
//                    child.setCreateTime(startDate);
//                    child.setUpdateTime(endDate);
                }
            } else {
                child.setId(childId);
                // 更新已存在记录
//                child.setUpdateTime(endDate);
            }

            child.setDeptUuid(deptUuid);
            child.setSourceSystem(XmlUtils.getElementText(element, "NGTYYSB_LYXTBH"));
            child.setSourceDataNm(XmlUtils.getElementText(element, "NGTYYSB_LYXTDATANM"));
            child.setUdef1(XmlUtils.getElementText(element, "NGTYYSB_UDEF1"));
            child.setUdef2(XmlUtils.getElementText(element, "NGTYYSB_UDEF2"));
            child.setUdef3(XmlUtils.getElementText(element, "NGTYYSB_UDEF3"));
            child.setUdef4(XmlUtils.getElementText(element, "NGTYYSB_UDEF4"));
            child.setUdef5(XmlUtils.getElementText(element, "NGTYYSB_UDEF5"));
            child.setUdef6(XmlUtils.getElementText(element, "NGTYYSB_UDEF6"));

            // 添加新字段处理
            child.setDeptMdmId(XmlUtils.getElementText(element, "NGTYYSB_ZBNM"));  // 部门MDM关联ID
            child.setUdef7(XmlUtils.getElementText(element, "NGTYYSB_UDEF7"));  // 默认成本中心描述
            child.setUdef8(XmlUtils.getElementText(element, "NGTYYSB_UDEF8"));  // 用于审批组织编号
            child.setUdef9(XmlUtils.getElementText(element, "NGTYYSB_UDEF9"));  // 备用字段1
            child.setUdef10(XmlUtils.getElementText(element, "NGTYYSB_UDEF10"));  // 备用字段2
            child.setUdef11(XmlUtils.getElementText(element, "NGTYYSB_UDEF11"));  // 备用字段3
            child.setUdef12(XmlUtils.getElementText(element, "NGTYYSB_UDEF12"));  // 备用字段4
            child.setUdef13(XmlUtils.getElementText(element, "NGTYYSB_UDEF13"));  // 备用字段5
            child.setUdef14(XmlUtils.getElementText(element, "NGTYYSB_UDEF14"));  // 备用字段6
            child.setUdef15(XmlUtils.getElementText(element, "NGTYYSB_UDEF15"));  // 备用字段7
            child.setUdef16(XmlUtils.getElementText(element, "NGTYYSB_UDEF16"));  // 备用字段8
            child.setUdef17(XmlUtils.getElementText(element, "NGTYYSB_UDEF17"));  // 备用字段9
            child.setUdef18(XmlUtils.getElementText(element, "NGTYYSB_UDEF18"));  // 备用字段10

            // 添加到批处理列表
            childBatch.add(child);

            // 批量保存
            if (childBatch.size() >= BATCH_SIZE) {
                saveChildBatch(childBatch);
                childBatch.clear();
            }
        } catch (Exception e) {
            log.error("处理部门子项元素失败: {}", e.getMessage(), e);
            // 不抛出异常，继续处理其他元素
        }
    }
}