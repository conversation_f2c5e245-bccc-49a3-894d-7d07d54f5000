package com.nercar.datasynchronization.dto;

import lombok.Data;
import java.util.Date;

/**
 * 员工系统标识数据传输对象
 * 对应employee_system表，存储员工在其他系统中的标识信息
 */
@Data
public class EmployeeSystemDTO {

    /**
     * 全局唯一标识符
     */
    private String guid;

    /**
     * 关联的员工MDM ID
     */
    private String employeeMdmId;

    /**
     * 系统代码
     */
    private String systemCode;

    /**
     * 系统数据ID
     */
    private String systemDataId;

    /**
     * 组织代码
     */
    private String orgCode;

    /**
     * 部门代码
     */
    private String departmentCode;

    /**
     * 员工编号
     */
    private String employeeCode;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新时间
     */
    private Date updatedTime;

    /**
     * 登录账号
     */
    private String loginAccount;

    /**
     * 公司别标识
     */
    private String companyCode;
}
