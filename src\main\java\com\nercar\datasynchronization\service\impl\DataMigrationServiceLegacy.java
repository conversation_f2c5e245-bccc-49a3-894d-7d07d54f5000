//package com.nercar.datasynchronization.service.impl;
//
//import com.nercar.datasynchronization.entity.*;
//import com.nercar.datasynchronization.repository.*;
//import com.nercar.datasynchronization.utils.IDUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.util.*;
//
///**
// * 数据迁移服务
// * 负责创建各实体间的关联关系
// */
///**
// * 已弃用的数据迁移服务 - 保留用于兼容性，所有功能已经迁移到新的DataMigrationServiceImpl
// * @deprecated 请使用{@link DataMigrationServiceImpl}代替
// */
//@Slf4j
//@Service
//public class DataMigrationServiceLegacy {
//
//    @Autowired
//    private DepartmentRepository departmentRepository;
//
//    @Autowired
//    private EmployeeRepository employeeRepository;
//
//    @Autowired
//    private HrUserDetailsRepository hrUserDetailsRepository;
//
//    @Autowired
//    private PositionInfoRepository positionInfoRepository;
//
//    @Autowired
//    private EmployeeDepartmentRepository employeeDepartmentRepository;
//
//    @Autowired
//    private EmployeePositionRelationRepository employeePositionRelationRepository;
//
//    @Autowired
//    private DepartmentPositionRepository departmentPositionRepository;
//
//    /**
//     * 创建员工与部门的关联关系
//     * 在同步员工数据后调用此方法
//     */
//    @Transactional
//    public void createEmployeeDepartmentRelations() {
//        log.info("开始创建员工-部门关联关系");
//
//        try {
//            // 查找所有员工
//            List<Employee> employees = employeeRepository.findAll();
//            log.info("找到 {} 名员工需要建立部门关联", employees.size());
//
//            List<EmployeeDepartment> relations = new ArrayList<>();
//            int successCount = 0;
//            int failedCount = 0;
//
//            for (Employee employee : employees) {
//                // 根据员工的orgCode查找对应部门
//                String orgCode = employee.getOrgCode();
//                if (orgCode == null || orgCode.isEmpty()) {
//                    log.warn("员工 [{}] 缺少部门代码，无法建立关联", employee.getEmployeeCode());
//                    failedCount++;
//                    continue;
//                }
//
//                Department department = departmentRepository.findByOrgCode(orgCode);
//                if (department == null) {
//                    log.warn("员工 [{}] 的部门代码 [{}] 未找到对应部门", employee.getEmployeeCode(), orgCode);
//                    failedCount++;
//                    continue;
//                }
//
//                // 创建关联记录
//                EmployeeDepartment relation = new EmployeeDepartment();
//                relation.setEmployeeId(employee.getId());
//                relation.setDepartmentId(department.getId());
//                relation.setRelationshipType("PRIMARY"); // 默认为主要部门关系
//                relation.setIsActive(1); // 设置为活动状态
//                relation.setCreatedTime(new Date());
//
//                relations.add(relation);
//                successCount++;
//
//                // 批量保存，避免一次性保存太多数据
//                if (relations.size() >= 500) {
//                    employeeDepartmentRepository.saveAll(relations);
//                    log.info("已保存 {} 条员工-部门关联关系", relations.size());
//                    relations.clear();
//                }
//            }
//
//            // 保存剩余的关联记录
//            if (!relations.isEmpty()) {
//                employeeDepartmentRepository.saveAll(relations);
//                log.info("已保存 {} 条员工-部门关联关系", relations.size());
//            }
//
//            log.info("员工-部门关联关系创建完成: 成功 {} 条, 失败 {} 条", successCount, failedCount);
//        } catch (Exception e) {
//            log.error("创建员工-部门关联关系失败", e);
//            throw new RuntimeException("创建员工-部门关联关系失败: " + e.getMessage(), e);
//        }
//    }
//
//    /**
//     * 从HR用户详情中提取岗位信息并创建关联关系
//     * 在同步岗位数据后调用此方法
//     */
//    @Transactional
//    public void createPositionRelations() {
//        log.info("开始从HR用户详情中提取岗位信息并创建关联关系");
//
//        try {
//            // 查询所有HR用户详情
//            List<HrUserDetails> allDetails = hrUserDetailsRepository.findAll();
//            log.info("找到 {} 条HR用户详情记录", allDetails.size());
//
//            // 1. 提取并创建岗位信息
//            Map<String, PositionInfo> positionMap = extractPositionInfo(allDetails);
//
//            if (positionMap.isEmpty()) {
//                log.warn("未从HR用户详情中提取出任何岗位信息");
//                return;
//            }
//
//            log.info("从HR用户详情中提取出 {} 个唯一岗位", positionMap.size());
//
//            // 2. 创建员工-岗位关联关系
//            createEmployeePositionRelations(allDetails, positionMap);
//
//            // 3. 创建部门-岗位关联关系
//            createDepartmentPositionRelations(allDetails, positionMap);
//
//            log.info("岗位信息提取和关联关系建立完成");
//        } catch (Exception e) {
//            log.error("从HR用户详情中提取岗位信息并创建关联关系失败", e);
//            throw new RuntimeException("岗位关系建立失败: " + e.getMessage(), e);
//        }
//    }
//
//    /**
//     * 从HR用户详情中提取唯一岗位信息
//     */
//    private Map<String, PositionInfo> extractPositionInfo(List<HrUserDetails> details) {
//        log.info("开始提取唯一岗位信息");
//
//        Map<String, PositionInfo> positionMap = new HashMap<>();
//
//        // 清空现有岗位数据
//        positionInfoRepository.deleteAll();
//        log.info("已清空现有岗位数据");
//
//        // 提取唯一岗位
//        for (HrUserDetails detail : details) {
//            if (detail.getPost() != null && !detail.getPost().trim().isEmpty()) {
//                String positionName = detail.getPost().trim();
//
//                if (!positionMap.containsKey(positionName)) {
//                    // 创建新的岗位记录
//                    PositionInfo position = new PositionInfo();
//                    position.setId(IDUtil.generateId());
//                    position.setPositionName(positionName);
//                    position.setPositionCode("POS_" + Math.abs(positionName.hashCode())); // 生成唯一编码
//                    position.setSkillLevelCode(detail.getSkillLevel());
//                    position.setStatus("ACTIVE");
//                    position.setCreatedTime(new Date());
//
//                    positionMap.put(positionName, position);
//                }
//            }
//        }
//
//        // 批量保存岗位信息
//        List<PositionInfo> positions = new ArrayList<>(positionMap.values());
//        positionInfoRepository.saveAll(positions);
//        log.info("已保存 {} 个唯一岗位信息", positions.size());
//
//        return positionMap;
//    }
//
//    /**
//     * 创建员工与岗位的关联关系
//     */
//    private void createEmployeePositionRelations(List<HrUserDetails> details, Map<String, PositionInfo> positionMap) {
//        log.info("开始创建员工-岗位关联关系");
//
//        // 清空现有关联
//        employeePositionRelationRepository.deleteAll();
//        log.info("已清空现有员工-岗位关联关系");
//
//        List<EmployeePositionRelation> relations = new ArrayList<>();
//        Map<String, Set<String>> employeePositionMap = new HashMap<>(); // 防止重复关联
//        int successCount = 0;
//        int failedCount = 0;
//
//        for (HrUserDetails detail : details) {
//            if (detail.getPost() == null || detail.getUserNo() == null) {
//                continue;
//            }
//
//            String positionName = detail.getPost().trim();
//            PositionInfo position = positionMap.get(positionName);
//
//            if (position == null) {
//                log.warn("岗位 [{}] 未找到对应记录", positionName);
//                failedCount++;
//                continue;
//            }
//
//            // 查找员工
//            Employee employee = employeeRepository.findByEmployeeCode(detail.getUserNo().trim());
//            if (employee == null) {
//                log.warn("员工工号 [{}] 未找到对应记录", detail.getUserNo());
//                failedCount++;
//                continue;
//            }
//
//            String employeeKey = employee.getId().toString();
//            String positionKey = position.getId();
//
//            // 防止重复关联
//            if (!employeePositionMap.containsKey(employeeKey)) {
//                employeePositionMap.put(employeeKey, new HashSet<>());
//            }
//
//            if (!employeePositionMap.get(employeeKey).contains(positionKey)) {
//                // 查找员工所在部门
//                Department department = departmentRepository.findByOrgCode(employee.getOrgCode());
//                if (department == null) {
//                    log.warn("员工 [{}] 的部门代码 [{}] 未找到对应部门", employee.getEmployeeCode(), employee.getOrgCode());
//                    failedCount++;
//                    continue;
//                }
//
//                // 创建关联记录
//                EmployeePositionRelation relation = new EmployeePositionRelation();
//                relation.setId(Long.parseLong(IDUtil.generateId()));
//                relation.setEmployeeId(employee.getId());
//                relation.setEmployeeCode(employee.getEmployeeCode());
//                relation.setPositionId(position.getId());
//                relation.setDepartmentId(department.getId());
//                relation.setIsPrimary(1); // 设置为主要岗位
//
//                // 设置工作经验
//                if (detail.getWorkExp() != null && !detail.getWorkExp().isEmpty()) {
//                    try {
//                        relation.setWorkExp(Integer.parseInt(detail.getWorkExp()));
//                    } catch (NumberFormatException e) {
//                        log.warn("员工 [{}] 的工作经验 [{}] 格式错误", employee.getEmployeeCode(), detail.getWorkExp());
//                    }
//                }
//
//                relation.setCreatedTime(new Date());
//
//                relations.add(relation);
//                employeePositionMap.get(employeeKey).add(positionKey);
//                successCount++;
//
//                // 批量保存，避免一次性保存太多数据
//                if (relations.size() >= 500) {
//                    employeePositionRelationRepository.saveAll(relations);
//                    log.info("已保存 {} 条员工-岗位关联关系", relations.size());
//                    relations.clear();
//                }
//            }
//        }
//
//        // 保存剩余的关联记录
//        if (!relations.isEmpty()) {
//            employeePositionRelationRepository.saveAll(relations);
//            log.info("已保存 {} 条员工-岗位关联关系", relations.size());
//        }
//
//        log.info("员工-岗位关联关系创建完成: 成功 {} 条, 失败 {} 条", successCount, failedCount);
//    }
//
//    /**
//     * 创建部门与岗位的关联关系
//     */
//    private void createDepartmentPositionRelations(List<HrUserDetails> details, Map<String, PositionInfo> positionMap) {
//        log.info("开始创建部门-岗位关联关系");
//
//        // 清空现有关联
//        departmentPositionRepository.deleteAll();
//        log.info("已清空现有部门-岗位关联关系");
//
//        // 使用部门ID和岗位ID作为键，统计每个部门中各岗位的人数
//        Map<Integer, Map<String, Integer>> departmentPositionCount = new HashMap<>();
//
//        // 统计部门岗位人数
//        for (HrUserDetails detail : details) {
//            if (detail.getPost() == null || detail.getDepartmentId() == null) {
//                continue;
//            }
//
//            String positionName = detail.getPost().trim();
//            PositionInfo position = positionMap.get(positionName);
//
//            if (position == null) {
//                continue;
//            }
//
//            // 查找部门
//            Department department;
//            try {
//                // 尝试通过ID查找
//                int departmentId = Integer.parseInt(detail.getDepartmentId());
//                department = departmentRepository.findById(departmentId).orElse(null);
//            } catch (NumberFormatException e) {
//                // 如果ID不是数字，尝试通过名称查找
//                department = departmentRepository.findByOrgName(detail.getDepartment());
//            }
//
//            if (department == null) {
//                log.warn("部门ID [{}] 或名称 [{}] 未找到对应记录", detail.getDepartmentId(), detail.getDepartment());
//                continue;
//            }
//
//            // 统计部门中各岗位的人数
//            int departmentId = department.getId();
//            String positionId = position.getId();
//
//            if (!departmentPositionCount.containsKey(departmentId)) {
//                departmentPositionCount.put(departmentId, new HashMap<>());
//            }
//
//            Map<String, Integer> positionCounts = departmentPositionCount.get(departmentId);
//            positionCounts.put(positionId, positionCounts.getOrDefault(positionId, 0) + 1);
//        }
//
//        // 创建部门-岗位关联
//        List<DepartmentPosition> relations = new ArrayList<>();
//
//        for (Map.Entry<Integer, Map<String, Integer>> entry : departmentPositionCount.entrySet()) {
//            Integer departmentId = entry.getKey();
//            Map<String, Integer> positionCounts = entry.getValue();
//
//            for (Map.Entry<String, Integer> posEntry : positionCounts.entrySet()) {
//                String positionId = posEntry.getKey();
//                Integer count = posEntry.getValue();
//
//                // 创建关联记录
//                DepartmentPosition relation = new DepartmentPosition();
//                relation.setDepartmentId(departmentId);
//                relation.setPositionId(positionId);
//                relation.setHeadcountLimit(count); // 以当前人数作为限额
//                relation.setIsActive(1); // 设置为活动状态
//                relation.setCreatedTime(new Date());
//
//                relations.add(relation);
//            }
//        }
//
//        // 保存关联记录
//        departmentPositionRepository.saveAll(relations);
//        log.info("已保存 {} 条部门-岗位关联关系", relations.size());
//
//        log.info("部门-岗位关联关系创建完成");
//    }
//}