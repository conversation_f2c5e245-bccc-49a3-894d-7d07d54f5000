package com.nercar.datasynchronization.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.util.Date;

/**
 * 部门实体 - 存储组织结构中的部门信息
 * 对应文档1.5.1组织信息下发接口的主表
 */
@Data
@Entity
@Table(name = "department")
@DynamicInsert
@DynamicUpdate
public class Department {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "dept_uuid")
    private String deptUuid;

    @Column(name = "org_code")
    private String orgCode;

    @Column(name = "org_name")
    private String orgName;

    @Column(name = "parent_id")
    private String parentId;

    @Column(name = "parent_code")
    private String parentCode;

    @Column(name = "full_name")
    private String fullName;

    @Column(name = "is_history")
    private Integer isHistory;

    @Column(name = "description")
    private String description;

    @Column(name = "fax")
    private String fax;

    @Column(name = "web_address")
    private String webAddress;

    @Column(name = "org_manager")
    private String orgManager;

    @Column(name = "post_code")
    private String postCode;

    @Column(name = "user_predef_13")
    private String userPredef13;

    @Column(name = "user_predef_14")
    private String userPredef14;

    @Column(name = "user_predef_18")
    private String userPredef18;

    @Column(name = "update_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateTime;

    @Column(name = "parent_node_id")
    private String parentNodeId;

    @Column(name = "org_type_code")
    private String orgTypeCode;

    @Column(name = "budget_currency")
    private String budgetCurrency;

    @Column(name = "user_predef_10")
    private String userPredef10;

    @Column(name = "user_predef_17")
    private String userPredef17;

    @Column(name = "is_legal_entity")
    private String isLegalEntity;

    @Column(name = "remarks")
    private String remarks;
}