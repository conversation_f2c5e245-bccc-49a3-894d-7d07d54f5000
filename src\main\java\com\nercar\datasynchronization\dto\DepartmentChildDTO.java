package com.nercar.datasynchronization.dto;

import lombok.Data;

/**
 * 部门子表数据传输对象
 * 对应department_child表，存储部门在其他系统中的标识信息
 */
@Data
public class DepartmentChildDTO {

    /**
     * 全局唯一标识符
     */
    private String guid;

    /**
     * 关联的部门UUID
     */
    private String deptUuid;

    /**
     * 来源系统
     */
    private String sourceSystem;

    /**
     * 来源系统数据标识
     */
    private String sourceDataNm;

    /**
     * 自定义字段1
     */
    private String udef1;

    /**
     * 自定义字段2
     */
    private String udef2;

    /**
     * 自定义字段3
     */
    private String udef3;

    /**
     * 自定义字段4
     */
    private String udef4;

    /**
     * 自定义字段5
     */
    private String udef5;

    /**
     * 自定义字段6
     */
    private String udef6;

    /**
     * 部门MDM关联ID
     */
    private String deptMdmId;

    /**
     * 默认成本中心描述
     */
    private String udef7;

    /**
     * 用于审批组织编号
     */
    private String udef8;

    /**
     * 备用字段1
     */
    private String udef9;

    /**
     * 备用字段2
     */
    private String udef10;

    /**
     * 备用字段3
     */
    private String udef11;

    /**
     * 备用字段4
     */
    private String udef12;

    /**
     * 备用字段5
     */
    private String udef13;

    /**
     * 备用字段6
     */
    private String udef14;

    /**
     * 备用字段7
     */
    private String udef15;

    /**
     * 备用字段8
     */
    private String udef16;

    /**
     * 备用字段9
     */
    private String udef17;

    /**
     * 备用字段10
     */
    private String udef18;
}
