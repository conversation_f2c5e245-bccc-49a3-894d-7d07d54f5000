-- =====================================================
-- 基于department_sync_test2.sql重构的标准化部门层级结构
-- 生成时间: 2025-07-25
-- 数据来源: department_sync_test2.sql (1399条记录)
-- =====================================================

-- 创建重构后的部门表
DROP TABLE IF EXISTS department_hierarchy_new;
CREATE TABLE department_hierarchy_new (
    id INT AUTO_INCREMENT PRIMARY KEY,
    dept_code VARCHAR(20) NOT NULL UNIQUE,
    dept_name VARCHAR(200) NOT NULL,
    dept_level INT NOT NULL,
    parent_code VARCHAR(20),
    full_path VARCHAR(500),
    dept_type VARCHAR(50),
    original_org_codes TEXT,
    is_active TINYINT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_parent_code (parent_code),
    INDEX idx_dept_level (dept_level),
    INDEX idx_dept_type (dept_type)
);

-- =====================================================
-- Level 1: 一级部门（事业部/集团/公司级别）
-- =====================================================

INSERT INTO department_hierarchy_new (dept_code, dept_name, dept_level, parent_code, full_path, dept_type, original_org_codes) VALUES
('L1_001', '板材事业部', 1, NULL, '板材事业部', '事业部', 'X50000000'),
('L1_002', '特钢事业部', 1, NULL, '特钢事业部', '事业部', 'X49000000'),
('L1_003', '炼铁事业部', 1, NULL, '炼铁事业部', '事业部', 'X47000000'),
('L1_004', '能源动力事业部', 1, NULL, '能源动力事业部', '事业部', 'X48000000'),
('L1_005', '物流中心', 1, NULL, '物流中心', '中心', 'X52000000'),
('L1_006', '采购中心', 1, NULL, '采购中心', '中心', 'X53000000'),
('L1_007', '新产业投资集团', 1, NULL, '新产业投资集团', '集团', 'X57000000'),
('L1_008', '蔚蓝高科技集团', 1, NULL, '蔚蓝高科技集团', '集团', 'XB1000000'),
('L1_009', '江苏南钢鑫洋供应链有限公司', 1, NULL, '江苏南钢鑫洋供应链有限公司', '公司', 'X79000000'),
('L1_010', '江苏金珂水务有限公司', 1, NULL, '江苏金珂水务有限公司', '公司', 'X83000000'),
('L1_011', '南京钢铁集团国际经济贸易有限公司', 1, NULL, '南京钢铁集团国际经济贸易有限公司', '公司', 'X36000000'),
('L1_012', '南京三金房地产开发有限公司', 1, NULL, '南京三金房地产开发有限公司', '公司', 'XC5000000'),
('L1_013', '科技质量部', 1, NULL, '科技质量部', '部门', 'X10000000'),
('L1_014', '人力资源部', 1, NULL, '人力资源部', '部门', 'X02000000'),
('L1_015', '财务部', 1, NULL, '财务部', '部门', 'X06000000'),
('L1_016', '公司办公室', 1, NULL, '公司办公室', '部门', 'X01000000'),
('L1_017', '安全环保部', 1, NULL, '安全环保部', '部门', 'X28000000'),
('L1_018', '制造部', 1, NULL, '制造部', '部门', 'X51000000'),
('L1_019', '新材料研究院（合署）', 1, NULL, '新材料研究院（合署）', '研究院', 'X27000000'),
('L1_020', '数字应用研究院（人工智能研究院）', 1, NULL, '数字应用研究院（人工智能研究院）', '研究院', 'XB2000000'),
('L1_021', '工会', 1, NULL, '工会', '部门', 'X17000000,XC2000000'),
('L1_022', '保卫部', 1, NULL, '保卫部', '部门', 'X15000000'),
('L1_023', '审计部', 1, NULL, '审计部', '部门', 'XB3000000,XC1000000'),
('L1_024', '风险合规部', 1, NULL, '风险合规部', '部门', 'X08000000,XC3000000'),
('L1_025', '董事会办公室', 1, NULL, '董事会办公室', '部门', 'XC7000000'),
('L1_026', '党委办公室', 1, NULL, '党委办公室', '部门', 'XB4000000'),
('L1_027', '组织部', 1, NULL, '组织部', '部门', 'XB5000000'),
('L1_028', '党委工作部', 1, NULL, '党委工作部', '部门', 'XB7000000'),
('L1_029', '纪委办公室（党风廉政办公室）', 1, NULL, '纪委办公室（党风廉政办公室）', '部门', 'X29000000'),
('L1_030', '企业文化部', 1, NULL, '企业文化部', '部门', 'X03000000'),
('L1_031', '战略运营部（产业发展研究院）', 1, NULL, '战略运营部（产业发展研究院）', '部门', 'X04000000'),
('L1_032', '市场部', 1, NULL, '市场部', '部门', 'X24000000'),
('L1_033', '证券部', 1, NULL, '证券部', '部门', 'X26000000'),
('L1_034', '公司领导', 1, NULL, '公司领导', '领导层', 'X00000000'),
('L1_035', '集团领导', 1, NULL, '集团领导', '领导层', 'XA0000000');

-- =====================================================
-- Level 2: 二级部门（厂/处级别）
-- =====================================================

INSERT INTO department_hierarchy_new (dept_code, dept_name, dept_level, parent_code, full_path, dept_type, original_org_codes) VALUES
-- 板材事业部下属
('L2_001', '中厚板卷厂', 2, 'L1_001', '板材事业部 -> 中厚板卷厂', '厂', 'X32000000'),
('L2_002', '宽厚板厂', 2, 'L1_001', '板材事业部 -> 宽厚板厂', '厂', 'X38000000'),
('L2_003', '第一炼钢厂', 2, 'L1_001', '板材事业部 -> 第一炼钢厂', '厂', 'X73000000'),
('L2_004', '中板厂', 2, 'L1_001', '板材事业部 -> 中板厂', '厂', 'X66000000'),
('L2_005', '金石材料厂', 2, 'L1_001', '板材事业部 -> 金石材料厂', '厂', 'X84000000'),
('L2_006', '技术研发处', 2, 'L1_001', '板材事业部 -> 技术研发处', '处', 'X50070000'),
('L2_007', '设备处', 2, 'L1_001', '板材事业部 -> 设备处', '处', 'X50040000'),
('L2_008', '江苏南钢板材销售有限公司', 2, 'L1_001', '板材事业部 -> 江苏南钢板材销售有限公司', '公司', 'X80000000'),

-- 特钢事业部下属
('L2_009', '第二炼钢厂', 2, 'L1_002', '特钢事业部 -> 第二炼钢厂', '厂', 'X63000000'),
('L2_010', '棒材厂', 2, 'L1_002', '特钢事业部 -> 棒材厂', '厂', 'X65000000'),
('L2_011', '精整厂', 2, 'L1_002', '特钢事业部 -> 精整厂', '厂', 'X41000000'),
('L2_012', '大棒厂', 2, 'L1_002', '特钢事业部 -> 大棒厂', '厂', 'X59000000'),
('L2_013', '高线厂', 2, 'L1_002', '特钢事业部 -> 高线厂', '厂', 'X46000000'),
('L2_014', '中棒厂', 2, 'L1_002', '特钢事业部 -> 中棒厂', '厂', 'X60000000'),
('L2_015', '特带厂', 2, 'L1_002', '特钢事业部 -> 特带厂', '厂', 'X98000000'),
('L2_016', '技术研发处', 2, 'L1_002', '特钢事业部 -> 技术研发处', '处', 'X49060000'),
('L2_017', '综合处', 2, 'L1_002', '特钢事业部 -> 综合处', '处', 'X49010000'),
('L2_018', '质量处', 2, 'L1_002', '特钢事业部 -> 质量处', '处', 'X49030000'),
('L2_019', '生产处', 2, 'L1_002', '特钢事业部 -> 生产处', '处', 'X49050000'),
('L2_020', '营销处', 2, 'L1_002', '特钢事业部 -> 营销处', '处', 'X49070000'),
('L2_021', '安全环保处', 2, 'L1_002', '特钢事业部 -> 安全环保处', '处', 'X49080000'),
('L2_022', '南京南钢特钢长材有限公司', 2, 'L1_002', '特钢事业部 -> 南京南钢特钢长材有限公司', '公司', 'X81000000'),

-- 炼铁事业部下属
('L2_023', '第一炼铁厂', 2, 'L1_003', '炼铁事业部 -> 第一炼铁厂', '厂', 'X31000000'),
('L2_024', '第二炼铁厂', 2, 'L1_003', '炼铁事业部 -> 第二炼铁厂', '厂', 'X62000000'),
('L2_025', '原料厂', 2, 'L1_003', '炼铁事业部 -> 原料厂', '厂', 'X43000000'),
('L2_026', '球团厂', 2, 'L1_003', '炼铁事业部 -> 球团厂', '厂', 'X44000000'),
('L2_027', '燃料供应厂', 2, 'L1_003', '炼铁事业部 -> 燃料供应厂', '厂', 'X70000000'),
('L2_028', '烧结厂', 2, 'L1_003', '炼铁事业部 -> 烧结厂', '厂', 'X86000000'),
('L2_029', '技术处', 2, 'L1_003', '炼铁事业部 -> 技术处', '处', 'X47080000'),
('L2_030', '设备处', 2, 'L1_003', '炼铁事业部 -> 设备处', '处', 'X47040000'),

-- 能源动力事业部下属
('L2_031', '制氧厂', 2, 'L1_004', '能源动力事业部 -> 制氧厂', '厂', 'X34000000'),
('L2_032', '水厂', 2, 'L1_004', '能源动力事业部 -> 水厂', '厂', 'X74000000'),
('L2_033', '燃气厂', 2, 'L1_004', '能源动力事业部 -> 燃气厂', '厂', 'X76000000'),
('L2_034', '发电厂', 2, 'L1_004', '能源动力事业部 -> 发电厂', '厂', 'X85000000'),
('L2_035', '设备处', 2, 'L1_004', '能源动力事业部 -> 设备处', '处', 'X48040000'),
('L2_036', '安全环保处', 2, 'L1_004', '能源动力事业部 -> 安全环保处', '处', 'X48010000'),
('L2_037', '生产质量处', 2, 'L1_004', '能源动力事业部 -> 生产质量处', '处', 'X48020000'),
('L2_038', '能源管理处', 2, 'L1_004', '能源动力事业部 -> 能源管理处', '处', 'X48070000'),
('L2_039', '江苏金灿能源科技有限公司', 2, 'L1_004', '能源动力事业部 -> 江苏金灿能源科技有限公司', '公司', 'X48080000'),

-- 物流中心下属
('L2_040', '铁路运输中心', 2, 'L1_005', '物流中心 -> 铁路运输中心', '中心', 'X35000000'),
('L2_041', '产成品储运室', 2, 'L1_005', '物流中心 -> 产成品储运室', '室', 'X52050000'),
('L2_042', '材料仓储室', 2, 'L1_005', '物流中心 -> 材料仓储室', '室', 'X52060000'),
('L2_043', '安全环保室', 2, 'L1_005', '物流中心 -> 安全环保室', '室', 'X52070000'),

-- 采购中心下属
('L2_044', '江苏南钢环宇贸易有限公司', 2, 'L1_006', '采购中心 -> 江苏南钢环宇贸易有限公司', '公司', 'X78000000'),
('L2_045', '材料室', 2, 'L1_006', '采购中心 -> 材料室', '室', 'X53030000'),
('L2_046', '设备备件室', 2, 'L1_006', '采购中心 -> 设备备件室', '室', 'X53040000');

-- =====================================================
-- Level 3: 三级部门（车间/室/科级别）
-- =====================================================

INSERT INTO department_hierarchy_new (dept_code, dept_name, dept_level, parent_code, full_path, dept_type, original_org_codes) VALUES
-- 第一炼钢厂下属车间
('L3_001', '炼钢车间', 3, 'L2_003', '板材事业部 -> 第一炼钢厂 -> 炼钢车间', '车间', 'X73020000'),
('L3_002', '精炼车间', 3, 'L2_003', '板材事业部 -> 第一炼钢厂 -> 精炼车间', '车间', 'X73030000'),
('L3_003', '连铸车间', 3, 'L2_003', '板材事业部 -> 第一炼钢厂 -> 连铸车间', '车间', 'X73040000'),
('L3_004', '石灰车间', 3, 'L2_003', '板材事业部 -> 第一炼钢厂 -> 石灰车间', '车间', 'X73070000'),
('L3_005', '坯料车间', 3, 'L2_003', '板材事业部 -> 第一炼钢厂 -> 坯料车间', '车间', 'X73140000'),
('L3_006', '综合科', 3, 'L2_003', '板材事业部 -> 第一炼钢厂 -> 综合科', '科', 'X73010000'),

-- 第二炼钢厂下属车间
('L3_007', '电炉炼钢车间', 3, 'L2_009', '特钢事业部 -> 第二炼钢厂 -> 电炉炼钢车间', '车间', 'X63000600'),
('L3_008', '电炉精炼车间', 3, 'L2_009', '特钢事业部 -> 第二炼钢厂 -> 电炉精炼车间', '车间', 'X63000200'),
('L3_009', '电炉连铸车间', 3, 'L2_009', '特钢事业部 -> 第二炼钢厂 -> 电炉连铸车间', '车间', 'X63000500'),
('L3_010', '电炉运行车间', 3, 'L2_009', '特钢事业部 -> 第二炼钢厂 -> 电炉运行车间', '车间', 'X63000400'),
('L3_011', '电炉检修车间', 3, 'L2_009', '特钢事业部 -> 第二炼钢厂 -> 电炉检修车间', '车间', 'X63000300'),
('L3_012', '连铸管理室', 3, 'L2_009', '特钢事业部 -> 第二炼钢厂 -> 连铸管理室', '室', 'X63000100'),

-- 宽厚板厂下属车间
('L3_013', '板加车间', 3, 'L2_002', '板材事业部 -> 宽厚板厂 -> 板加车间', '车间', 'X38060000'),
('L3_014', '安全环保科', 3, 'L2_002', '板材事业部 -> 宽厚板厂 -> 安全环保科', '科', 'X38050000'),

-- 中厚板卷厂下属
('L3_015', '配送车间', 3, 'L2_001', '板材事业部 -> 中厚板卷厂 -> 配送车间', '车间', 'X32110000'),
('L3_016', '综合科', 3, 'L2_001', '板材事业部 -> 中厚板卷厂 -> 综合科', '科', 'X32010000'),
('L3_017', '安全环保科', 3, 'L2_001', '板材事业部 -> 中厚板卷厂 -> 安全环保科', '科', 'X32160000'),

-- 中板厂下属
('L3_018', '综合科', 3, 'L2_004', '板材事业部 -> 中板厂 -> 综合科', '科', 'X66010000'),
('L3_019', '安全科', 3, 'L2_004', '板材事业部 -> 中板厂 -> 安全科', '科', 'X66120000'),
('L3_020', '产品管理室', 3, 'L2_004', '板材事业部 -> 中板厂 -> 产品管理室', '室', 'X66130000'),

-- 金石材料厂下属
('L3_021', '渣处理车间', 3, 'L2_005', '板材事业部 -> 金石材料厂 -> 渣处理车间', '车间', 'X84020000'),
('L3_022', '石灰车间', 3, 'L2_005', '板材事业部 -> 金石材料厂 -> 石灰车间', '车间', 'X84030000');

-- =====================================================
-- Level 4: 四级部门（班组级别）
-- =====================================================

INSERT INTO department_hierarchy_new (dept_code, dept_name, dept_level, parent_code, full_path, dept_type, original_org_codes) VALUES
-- 板加车间下属班组
('L4_001', '加热炉甲班', 4, 'L3_013', '板材事业部 -> 宽厚板厂 -> 板加车间 -> 加热炉甲班', '班', 'X38060001'),
('L4_002', '加热炉乙班', 4, 'L3_013', '板材事业部 -> 宽厚板厂 -> 板加车间 -> 加热炉乙班', '班', 'X38060002'),
('L4_003', '加热炉丙班', 4, 'L3_013', '板材事业部 -> 宽厚板厂 -> 板加车间 -> 加热炉丙班', '班', 'X38060003'),
('L4_004', '加热炉丁班', 4, 'L3_013', '板材事业部 -> 宽厚板厂 -> 板加车间 -> 加热炉丁班', '班', 'X38060004'),

-- 电炉炼钢车间下属班组
('L4_005', '电炉甲班', 4, 'L3_007', '特钢事业部 -> 第二炼钢厂 -> 电炉炼钢车间 -> 电炉甲班', '班', 'X63000601'),
('L4_006', '电炉乙班', 4, 'L3_007', '特钢事业部 -> 第二炼钢厂 -> 电炉炼钢车间 -> 电炉乙班', '班', 'X63000602'),
('L4_007', '电炉丙班', 4, 'L3_007', '特钢事业部 -> 第二炼钢厂 -> 电炉炼钢车间 -> 电炉丙班', '班', 'X63000603'),
('L4_008', '电炉丁班', 4, 'L3_007', '特钢事业部 -> 第二炼钢厂 -> 电炉炼钢车间 -> 电炉丁班', '班', 'X63000604'),
('L4_009', '辅助班', 4, 'L3_007', '特钢事业部 -> 第二炼钢厂 -> 电炉炼钢车间 -> 辅助班', '班', 'X63000605'),

-- 电炉精炼车间下属班组
('L4_010', '钢包班', 4, 'L3_008', '特钢事业部 -> 第二炼钢厂 -> 电炉精炼车间 -> 钢包班', '班', 'X63000201'),
('L4_011', '精炼炉甲班', 4, 'L3_008', '特钢事业部 -> 第二炼钢厂 -> 电炉精炼车间 -> 精炼炉甲班', '班', 'X63000205'),
('L4_012', '精炼炉乙班', 4, 'L3_008', '特钢事业部 -> 第二炼钢厂 -> 电炉精炼车间 -> 精炼炉乙班', '班', 'X63000204'),
('L4_013', '精炼炉丙班', 4, 'L3_008', '特钢事业部 -> 第二炼钢厂 -> 电炉精炼车间 -> 精炼炉丙班', '班', 'X63000203'),
('L4_014', '精炼炉丁班', 4, 'L3_008', '特钢事业部 -> 第二炼钢厂 -> 电炉精炼车间 -> 精炼炉丁班', '班', 'X63000202'),

-- 电炉运行车间下属班组
('L4_015', '配料丙班', 4, 'L3_010', '特钢事业部 -> 第二炼钢厂 -> 电炉运行车间 -> 配料丙班', '班', 'X63000402'),
('L4_016', '行车甲班', 4, 'L3_010', '特钢事业部 -> 第二炼钢厂 -> 电炉运行车间 -> 行车甲班', '班', 'X63000408'),
('L4_017', '行车丙班', 4, 'L3_010', '特钢事业部 -> 第二炼钢厂 -> 电炉运行车间 -> 行车丙班', '班', 'X63000406'),

-- 电炉检修车间下属班组
('L4_018', '钳工班', 4, 'L3_011', '特钢事业部 -> 第二炼钢厂 -> 电炉检修车间 -> 钳工班', '班', 'X63000301'),
('L4_019', '电工班', 4, 'L3_011', '特钢事业部 -> 第二炼钢厂 -> 电炉检修车间 -> 电工班', '班', 'X63000302'),

-- 产品管理室下属班组
('L4_020', '检验班', 4, 'L3_020', '板材事业部 -> 中板厂 -> 产品管理室 -> 检验班', '班', 'X66130001'),
('L4_021', '探伤班', 4, 'L3_020', '板材事业部 -> 中板厂 -> 产品管理室 -> 探伤班', '班', 'X66130002');

-- =====================================================
-- 创建原始数据映射表
-- =====================================================

DROP TABLE IF EXISTS original_dept_mapping;
CREATE TABLE original_dept_mapping (
    id INT AUTO_INCREMENT PRIMARY KEY,
    original_org_code VARCHAR(20) NOT NULL,
    original_org_name VARCHAR(200),
    original_parent_code VARCHAR(20),
    original_full_name VARCHAR(500),
    new_dept_code VARCHAR(20),
    mapping_level INT,
    mapping_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_original_org_code (original_org_code),
    INDEX idx_new_dept_code (new_dept_code)
);

-- =====================================================
-- 统计信息
-- =====================================================

SELECT 
    '=== 重构后的部门层级统计 ===' as section,
    dept_level as level,
    dept_type,
    COUNT(*) as count
FROM department_hierarchy_new 
GROUP BY dept_level, dept_type 
ORDER BY dept_level, dept_type;

SELECT 
    '=== 各事业部下属部门统计 ===' as section,
    parent_code,
    COUNT(*) as sub_dept_count
FROM department_hierarchy_new 
WHERE dept_level = 2
GROUP BY parent_code 
ORDER BY sub_dept_count DESC;

-- =====================================================
-- 数据迁移脚本：将原始数据映射到新结构
-- =====================================================

-- 插入原始数据映射关系
INSERT INTO original_dept_mapping (original_org_code, original_org_name, original_parent_code, original_full_name, new_dept_code, mapping_level, mapping_notes)
SELECT
    org_code,
    org_name,
    parent_code,
    COALESCE(full_name, org_name) as full_name,
    CASE
        -- Level 1 映射
        WHEN org_code = 'X50000000' THEN 'L1_001'
        WHEN org_code = 'X49000000' THEN 'L1_002'
        WHEN org_code = 'X47000000' THEN 'L1_003'
        WHEN org_code = 'X48000000' THEN 'L1_004'
        WHEN org_code = 'X52000000' THEN 'L1_005'
        WHEN org_code = 'X53000000' THEN 'L1_006'
        WHEN org_code = 'X57000000' THEN 'L1_007'
        WHEN org_code = 'XB1000000' THEN 'L1_008'
        WHEN org_code = 'X79000000' THEN 'L1_009'
        WHEN org_code = 'X83000000' THEN 'L1_010'
        WHEN org_code = 'X36000000' THEN 'L1_011'
        WHEN org_code = 'XC5000000' THEN 'L1_012'
        WHEN org_code = 'X10000000' THEN 'L1_013'
        WHEN org_code = 'X02000000' THEN 'L1_014'
        WHEN org_code = 'X06000000' THEN 'L1_015'
        WHEN org_code = 'X01000000' THEN 'L1_016'
        WHEN org_code = 'X28000000' THEN 'L1_017'
        WHEN org_code = 'X51000000' THEN 'L1_018'
        WHEN org_code = 'X27000000' THEN 'L1_019'
        WHEN org_code = 'XB2000000' THEN 'L1_020'
        WHEN org_code IN ('X17000000','XC2000000') THEN 'L1_021'
        WHEN org_code = 'X15000000' THEN 'L1_022'
        WHEN org_code IN ('XB3000000','XC1000000') THEN 'L1_023'
        WHEN org_code IN ('X08000000','XC3000000') THEN 'L1_024'
        WHEN org_code = 'XC7000000' THEN 'L1_025'
        WHEN org_code = 'X00000000' THEN 'L1_034'

        -- Level 2 映射
        WHEN org_code = 'X32000000' THEN 'L2_001'
        WHEN org_code = 'X38000000' THEN 'L2_002'
        WHEN org_code = 'X73000000' THEN 'L2_003'
        WHEN org_code = 'X66000000' THEN 'L2_004'
        WHEN org_code = 'X84000000' THEN 'L2_005'
        WHEN org_code = 'X50070000' THEN 'L2_006'
        WHEN org_code = 'X50040000' THEN 'L2_007'
        WHEN org_code = 'X63000000' THEN 'L2_009'
        WHEN org_code = 'X65000000' THEN 'L2_010'
        WHEN org_code = 'X41000000' THEN 'L2_011'
        WHEN org_code = 'X59000000' THEN 'L2_012'
        WHEN org_code = 'X46000000' THEN 'L2_013'
        WHEN org_code = 'X60000000' THEN 'L2_014'
        WHEN org_code = 'X98000000' THEN 'L2_015'
        WHEN org_code = 'X49060000' THEN 'L2_016'
        WHEN org_code = 'X49010000' THEN 'L2_017'
        WHEN org_code = 'X49030000' THEN 'L2_018'
        WHEN org_code = 'X49050000' THEN 'L2_019'
        WHEN org_code = 'X49070000' THEN 'L2_020'
        WHEN org_code = 'X49080000' THEN 'L2_021'
        WHEN org_code = 'X31000000' THEN 'L2_023'
        WHEN org_code = 'X62000000' THEN 'L2_024'
        WHEN org_code = 'X43000000' THEN 'L2_025'
        WHEN org_code = 'X44000000' THEN 'L2_026'
        WHEN org_code = 'X34000000' THEN 'L2_031'
        WHEN org_code = 'X74000000' THEN 'L2_032'
        WHEN org_code = 'X76000000' THEN 'L2_033'
        WHEN org_code = 'X85000000' THEN 'L2_034'
        WHEN org_code = 'X48040000' THEN 'L2_035'
        WHEN org_code = 'X48010000' THEN 'L2_036'
        WHEN org_code = 'X48020000' THEN 'L2_037'
        WHEN org_code = 'X48070000' THEN 'L2_038'
        WHEN org_code = 'X48080000' THEN 'L2_039'
        WHEN org_code = 'X35000000' THEN 'L2_040'
        WHEN org_code = 'X52050000' THEN 'L2_041'
        WHEN org_code = 'X52060000' THEN 'L2_042'
        WHEN org_code = 'X52070000' THEN 'L2_043'
        WHEN org_code = 'X78000000' THEN 'L2_044'

        -- Level 3 映射
        WHEN org_code = 'X73020000' THEN 'L3_001'
        WHEN org_code = 'X73030000' THEN 'L3_002'
        WHEN org_code = 'X73040000' THEN 'L3_003'
        WHEN org_code = 'X73070000' THEN 'L3_004'
        WHEN org_code = 'X73140000' THEN 'L3_005'
        WHEN org_code = 'X73010000' THEN 'L3_006'
        WHEN org_code = 'X63000600' THEN 'L3_007'
        WHEN org_code = 'X63000200' THEN 'L3_008'
        WHEN org_code = 'X63000500' THEN 'L3_009'
        WHEN org_code = 'X63000400' THEN 'L3_010'
        WHEN org_code = 'X63000300' THEN 'L3_011'
        WHEN org_code = 'X63000100' THEN 'L3_012'
        WHEN org_code = 'X38060000' THEN 'L3_013'
        WHEN org_code = 'X38050000' THEN 'L3_014'
        WHEN org_code = 'X32110000' THEN 'L3_015'
        WHEN org_code = 'X32010000' THEN 'L3_016'
        WHEN org_code = 'X32160000' THEN 'L3_017'
        WHEN org_code = 'X66010000' THEN 'L3_018'
        WHEN org_code = 'X66120000' THEN 'L3_019'
        WHEN org_code = 'X84020000' THEN 'L3_021'
        WHEN org_code = 'X84030000' THEN 'L3_022'

        -- Level 4 映射
        WHEN org_code = 'X38060001' THEN 'L4_001'
        WHEN org_code = 'X38060002' THEN 'L4_002'
        WHEN org_code = 'X38060003' THEN 'L4_003'
        WHEN org_code = 'X38060004' THEN 'L4_004'
        WHEN org_code = 'X63000601' THEN 'L4_005'
        WHEN org_code = 'X63000602' THEN 'L4_006'
        WHEN org_code = 'X63000603' THEN 'L4_007'
        WHEN org_code = 'X63000604' THEN 'L4_008'
        WHEN org_code = 'X63000605' THEN 'L4_009'
        WHEN org_code = 'X63000201' THEN 'L4_010'
        WHEN org_code = 'X63000205' THEN 'L4_011'
        WHEN org_code = 'X63000204' THEN 'L4_012'
        WHEN org_code = 'X63000203' THEN 'L4_013'
        WHEN org_code = 'X63000202' THEN 'L4_014'
        WHEN org_code = 'X63000402' THEN 'L4_015'
        WHEN org_code = 'X63000408' THEN 'L4_016'
        WHEN org_code = 'X63000406' THEN 'L4_017'
        WHEN org_code = 'X63000301' THEN 'L4_018'
        WHEN org_code = 'X63000302' THEN 'L4_019'

        ELSE NULL
    END as new_dept_code,
    CASE
        WHEN org_code IN ('X50000000','X49000000','X47000000','X48000000','X52000000','X53000000','X57000000','XB1000000','X79000000','X83000000','X36000000','XC5000000','X10000000','X02000000','X06000000','X01000000','X28000000','X51000000','X27000000','XB2000000','X17000000','XC2000000','X15000000','XB3000000','XC1000000','X08000000','XC3000000','XC7000000','X00000000') THEN 1
        WHEN org_code IN ('X32000000','X38000000','X73000000','X66000000','X84000000','X50070000','X50040000','X63000000','X65000000','X41000000','X59000000','X46000000','X60000000','X98000000','X49060000','X49010000','X49030000','X49050000','X49070000','X49080000','X31000000','X62000000','X43000000','X44000000','X34000000','X74000000','X76000000','X85000000','X48040000','X48010000','X48020000','X48070000','X48080000','X35000000','X52050000','X52060000','X52070000','X78000000') THEN 2
        WHEN org_code IN ('X73020000','X73030000','X73040000','X73070000','X73140000','X73010000','X63000600','X63000200','X63000500','X63000400','X63000300','X63000100','X38060000','X38050000','X32110000','X32010000','X32160000','X66010000','X66120000','X84020000','X84030000') THEN 3
        WHEN org_code IN ('X38060001','X38060002','X38060003','X38060004','X63000601','X63000602','X63000603','X63000604','X63000605','X63000201','X63000205','X63000204','X63000203','X63000202','X63000402','X63000408','X63000406','X63000301','X63000302') THEN 4
        ELSE 0
    END as mapping_level,
    CASE
        WHEN org_code IN ('X50000000','X49000000','X47000000','X48000000','X52000000','X53000000','X57000000','XB1000000','X79000000','X83000000','X36000000','XC5000000','X10000000','X02000000','X06000000','X01000000','X28000000','X51000000','X27000000','XB2000000','X17000000','XC2000000','X15000000','XB3000000','XC1000000','X08000000','XC3000000','XC7000000','X00000000') THEN '已映射到标准化一级部门'
        WHEN org_code IN ('X32000000','X38000000','X73000000','X66000000','X84000000','X50070000','X50040000','X63000000','X65000000','X41000000','X59000000','X46000000','X60000000','X98000000','X49060000','X49010000','X49030000','X49050000','X49070000','X49080000','X31000000','X62000000','X43000000','X44000000','X34000000','X74000000','X76000000','X85000000','X48040000','X48010000','X48020000','X48070000','X48080000','X35000000','X52050000','X52060000','X52070000','X78000000') THEN '已映射到标准化二级部门'
        WHEN org_code IN ('X73020000','X73030000','X73040000','X73070000','X73140000','X73010000','X63000600','X63000200','X63000500','X63000400','X63000300','X63000100','X38060000','X38050000','X32110000','X32010000','X32160000','X66010000','X66120000','X84020000','X84030000') THEN '已映射到标准化三级部门'
        WHEN org_code IN ('X38060001','X38060002','X38060003','X38060004','X63000601','X63000602','X63000603','X63000604','X63000605','X63000201','X63000205','X63000204','X63000203','X63000202','X63000402','X63000408','X63000406','X63000301','X63000302') THEN '已映射到标准化四级部门'
        ELSE '需要进一步分析映射'
    END as mapping_notes
FROM (
    SELECT DISTINCT org_code, org_name, parent_code, full_name
    FROM department_sync_test
    WHERE is_history = 0 AND user_predef_14 != 'D'
) t;

-- =====================================================
-- 生成部门名称标准化建议
-- =====================================================

DROP TABLE IF EXISTS dept_name_standardization;
CREATE TABLE dept_name_standardization (
    id INT AUTO_INCREMENT PRIMARY KEY,
    original_org_code VARCHAR(20),
    original_name VARCHAR(200),
    suggested_name VARCHAR(200),
    standardization_type VARCHAR(50),
    reason TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入名称标准化建议
INSERT INTO dept_name_standardization (original_org_code, original_name, suggested_name, standardization_type, reason)
SELECT
    org_code,
    org_name as original_name,
    CASE
        -- 去除事业部前缀
        WHEN org_name LIKE '%事业部%厂%' THEN
            REGEXP_REPLACE(org_name, '^.*事业部', '')
        WHEN org_name LIKE '%事业部%处%' THEN
            REGEXP_REPLACE(org_name, '^.*事业部', '')
        WHEN org_name LIKE '%事业部%车间%' THEN
            REGEXP_REPLACE(org_name, '^.*事业部', '')
        WHEN org_name LIKE '%事业部%科%' THEN
            REGEXP_REPLACE(org_name, '^.*事业部', '')
        WHEN org_name LIKE '%事业部%室%' THEN
            REGEXP_REPLACE(org_name, '^.*事业部', '')
        WHEN org_name LIKE '%事业部%班%' THEN
            REGEXP_REPLACE(org_name, '^.*事业部', '')

        -- 去除厂名前缀
        WHEN org_name LIKE '%厂%车间%' THEN
            REGEXP_REPLACE(org_name, '^.*厂', '')
        WHEN org_name LIKE '%厂%科%' THEN
            REGEXP_REPLACE(org_name, '^.*厂', '')
        WHEN org_name LIKE '%厂%室%' THEN
            REGEXP_REPLACE(org_name, '^.*厂', '')
        WHEN org_name LIKE '%厂%班%' THEN
            REGEXP_REPLACE(org_name, '^.*厂', '')

        -- 去除车间前缀
        WHEN org_name LIKE '%车间%班%' THEN
            REGEXP_REPLACE(org_name, '^.*车间', '')

        -- 去除中心前缀
        WHEN org_name LIKE '%中心%科%' THEN
            REGEXP_REPLACE(org_name, '^.*中心', '')
        WHEN org_name LIKE '%中心%室%' THEN
            REGEXP_REPLACE(org_name, '^.*中心', '')

        ELSE org_name
    END as suggested_name,
    CASE
        WHEN org_name LIKE '%事业部%' THEN '去除事业部前缀'
        WHEN org_name LIKE '%厂%' THEN '去除厂名前缀'
        WHEN org_name LIKE '%车间%' THEN '去除车间前缀'
        WHEN org_name LIKE '%中心%' THEN '去除中心前缀'
        ELSE '无需标准化'
    END as standardization_type,
    CASE
        WHEN org_name LIKE '%事业部%' THEN '部门名称包含上级事业部信息，建议简化为直接的部门名称'
        WHEN org_name LIKE '%厂%' THEN '部门名称包含上级厂名信息，建议简化为直接的部门名称'
        WHEN org_name LIKE '%车间%' THEN '部门名称包含上级车间信息，建议简化为直接的班组名称'
        WHEN org_name LIKE '%中心%' THEN '部门名称包含上级中心信息，建议简化为直接的部门名称'
        ELSE '当前名称已经标准化'
    END as reason
FROM department_sync_test
WHERE is_history = 0 AND user_predef_14 != 'D'
AND (
    org_name LIKE '%事业部%厂%' OR
    org_name LIKE '%事业部%处%' OR
    org_name LIKE '%事业部%车间%' OR
    org_name LIKE '%事业部%科%' OR
    org_name LIKE '%事业部%室%' OR
    org_name LIKE '%事业部%班%' OR
    org_name LIKE '%厂%车间%' OR
    org_name LIKE '%厂%科%' OR
    org_name LIKE '%厂%室%' OR
    org_name LIKE '%厂%班%' OR
    org_name LIKE '%车间%班%' OR
    org_name LIKE '%中心%科%' OR
    org_name LIKE '%中心%室%'
);

-- =====================================================
-- 最终统计报告
-- =====================================================

SELECT '=== 重构完成统计报告 ===' as report_section;

SELECT
    '标准化部门层级统计' as item,
    dept_level as level,
    COUNT(*) as count,
    GROUP_CONCAT(DISTINCT dept_type) as types
FROM department_hierarchy_new
GROUP BY dept_level
ORDER BY dept_level;

SELECT
    '原始数据映射统计' as item,
    mapping_level as level,
    COUNT(*) as mapped_count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM original_dept_mapping), 2) as percentage
FROM original_dept_mapping
WHERE new_dept_code IS NOT NULL
GROUP BY mapping_level
ORDER BY mapping_level;

SELECT
    '名称标准化统计' as item,
    standardization_type,
    COUNT(*) as count
FROM dept_name_standardization
GROUP BY standardization_type
ORDER BY count DESC;

SELECT
    '未映射部门统计' as item,
    COUNT(*) as unmapped_count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM original_dept_mapping), 2) as percentage
FROM original_dept_mapping
WHERE new_dept_code IS NULL;
