package com.nercar.datasynchronization.service;

import java.util.Date;

/**
 * 员工岗位详情服务接口
 */
public interface HrUserDetailsService {

    /**
     * 同步所有员工岗位详情数据
     */
    void updateAllUserDetailFromErp();

    /**
     * 按时间范围同步员工岗位详情数据
     * 注意：第三个接口不支持时间参数，实际调用全量同步
     *
     * @param startDate 开始时间
     * @param endDate 结束时间
     */
    void updateUserDetailFromErp(Date startDate, Date endDate);

    /**
     * 从本地文件同步员工岗位详情数据
     * 用于在无内网环境下，读取之前保存的JSON数据进行同步测试
     *
     * @param filePath 包含JSON数据的txt文件路径
     */
    void updateUserDetailFromFile(String filePath);
}