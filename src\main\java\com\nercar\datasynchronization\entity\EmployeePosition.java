package com.nercar.datasynchronization.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.util.Date;

/**
 * 员工岗位关联实体 - 存储员工的岗位信息
 * 对应文档1.5.3人员信息下发接口中的从表1（人员组织岗位关系）
 */
@Data
@Entity
@Table(name = "employee_position")
public class EmployeePosition {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column
    private String guid;
    
    @Column
    private String employeeMdmId;
    
    @Column(name = "position_code")
    private String positionCode;
    
    @Column
    private String orgCode;
    
    @Column
    private String departmentCode;
    
    @Column
    private String isPrimary;
    
    @Column
    private String status;
    
    @Column(name = "is_active")
    private String isActive;
    
    @Column(name = "position_detail_code")
    private String positionDetailCode;
    
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdTime;
    
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedTime;
}