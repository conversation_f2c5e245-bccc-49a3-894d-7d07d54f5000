//package com.nercar.datasynchronization.controller;
//
//import com.nercar.datasynchronization.client.SoapClient;
//import com.nercar.datasynchronization.service.EmployeeSyncService;
//import com.nercar.datasynchronization.utils.XmlUtils;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.format.annotation.DateTimeFormat;
//import org.springframework.http.ResponseEntity;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.RestController;
//
//import java.util.*;
//import java.util.concurrent.*;
//import java.util.stream.Collectors;
//
//@Slf4j
//@RestController
//@RequestMapping("/api/parallel")
//@Tag(name = "并行数据同步API", description = "提供高性能并行数据同步的接口")
//public class ParallelBatchSyncController {
//
//    @Autowired
//    private SoapClient soapClient;
//
//    @Autowired
//    private EmployeeSyncService employeeSyncService;
//
//    @GetMapping("/employees/fast")
//    @Operation(summary = "快速同步员工数据", description = "使用并行处理和分片优化进行高性能员工数据同步")
//    public ResponseEntity<Map<String, Object>> fastSyncEmployees(
//            @Parameter(description = "开始时间 (格式: yyyy-MM-dd HH:mm:ss)")
//            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startDate,
//            @Parameter(description = "结束时间 (格式: yyyy-MM-dd HH:mm:ss)")
//            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endDate,
//            @Parameter(description = "每个分片的天数")
//            @RequestParam(defaultValue = "1") int chunkDays,
//            @Parameter(description = "并行度")
//            @RequestParam(defaultValue = "3") int parallelism) {
//
//        Map<String, Object> result = new HashMap<>();
//        ExecutorService executor = null;
//
//        try {
//            // 限制并行度不超过3，避免资源竞争和连接泄漏
//            final int actualParallelism = Math.min(parallelism, 3);
//            if (actualParallelism != parallelism) {
//                log.info("将并行度从{}调整为{}，以避免资源竞争", parallelism, actualParallelism);
//            }
//
//            long globalStartTime = System.currentTimeMillis();
//            log.info("开始快速同步员工数据，开始时间: {}, 结束时间: {}, 分片天数: {}, 并行度: {}",
//                    startDate, endDate, chunkDays, actualParallelism);
//
//            // 计算时间分片
//            long totalMillis = endDate.getTime() - startDate.getTime();
//            long chunkMillis = chunkDays * 24 * 60 * 60 * 1000L;
//            int totalChunks = (int) Math.ceil((double) totalMillis / chunkMillis);
//
//            log.info("总分片数: {}", totalChunks);
//
//            // 创建线程池，调整线程池配置以防止资源溢出
//            executor = new ThreadPoolExecutor(
//                Math.min(actualParallelism, totalChunks), // 核心线程数不超过分片数
//                Math.min(actualParallelism, totalChunks), // 最大线程数不超过分片数
//                30L,
//                TimeUnit.SECONDS,
//                new ArrayBlockingQueue<>(totalChunks), // 使用有界队列
//                Executors.defaultThreadFactory(),
//                new ThreadPoolExecutor.CallerRunsPolicy()  // 使用调用者运行策略
//            );
//
//            CompletionService<Map<String, Object>> completionService =
//                    new ExecutorCompletionService<>(executor);
//
//            // 准备任务列表
//            List<Future<Map<String, Object>>> futures = new ArrayList<>();
//
//            Calendar calendar = Calendar.getInstance();
//            calendar.setTime(startDate);
//
//            // 提交所有分片任务
//            for (int i = 0; i < totalChunks; i++) {
//                Date chunkStart = calendar.getTime();
//
//                calendar.add(Calendar.DATE, chunkDays);
//                Date chunkEnd = calendar.getTime();
//
//                if (chunkEnd.after(endDate)) {
//                    chunkEnd = endDate;
//                }
//
//                // 创建并提交任务
//                final int chunkNumber = i + 1;
//                final Date finalChunkStart = chunkStart;
//                final Date finalChunkEnd = chunkEnd;
//
//                Callable<Map<String, Object>> task = () -> {
//                    Map<String, Object> chunkResult = new HashMap<>();
//                    chunkResult.put("chunkNumber", chunkNumber);
//                    chunkResult.put("startDate", finalChunkStart);
//                    chunkResult.put("endDate", finalChunkEnd);
//
//                    long chunkStartTime = System.currentTimeMillis();
//                    try {
//                        log.info("开始处理分片 {}/{} [{}->{}]",
//                                chunkNumber, totalChunks, finalChunkStart, finalChunkEnd);
//
//                        // 添加间隔，避免同时请求SOAP服务和数据库连接
//                        if (chunkNumber > 1) {
//                            Thread.sleep(1000 * (chunkNumber % actualParallelism));  // 更大的间隔，错开执行
//                        }
//
//                        // 调用SOAP接口获取数据
//                        String userXmlResponse = soapClient.getUserInfo(finalChunkStart, finalChunkEnd);
//                        String userXmlData = XmlUtils.extractXmlData(userXmlResponse, "GetUserInfoFromMDMResult");
//
//                        if (userXmlData != null && !userXmlData.isEmpty()) {
//                            // 同步员工数据 - 添加try-finally以确保资源释放
//                            try {
//                                employeeSyncService.syncEmployees(userXmlData);
//
//                                long duration = System.currentTimeMillis() - chunkStartTime;
//                                chunkResult.put("success", true);
//                                chunkResult.put("processingTime", duration);
//                                chunkResult.put("message", "分片同步成功");
//
//                                log.info("分片 {}/{} 同步成功，耗时: {}ms",
//                                        chunkNumber, totalChunks, duration);
//                            } finally {
//                                // 手动触发GC协助释放资源
//                                System.gc();
//                                Thread.sleep(100); // 给GC一些时间
//                            }
//                        } else {
//                            chunkResult.put("success", false);
//                            chunkResult.put("processingTime", System.currentTimeMillis() - chunkStartTime);
//                            chunkResult.put("message", "未获取到数据或数据为空");
//
//                            log.warn("分片 {}/{} 未获取到数据或数据为空", chunkNumber, totalChunks);
//                        }
//                    } catch (Exception e) {
//                        log.error("分片 {}/{} 处理失败: {}", chunkNumber, totalChunks, e.getMessage(), e);
//
//                        chunkResult.put("success", false);
//                        chunkResult.put("processingTime", System.currentTimeMillis() - chunkStartTime);
//                        chunkResult.put("message", "处理失败: " + e.getMessage());
//                    }
//                    return chunkResult;
//                };
//
//                Future<Map<String, Object>> future = completionService.submit(task);
//                futures.add(future);
//            }
//
//            // 收集结果
//            List<Map<String, Object>> chunkResults = new ArrayList<>();
//            for (int i = 0; i < futures.size(); i++) {
//                try {
//                    Map<String, Object> chunkResult = completionService.take().get(60, TimeUnit.SECONDS); // 添加超时
//                    chunkResults.add(chunkResult);
//                } catch (Exception e) {
//                    log.error("获取分片结果失败", e);
//                }
//            }
//
//            // 按分片序号排序
//            chunkResults = chunkResults.stream()
//                    .sorted(Comparator.comparingInt(m -> (Integer) m.get("chunkNumber")))
//                    .collect(Collectors.toList());
//
//            // 汇总结果
//            int successCount = (int) chunkResults.stream()
//                    .filter(r -> (Boolean) r.get("success"))
//                    .count();
//
//            long totalDuration = System.currentTimeMillis() - globalStartTime;
//
//            result.put("success", true);
//            result.put("totalChunks", totalChunks);
//            result.put("successfulChunks", successCount);
//            result.put("failedChunks", totalChunks - successCount);
//            result.put("totalProcessingTime", totalDuration);
//            result.put("details", chunkResults);
//            result.put("message", String.format(
//                    "快速同步完成，共%d个分片，成功%d个，失败%d个，总耗时%dms",
//                    totalChunks, successCount, totalChunks - successCount, totalDuration));
//
//            log.info("快速同步完成，共{}个分片，成功{}个，失败{}个，总耗时{}ms",
//                    totalChunks, successCount, totalChunks - successCount, totalDuration);
//
//            return ResponseEntity.ok(result);
//        } catch (Exception e) {
//            log.error("快速同步员工数据失败", e);
//            result.put("success", false);
//            result.put("message", "快速同步员工数据失败: " + e.getMessage());
//            return ResponseEntity.internalServerError().body(result);
//        } finally {
//            if (executor != null) {
//                executor.shutdown();
//                try {
//                    // 等待所有任务完成，确保资源释放
//                    if (!executor.awaitTermination(30, TimeUnit.SECONDS)) {
//                        executor.shutdownNow();
//                    }
//                } catch (InterruptedException e) {
//                    executor.shutdownNow();
//                    Thread.currentThread().interrupt();
//                }
//            }
//        }
//    }
//}