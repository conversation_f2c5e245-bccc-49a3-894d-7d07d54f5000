package com.nercar.datasynchronization.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 离线数据迁移服务
 * 直接读取department_sync_test.sql文件，转换为t_org_structure格式
 */
@Slf4j
@Service
public class OfflineMigrationService {

    /**
     * 从SQL文件读取数据并转换为t_org_structure格式
     */
    public String migrateFromSqlFile(String inputFilePath, String outputFilePath) {
        try {
            log.info("开始离线数据迁移，输入文件: {}, 输出文件: {}", inputFilePath, outputFilePath);

            // 读取并解析SQL文件
            List<DepartmentRecord> departments = parseSqlFile(inputFilePath);
            log.info("解析到 {} 条部门记录", departments.size());

            // 生成迁移SQL文件
            generateMigrationSql(departments, outputFilePath);

            log.info("离线数据迁移完成，生成文件: {}", outputFilePath);
            return "迁移成功，共处理 " + departments.size() + " 条记录";

        } catch (Exception e) {
            log.error("离线数据迁移失败", e);
            throw new RuntimeException("离线数据迁移失败: " + e.getMessage(), e);
        }
    }

    /**
     * 专门处理department_sync_test2.sql文件的迁移
     */
    public String migrateFromTest2SqlFile(String outputFilePath) {
        String inputFilePath = "department_sync_test2.sql";
        return migrateFromSqlFile(inputFilePath, outputFilePath);
    }

    /**
     * 解析SQL文件，提取部门数据
     */
    private List<DepartmentRecord> parseSqlFile(String filePath) throws IOException {
        List<DepartmentRecord> departments = new ArrayList<>();

        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            String line;

            // 正则表达式匹配实际的INSERT VALUES格式
            // INSERT INTO `department_sync_test` VALUES (id, 'orgCode', 'orgName', 'parentCode', 'uuid', 'fullName', isHistory, 'userPredef14', 'createTime', 'syncDate', 'updateTime');
            Pattern insertPattern = Pattern.compile(
                "INSERT INTO `department_sync_test` VALUES \\((\\d+),\\s*'([^']+)',\\s*'([^']*)',\\s*'([^']*)',\\s*'([^']*)',\\s*'([^']*)',\\s*(\\d+),\\s*'([^']*)',\\s*'[^']*',\\s*'[^']*',\\s*'[^']*'\\);"
            );

            while ((line = reader.readLine()) != null) {
                line = line.trim();

                // 直接匹配INSERT语句
                if (line.startsWith("INSERT INTO `department_sync_test`")) {
                    Matcher matcher = insertPattern.matcher(line);
                    if (matcher.find()) {
                        try {
                            DepartmentRecord dept = new DepartmentRecord();
                            dept.id = Long.parseLong(matcher.group(1));
                            dept.orgCode = matcher.group(2);
                            dept.orgName = matcher.group(3);
                            dept.parentCode = matcher.group(4);
                            dept.deptUuid = matcher.group(5);
                            dept.fullName = matcher.group(6);
                            dept.isHistory = Integer.parseInt(matcher.group(7));
                            dept.userPredef14 = matcher.group(8);

                            // 只处理有效数据
                            if (dept.isHistory == 0 && !"D".equals(dept.userPredef14)) {
                                departments.add(dept);
                                log.debug("解析到有效部门: {} ({})", dept.orgName, dept.orgCode);
                            } else {
                                log.debug("跳过无效部门: {} ({}), isHistory={}, userPredef14={}",
                                         dept.orgName, dept.orgCode, dept.isHistory, dept.userPredef14);
                            }
                        } catch (Exception e) {
                            log.warn("解析记录失败: {}", line, e);
                        }
                    } else {
                        log.warn("INSERT语句格式不匹配: {}", line.length() > 100 ? line.substring(0, 100) + "..." : line);
                    }
                }
            }
        }

        return departments;
    }

    /**
     * 生成PostgreSQL格式的迁移SQL文件
     */
    private void generateMigrationSql(List<DepartmentRecord> departments, String outputFilePath) throws IOException {
        try (FileWriter writer = new FileWriter(outputFilePath)) {
            // 写入文件头
            writeFileHeader(writer, departments.size());
            
            // 写入清理语句
            writer.write("-- 清理现有同步数据\n");
            writer.write("DELETE FROM t_org_structure WHERE data_source = 2;\n\n");
            
            // 统计信息
            long rootCount = departments.stream()
                    .filter(d -> "1".equals(d.parentCode) || 
                               d.parentCode == null || 
                               d.parentCode.isEmpty())
                    .count();
            
            writer.write("-- 数据统计信息\n");
            writer.write("-- 总记录数: " + departments.size() + "\n");
            writer.write("-- 根节点数: " + rootCount + "\n");
            writer.write("-- 子节点数: " + (departments.size() - rootCount) + "\n\n");
            
            // 生成INSERT语句
            writer.write("-- 插入部门数据\n");
            writer.write("INSERT INTO t_org_structure (id, organ_name, pre_id, order_info, is_del, data_source) VALUES\n");
            
            for (int i = 0; i < departments.size(); i++) {
                DepartmentRecord dept = departments.get(i);
                String insertLine = generateInsertLine(dept);
                
                if (i == departments.size() - 1) {
                    writer.write(insertLine + ";\n");
                } else {
                    writer.write(insertLine + ",\n");
                }
            }
            
            // 写入验证查询
            writeValidationQueries(writer);
        }
    }

    /**
     * 写入文件头注释
     */
    private void writeFileHeader(FileWriter writer, int recordCount) throws IOException {
        writer.write("-- =====================================================\n");
        writer.write("-- 部门数据迁移SQL - 从department_sync_test到t_org_structure\n");
        writer.write("-- 生成时间: " + java.time.LocalDateTime.now() + "\n");
        writer.write("-- 数据来源: ERP系统25年完整同步数据（离线处理）\n");
        writer.write("-- 记录数量: " + recordCount + "\n");
        writer.write("-- =====================================================\n\n");
        
        writer.write("-- 目标表结构: t_org_structure\n");
        writer.write("-- id: numeric (组织编码，来源于org_code)\n");
        writer.write("-- organ_name: varchar (组织名称，来源于org_name)\n");
        writer.write("-- pre_id: numeric (父级ID，来源于parent_code)\n");
        writer.write("-- order_info: numeric (排序信息，默认值)\n");
        writer.write("-- is_del: numeric (删除标识，0=正常)\n");
        writer.write("-- data_source: numeric (数据来源，2=同步数据)\n\n");
    }

    /**
     * 生成单条记录的INSERT语句
     */
    private String generateInsertLine(DepartmentRecord dept) {
        // 处理组织编码 - 移除X前缀，转换为数字
        Long id = convertOrgCodeToId(dept.orgCode);

        // 处理组织名称 - 转义单引号
        String organName = dept.orgName.replace("'", "''");

        // 处理父级ID
        String preIdStr = convertParentCodeToPreId(dept.parentCode);

        // 默认值
        int orderInfo = 0;        // 默认排序
        boolean isDel = false;    // 正常状态（boolean类型）
        int dataSource = 2;       // 同步数据

        return String.format("    (%d, '%s', %s, %d, %s, %d)",
                id, organName, preIdStr, orderInfo, isDel, dataSource);
    }

    /**
     * 将org_code转换为数字ID
     */
    private Long convertOrgCodeToId(String orgCode) {
        if (orgCode == null || orgCode.isEmpty()) {
            throw new IllegalArgumentException("组织编码不能为空");
        }
        
        try {
            // 如果以X开头，移除X前缀
            if (orgCode.startsWith("X")) {
                String numericPart = orgCode.substring(1);
                return Long.parseLong(numericPart);
            } else {
                return Long.parseLong(orgCode);
            }
        } catch (NumberFormatException e) {
            log.warn("无法转换组织编码为数字: {}", orgCode);
            // 使用hashCode作为备用方案
            return Math.abs((long) orgCode.hashCode());
        }
    }

    /**
     * 将parent_code转换为pre_id
     * 使用与convertOrgCodeToId相同的逻辑确保ID映射一致
     */
    private String convertParentCodeToPreId(String parentCode) {
        if (parentCode == null || parentCode.isEmpty() || "1".equals(parentCode)) {
            // 根节点，pre_id为NULL
            return "NULL";
        }

        try {
            // 使用与convertOrgCodeToId相同的转换逻辑
            Long parentId = convertOrgCodeToId(parentCode);
            return parentId.toString();
        } catch (Exception e) {
            log.warn("无法转换父级编码: {}", parentCode);
            return "NULL";
        }
    }

    /**
     * 写入验证查询
     */
    private void writeValidationQueries(FileWriter writer) throws IOException {
        writer.write("\n-- 验证查询\n");
        writer.write("SELECT \n");
        writer.write("    '数据验证' as 检查项,\n");
        writer.write("    COUNT(*) as 记录数\n");
        writer.write("FROM t_org_structure \n");
        writer.write("WHERE data_source = 2;\n\n");
        
        writer.write("-- 根节点查询\n");
        writer.write("SELECT \n");
        writer.write("    id,\n");
        writer.write("    organ_name,\n");
        writer.write("    pre_id\n");
        writer.write("FROM t_org_structure \n");
        writer.write("WHERE data_source = 2 AND pre_id IS NULL\n");
        writer.write("ORDER BY id\n");
        writer.write("LIMIT 20;\n\n");
        
        writer.write("-- 孤儿节点检查\n");
        writer.write("SELECT \n");
        writer.write("    COUNT(*) as 孤儿节点数\n");
        writer.write("FROM t_org_structure t1\n");
        writer.write("WHERE t1.data_source = 2\n");
        writer.write("AND t1.pre_id IS NOT NULL\n");
        writer.write("AND t1.pre_id NOT IN (\n");
        writer.write("    SELECT t2.id FROM t_org_structure t2 WHERE t2.data_source = 2\n");
        writer.write(");\n");
    }

    /**
     * 部门记录数据类
     */
    private static class DepartmentRecord {
        Long id;
        String orgCode;
        String orgName;
        String parentCode;
        String deptUuid;
        String fullName;
        Integer isHistory;
        String userPredef14;
    }
}
