2025-06-09 11:48:34.825 [http-nio-8080-exec-1] INFO  c.n.d.c.DataRetrievalController - 🔍 [API请求] 获取部门数据 - 原始参数: startDate=2024-01-01%2000:00:00, endDate=2024-01-10%2023:59:59
2025-06-09 11:48:34.830 [http-nio-8080-exec-1] DEBUG c.n.d.utils.DateUtils - 尝试解析日期字符串: 原始=2024-01-01%2000:00:00, 解码后=2024-01-01 00:00:00
2025-06-09 11:48:34.831 [http-nio-8080-exec-1] DEBUG c.n.d.utils.DateUtils - 成功使用格式 yyyy-MM-dd HH:mm:ss 解析日期: Mon Jan 01 00:00:00 CST 2024
2025-06-09 11:48:34.832 [http-nio-8080-exec-1] DEBUG c.n.d.utils.DateUtils - 尝试解析日期字符串: 原始=2024-01-10%2023:59:59, 解码后=2024-01-10 23:59:59
2025-06-09 11:48:34.832 [http-nio-8080-exec-1] DEBUG c.n.d.utils.DateUtils - 成功使用格式 yyyy-MM-dd HH:mm:ss 解析日期: Wed Jan 10 23:59:59 CST 2024
2025-06-09 11:48:34.833 [http-nio-8080-exec-1] INFO  c.n.d.c.DataRetrievalController - 📅 [日期解析] 成功解析日期参数 - 开始时间: Mon Jan 01 00:00:00 CST 2024, 结束时间: Wed Jan 10 23:59:59 CST 2024
2025-06-09 11:48:34.833 [http-nio-8080-exec-1] INFO  c.n.d.c.DataRetrievalController - 🚀 [服务调用] 开始调用数据获取服务...
2025-06-09 11:48:34.834 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl - === 开始获取部门数据 ===
2025-06-09 11:48:34.835 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 查询时间范围: Mon Jan 01 00:00:00 CST 2024 至 Wed Jan 10 23:59:59 CST 2024
2025-06-09 11:48:34.835 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在调用远程SOAP接口获取部门数据...
2025-06-09 11:48:35.935 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 远程SOAP接口调用成功，响应数据长度: 60059 字符
2025-06-09 11:48:35.935 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在解析SOAP响应，提取XML数据...
2025-06-09 11:48:35.951 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl - XML数据提取成功，数据长度: 46053 字符
2025-06-09 11:48:35.951 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在解析XML数据为部门DTO对象...
2025-06-09 11:48:35.960 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl - ✅ 部门数据解析完成:
2025-06-09 11:48:35.961 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 主部门记录: 21 条 (对应 department 表)
2025-06-09 11:48:35.961 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 子部门记录: 0 条 (对应 department_child 表)
2025-06-09 11:48:35.961 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 总计: 21 条记录
2025-06-09 11:48:35.961 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 部门数据示例:
2025-06-09 11:48:35.964 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 部门: 原料二车间 | 代码: X86100000 | UUID: null | 层级: null
2025-06-09 11:48:35.964 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 部门: 除尘班 | 代码: X86100001 | UUID: null | 层级: null
2025-06-09 11:48:35.964 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 部门: 破碎班 | 代码: X86100002 | UUID: null | 层级: null
2025-06-09 11:48:35.964 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - ... 还有 18 个部门
2025-06-09 11:48:35.964 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl - === 部门数据获取完成，总计: 21条记录 ===
2025-06-09 11:48:35.966 [http-nio-8080-exec-1] INFO  c.n.d.c.DataRetrievalController - ✅ [API响应] 部门数据获取成功:
2025-06-09 11:48:35.966 [http-nio-8080-exec-1] INFO  c.n.d.c.DataRetrievalController -    - 主部门记录: 21 条
2025-06-09 11:48:35.966 [http-nio-8080-exec-1] INFO  c.n.d.c.DataRetrievalController -    - 子部门记录: 0 条
2025-06-09 11:48:35.966 [http-nio-8080-exec-1] INFO  c.n.d.c.DataRetrievalController -    - 响应总记录数: 21 条
2025-06-09 11:56:47.119 [http-nio-8080-exec-5] INFO  c.n.d.c.DataRetrievalController - 🔍 [API请求] 获取部门数据 - 原始参数: startDate=2024-01-01%2000:00:00, endDate=2024-01-10%2023:59:59
2025-06-09 11:56:47.120 [http-nio-8080-exec-5] DEBUG c.n.d.utils.DateUtils - 尝试解析日期字符串: 原始=2024-01-01%2000:00:00, 解码后=2024-01-01 00:00:00
2025-06-09 11:56:47.120 [http-nio-8080-exec-5] DEBUG c.n.d.utils.DateUtils - 成功使用格式 yyyy-MM-dd HH:mm:ss 解析日期: Mon Jan 01 00:00:00 CST 2024
2025-06-09 11:56:47.120 [http-nio-8080-exec-5] DEBUG c.n.d.utils.DateUtils - 尝试解析日期字符串: 原始=2024-01-10%2023:59:59, 解码后=2024-01-10 23:59:59
2025-06-09 11:56:47.121 [http-nio-8080-exec-5] DEBUG c.n.d.utils.DateUtils - 成功使用格式 yyyy-MM-dd HH:mm:ss 解析日期: Wed Jan 10 23:59:59 CST 2024
2025-06-09 11:56:47.121 [http-nio-8080-exec-5] INFO  c.n.d.c.DataRetrievalController - 📅 [日期解析] 成功解析日期参数 - 开始时间: Mon Jan 01 00:00:00 CST 2024, 结束时间: Wed Jan 10 23:59:59 CST 2024
2025-06-09 11:56:47.121 [http-nio-8080-exec-5] INFO  c.n.d.c.DataRetrievalController - 🚀 [服务调用] 开始调用数据获取服务...
2025-06-09 11:56:47.121 [http-nio-8080-exec-5] INFO  c.n.d.s.i.DataRetrievalServiceImpl - === 开始获取部门数据 ===
2025-06-09 11:56:47.121 [http-nio-8080-exec-5] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 查询时间范围: Mon Jan 01 00:00:00 CST 2024 至 Wed Jan 10 23:59:59 CST 2024
2025-06-09 11:56:47.121 [http-nio-8080-exec-5] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在调用远程SOAP接口获取部门数据...
2025-06-09 11:56:48.223 [http-nio-8080-exec-5] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 远程SOAP接口调用成功，响应数据长度: 60059 字符
2025-06-09 11:56:48.223 [http-nio-8080-exec-5] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在解析SOAP响应，提取XML数据...
2025-06-09 11:56:48.232 [http-nio-8080-exec-5] INFO  c.n.d.s.i.DataRetrievalServiceImpl - XML数据提取成功，数据长度: 46053 字符
2025-06-09 11:56:48.232 [http-nio-8080-exec-5] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在解析XML数据为部门DTO对象...
2025-06-09 11:56:48.236 [http-nio-8080-exec-5] INFO  c.n.d.s.i.DataRetrievalServiceImpl - ✅ 部门数据解析完成:
2025-06-09 11:56:48.236 [http-nio-8080-exec-5] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 主部门记录: 21 条 (对应 department 表)
2025-06-09 11:56:48.236 [http-nio-8080-exec-5] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 子部门记录: 0 条 (对应 department_child 表)
2025-06-09 11:56:48.236 [http-nio-8080-exec-5] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 总计: 21 条记录
2025-06-09 11:56:48.236 [http-nio-8080-exec-5] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 部门数据示例:
2025-06-09 11:56:48.236 [http-nio-8080-exec-5] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 部门: 原料二车间 | 代码: X86100000 | UUID: null | 层级: null
2025-06-09 11:56:48.236 [http-nio-8080-exec-5] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 部门: 除尘班 | 代码: X86100001 | UUID: null | 层级: null
2025-06-09 11:56:48.236 [http-nio-8080-exec-5] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 部门: 破碎班 | 代码: X86100002 | UUID: null | 层级: null
2025-06-09 11:56:48.237 [http-nio-8080-exec-5] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - ... 还有 18 个部门
2025-06-09 11:56:48.237 [http-nio-8080-exec-5] INFO  c.n.d.s.i.DataRetrievalServiceImpl - === 部门数据获取完成，总计: 21条记录 ===
2025-06-09 11:56:48.237 [http-nio-8080-exec-5] INFO  c.n.d.c.DataRetrievalController - ✅ [API响应] 部门数据获取成功:
2025-06-09 11:56:48.237 [http-nio-8080-exec-5] INFO  c.n.d.c.DataRetrievalController -    - 主部门记录: 21 条
2025-06-09 11:56:48.237 [http-nio-8080-exec-5] INFO  c.n.d.c.DataRetrievalController -    - 子部门记录: 0 条
2025-06-09 11:56:48.237 [http-nio-8080-exec-5] INFO  c.n.d.c.DataRetrievalController -    - 响应总记录数: 21 条
2025-06-09 11:58:20.748 [http-nio-8080-exec-9] INFO  c.n.d.c.DataRetrievalController - 🔍 [API请求] 获取部门数据 - 原始参数: startDate=2024-01-01%2000:00:00, endDate=2024-01-10%2023:59:59
2025-06-09 11:58:20.749 [http-nio-8080-exec-9] DEBUG c.n.d.utils.DateUtils - 尝试解析日期字符串: 原始=2024-01-01%2000:00:00, 解码后=2024-01-01 00:00:00
2025-06-09 11:58:20.750 [http-nio-8080-exec-9] DEBUG c.n.d.utils.DateUtils - 成功使用格式 yyyy-MM-dd HH:mm:ss 解析日期: Mon Jan 01 00:00:00 CST 2024
2025-06-09 11:58:20.750 [http-nio-8080-exec-9] DEBUG c.n.d.utils.DateUtils - 尝试解析日期字符串: 原始=2024-01-10%2023:59:59, 解码后=2024-01-10 23:59:59
2025-06-09 11:58:20.751 [http-nio-8080-exec-9] DEBUG c.n.d.utils.DateUtils - 成功使用格式 yyyy-MM-dd HH:mm:ss 解析日期: Wed Jan 10 23:59:59 CST 2024
2025-06-09 11:58:20.751 [http-nio-8080-exec-9] INFO  c.n.d.c.DataRetrievalController - 📅 [日期解析] 成功解析日期参数 - 开始时间: Mon Jan 01 00:00:00 CST 2024, 结束时间: Wed Jan 10 23:59:59 CST 2024
2025-06-09 11:58:20.751 [http-nio-8080-exec-9] INFO  c.n.d.c.DataRetrievalController - 🚀 [服务调用] 开始调用数据获取服务...
2025-06-09 11:58:20.752 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl - === 开始获取部门数据 ===
2025-06-09 11:58:20.752 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 查询时间范围: Mon Jan 01 00:00:00 CST 2024 至 Wed Jan 10 23:59:59 CST 2024
2025-06-09 11:58:20.753 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在调用远程SOAP接口获取部门数据...
2025-06-09 11:58:24.698 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 远程SOAP接口调用成功，响应数据长度: 60059 字符
2025-06-09 11:58:24.698 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在解析SOAP响应，提取XML数据...
2025-06-09 11:58:24.703 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl - XML数据提取成功，数据长度: 46053 字符
2025-06-09 11:58:24.703 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在解析XML数据为部门DTO对象...
2025-06-09 11:58:24.705 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl - ✅ 部门数据解析完成:
2025-06-09 11:58:24.705 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 主部门记录: 21 条 (对应 department 表)
2025-06-09 11:58:24.705 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 子部门记录: 0 条 (对应 department_child 表)
2025-06-09 11:58:24.705 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 总计: 21 条记录
2025-06-09 11:58:24.705 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 部门数据示例:
2025-06-09 11:58:24.705 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 部门: 原料二车间 | 代码: X86100000 | UUID: null | 层级: null
2025-06-09 11:58:24.705 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 部门: 除尘班 | 代码: X86100001 | UUID: null | 层级: null
2025-06-09 11:58:24.705 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 部门: 破碎班 | 代码: X86100002 | UUID: null | 层级: null
2025-06-09 11:58:24.705 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - ... 还有 18 个部门
2025-06-09 11:58:24.705 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl - === 部门数据获取完成，总计: 21条记录 ===
2025-06-09 11:58:24.705 [http-nio-8080-exec-9] INFO  c.n.d.c.DataRetrievalController - ✅ [API响应] 部门数据获取成功:
2025-06-09 11:58:24.705 [http-nio-8080-exec-9] INFO  c.n.d.c.DataRetrievalController -    - 主部门记录: 21 条
2025-06-09 11:58:24.705 [http-nio-8080-exec-9] INFO  c.n.d.c.DataRetrievalController -    - 子部门记录: 0 条
2025-06-09 11:58:24.705 [http-nio-8080-exec-9] INFO  c.n.d.c.DataRetrievalController -    - 响应总记录数: 21 条
2025-06-09 12:02:04.128 [http-nio-8080-exec-3] INFO  c.n.d.c.DataRetrievalController - 🔍 [API请求] 获取部门数据 - 原始参数: startDate=2024-01-01%2000:00:00, endDate=2024-01-10%2023:59:59
2025-06-09 12:02:04.129 [http-nio-8080-exec-3] DEBUG c.n.d.utils.DateUtils - 尝试解析日期字符串: 原始=2024-01-01%2000:00:00, 解码后=2024-01-01 00:00:00
2025-06-09 12:02:04.129 [http-nio-8080-exec-3] DEBUG c.n.d.utils.DateUtils - 成功使用格式 yyyy-MM-dd HH:mm:ss 解析日期: Mon Jan 01 00:00:00 CST 2024
2025-06-09 12:02:04.129 [http-nio-8080-exec-3] DEBUG c.n.d.utils.DateUtils - 尝试解析日期字符串: 原始=2024-01-10%2023:59:59, 解码后=2024-01-10 23:59:59
2025-06-09 12:02:04.129 [http-nio-8080-exec-3] DEBUG c.n.d.utils.DateUtils - 成功使用格式 yyyy-MM-dd HH:mm:ss 解析日期: Wed Jan 10 23:59:59 CST 2024
2025-06-09 12:02:04.129 [http-nio-8080-exec-3] INFO  c.n.d.c.DataRetrievalController - 📅 [日期解析] 成功解析日期参数 - 开始时间: Mon Jan 01 00:00:00 CST 2024, 结束时间: Wed Jan 10 23:59:59 CST 2024
2025-06-09 12:02:04.129 [http-nio-8080-exec-3] INFO  c.n.d.c.DataRetrievalController - 🚀 [服务调用] 开始调用数据获取服务...
2025-06-09 12:02:04.130 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl - === 开始获取部门数据 ===
2025-06-09 12:02:04.130 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 查询时间范围: Mon Jan 01 00:00:00 CST 2024 至 Wed Jan 10 23:59:59 CST 2024
2025-06-09 12:02:04.130 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在调用远程SOAP接口获取部门数据...
2025-06-09 12:02:04.759 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 远程SOAP接口调用成功，响应数据长度: 60059 字符
2025-06-09 12:02:04.759 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在解析SOAP响应，提取XML数据...
2025-06-09 12:02:04.770 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl - XML数据提取成功，数据长度: 46053 字符
2025-06-09 12:02:04.770 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在解析XML数据为部门DTO对象...
2025-06-09 12:02:04.778 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl - ✅ 部门数据解析完成:
2025-06-09 12:02:04.778 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 主部门记录: 21 条 (对应 department 表)
2025-06-09 12:02:04.779 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 子部门记录: 0 条 (对应 department_child 表)
2025-06-09 12:02:04.779 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 总计: 21 条记录
2025-06-09 12:02:04.779 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 部门数据示例:
2025-06-09 12:02:04.779 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 部门: 原料二车间 | 代码: X86100000 | UUID: null | 层级: null
2025-06-09 12:02:04.779 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 部门: 除尘班 | 代码: X86100001 | UUID: null | 层级: null
2025-06-09 12:02:04.779 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 部门: 破碎班 | 代码: X86100002 | UUID: null | 层级: null
2025-06-09 12:02:04.779 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - ... 还有 18 个部门
2025-06-09 12:02:04.779 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl - === 部门数据获取完成，总计: 21条记录 ===
2025-06-09 12:02:04.779 [http-nio-8080-exec-3] INFO  c.n.d.c.DataRetrievalController - ✅ [API响应] 部门数据获取成功:
2025-06-09 12:02:04.779 [http-nio-8080-exec-3] INFO  c.n.d.c.DataRetrievalController -    - 主部门记录: 21 条
2025-06-09 12:02:04.779 [http-nio-8080-exec-3] INFO  c.n.d.c.DataRetrievalController -    - 子部门记录: 0 条
2025-06-09 12:02:04.779 [http-nio-8080-exec-3] INFO  c.n.d.c.DataRetrievalController -    - 响应总记录数: 21 条
2025-06-09 12:02:35.273 [http-nio-8080-exec-8] INFO  c.n.d.c.DataRetrievalController - 🔍 [API请求] 获取部门数据 - 原始参数: startDate=2024-01-11%2000:00:00, endDate=2024-01-20%2023:59:59
2025-06-09 12:02:35.274 [http-nio-8080-exec-8] DEBUG c.n.d.utils.DateUtils - 尝试解析日期字符串: 原始=2024-01-11%2000:00:00, 解码后=2024-01-11 00:00:00
2025-06-09 12:02:35.274 [http-nio-8080-exec-8] DEBUG c.n.d.utils.DateUtils - 成功使用格式 yyyy-MM-dd HH:mm:ss 解析日期: Thu Jan 11 00:00:00 CST 2024
2025-06-09 12:02:35.274 [http-nio-8080-exec-8] DEBUG c.n.d.utils.DateUtils - 尝试解析日期字符串: 原始=2024-01-20%2023:59:59, 解码后=2024-01-20 23:59:59
2025-06-09 12:02:35.274 [http-nio-8080-exec-8] DEBUG c.n.d.utils.DateUtils - 成功使用格式 yyyy-MM-dd HH:mm:ss 解析日期: Sat Jan 20 23:59:59 CST 2024
2025-06-09 12:02:35.274 [http-nio-8080-exec-8] INFO  c.n.d.c.DataRetrievalController - 📅 [日期解析] 成功解析日期参数 - 开始时间: Thu Jan 11 00:00:00 CST 2024, 结束时间: Sat Jan 20 23:59:59 CST 2024
2025-06-09 12:02:35.274 [http-nio-8080-exec-8] INFO  c.n.d.c.DataRetrievalController - 🚀 [服务调用] 开始调用数据获取服务...
2025-06-09 12:02:35.274 [http-nio-8080-exec-8] INFO  c.n.d.s.i.DataRetrievalServiceImpl - === 开始获取部门数据 ===
2025-06-09 12:02:35.274 [http-nio-8080-exec-8] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 查询时间范围: Thu Jan 11 00:00:00 CST 2024 至 Sat Jan 20 23:59:59 CST 2024
2025-06-09 12:02:35.274 [http-nio-8080-exec-8] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在调用远程SOAP接口获取部门数据...
2025-06-09 12:02:35.561 [http-nio-8080-exec-8] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 远程SOAP接口调用成功，响应数据长度: 442 字符
2025-06-09 12:02:35.561 [http-nio-8080-exec-8] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在解析SOAP响应，提取XML数据...
2025-06-09 12:02:35.564 [http-nio-8080-exec-8] INFO  c.n.d.s.i.DataRetrievalServiceImpl - XML数据提取成功，数据长度: 50 字符
2025-06-09 12:02:35.564 [http-nio-8080-exec-8] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在解析XML数据为部门DTO对象...
2025-06-09 12:02:35.566 [http-nio-8080-exec-8] INFO  c.n.d.s.i.DataRetrievalServiceImpl - ✅ 部门数据解析完成:
2025-06-09 12:02:35.566 [http-nio-8080-exec-8] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 主部门记录: 0 条 (对应 department 表)
2025-06-09 12:02:35.566 [http-nio-8080-exec-8] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 子部门记录: 0 条 (对应 department_child 表)
2025-06-09 12:02:35.566 [http-nio-8080-exec-8] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 总计: 0 条记录
2025-06-09 12:02:35.566 [http-nio-8080-exec-8] INFO  c.n.d.s.i.DataRetrievalServiceImpl - === 部门数据获取完成，总计: 0条记录 ===
2025-06-09 12:02:35.566 [http-nio-8080-exec-8] INFO  c.n.d.c.DataRetrievalController - ✅ [API响应] 部门数据获取成功:
2025-06-09 12:02:35.566 [http-nio-8080-exec-8] INFO  c.n.d.c.DataRetrievalController -    - 主部门记录: 0 条
2025-06-09 12:02:35.566 [http-nio-8080-exec-8] INFO  c.n.d.c.DataRetrievalController -    - 子部门记录: 0 条
2025-06-09 12:02:35.566 [http-nio-8080-exec-8] INFO  c.n.d.c.DataRetrievalController -    - 响应总记录数: 0 条
2025-06-09 12:03:00.037 [http-nio-8080-exec-7] INFO  c.n.d.c.DataRetrievalController - 🔍 [API请求] 获取部门数据 - 原始参数: startDate=2024-01-21%2000:00:00, endDate=2024-01-31%2023:59:59
2025-06-09 12:03:00.037 [http-nio-8080-exec-7] DEBUG c.n.d.utils.DateUtils - 尝试解析日期字符串: 原始=2024-01-21%2000:00:00, 解码后=2024-01-21 00:00:00
2025-06-09 12:03:00.037 [http-nio-8080-exec-7] DEBUG c.n.d.utils.DateUtils - 成功使用格式 yyyy-MM-dd HH:mm:ss 解析日期: Sun Jan 21 00:00:00 CST 2024
2025-06-09 12:03:00.037 [http-nio-8080-exec-7] DEBUG c.n.d.utils.DateUtils - 尝试解析日期字符串: 原始=2024-01-31%2023:59:59, 解码后=2024-01-31 23:59:59
2025-06-09 12:03:00.037 [http-nio-8080-exec-7] DEBUG c.n.d.utils.DateUtils - 成功使用格式 yyyy-MM-dd HH:mm:ss 解析日期: Wed Jan 31 23:59:59 CST 2024
2025-06-09 12:03:00.037 [http-nio-8080-exec-7] INFO  c.n.d.c.DataRetrievalController - 📅 [日期解析] 成功解析日期参数 - 开始时间: Sun Jan 21 00:00:00 CST 2024, 结束时间: Wed Jan 31 23:59:59 CST 2024
2025-06-09 12:03:00.037 [http-nio-8080-exec-7] INFO  c.n.d.c.DataRetrievalController - 🚀 [服务调用] 开始调用数据获取服务...
2025-06-09 12:03:00.037 [http-nio-8080-exec-7] INFO  c.n.d.s.i.DataRetrievalServiceImpl - === 开始获取部门数据 ===
2025-06-09 12:03:00.037 [http-nio-8080-exec-7] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 查询时间范围: Sun Jan 21 00:00:00 CST 2024 至 Wed Jan 31 23:59:59 CST 2024
2025-06-09 12:03:00.037 [http-nio-8080-exec-7] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在调用远程SOAP接口获取部门数据...
2025-06-09 12:03:00.317 [http-nio-8080-exec-7] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 远程SOAP接口调用成功，响应数据长度: 442 字符
2025-06-09 12:03:00.317 [http-nio-8080-exec-7] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在解析SOAP响应，提取XML数据...
2025-06-09 12:03:00.319 [http-nio-8080-exec-7] INFO  c.n.d.s.i.DataRetrievalServiceImpl - XML数据提取成功，数据长度: 50 字符
2025-06-09 12:03:00.319 [http-nio-8080-exec-7] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在解析XML数据为部门DTO对象...
2025-06-09 12:03:00.321 [http-nio-8080-exec-7] INFO  c.n.d.s.i.DataRetrievalServiceImpl - ✅ 部门数据解析完成:
2025-06-09 12:03:00.321 [http-nio-8080-exec-7] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 主部门记录: 0 条 (对应 department 表)
2025-06-09 12:03:00.321 [http-nio-8080-exec-7] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 子部门记录: 0 条 (对应 department_child 表)
2025-06-09 12:03:00.321 [http-nio-8080-exec-7] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 总计: 0 条记录
2025-06-09 12:03:00.321 [http-nio-8080-exec-7] INFO  c.n.d.s.i.DataRetrievalServiceImpl - === 部门数据获取完成，总计: 0条记录 ===
2025-06-09 12:03:00.321 [http-nio-8080-exec-7] INFO  c.n.d.c.DataRetrievalController - ✅ [API响应] 部门数据获取成功:
2025-06-09 12:03:00.321 [http-nio-8080-exec-7] INFO  c.n.d.c.DataRetrievalController -    - 主部门记录: 0 条
2025-06-09 12:03:00.321 [http-nio-8080-exec-7] INFO  c.n.d.c.DataRetrievalController -    - 子部门记录: 0 条
2025-06-09 12:03:00.321 [http-nio-8080-exec-7] INFO  c.n.d.c.DataRetrievalController -    - 响应总记录数: 0 条
2025-06-09 12:03:31.753 [http-nio-8080-exec-4] INFO  c.n.d.c.DataRetrievalController - 🔍 [API请求] 获取部门数据 - 原始参数: startDate=2024-02-01%2000:00:00, endDate=2024-03-31%2023:59:59
2025-06-09 12:03:31.754 [http-nio-8080-exec-4] DEBUG c.n.d.utils.DateUtils - 尝试解析日期字符串: 原始=2024-02-01%2000:00:00, 解码后=2024-02-01 00:00:00
2025-06-09 12:03:31.754 [http-nio-8080-exec-4] DEBUG c.n.d.utils.DateUtils - 成功使用格式 yyyy-MM-dd HH:mm:ss 解析日期: Thu Feb 01 00:00:00 CST 2024
2025-06-09 12:03:31.754 [http-nio-8080-exec-4] DEBUG c.n.d.utils.DateUtils - 尝试解析日期字符串: 原始=2024-03-31%2023:59:59, 解码后=2024-03-31 23:59:59
2025-06-09 12:03:31.754 [http-nio-8080-exec-4] DEBUG c.n.d.utils.DateUtils - 成功使用格式 yyyy-MM-dd HH:mm:ss 解析日期: Sun Mar 31 23:59:59 CST 2024
2025-06-09 12:03:31.754 [http-nio-8080-exec-4] INFO  c.n.d.c.DataRetrievalController - 📅 [日期解析] 成功解析日期参数 - 开始时间: Thu Feb 01 00:00:00 CST 2024, 结束时间: Sun Mar 31 23:59:59 CST 2024
2025-06-09 12:03:31.754 [http-nio-8080-exec-4] INFO  c.n.d.c.DataRetrievalController - 🚀 [服务调用] 开始调用数据获取服务...
2025-06-09 12:03:31.754 [http-nio-8080-exec-4] INFO  c.n.d.s.i.DataRetrievalServiceImpl - === 开始获取部门数据 ===
2025-06-09 12:03:31.754 [http-nio-8080-exec-4] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 查询时间范围: Thu Feb 01 00:00:00 CST 2024 至 Sun Mar 31 23:59:59 CST 2024
2025-06-09 12:03:31.754 [http-nio-8080-exec-4] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在调用远程SOAP接口获取部门数据...
2025-06-09 12:03:32.242 [http-nio-8080-exec-4] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 远程SOAP接口调用成功，响应数据长度: 25806 字符
2025-06-09 12:03:32.242 [http-nio-8080-exec-4] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在解析SOAP响应，提取XML数据...
2025-06-09 12:03:32.247 [http-nio-8080-exec-4] INFO  c.n.d.s.i.DataRetrievalServiceImpl - XML数据提取成功，数据长度: 19576 字符
2025-06-09 12:03:32.247 [http-nio-8080-exec-4] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在解析XML数据为部门DTO对象...
2025-06-09 12:03:32.250 [http-nio-8080-exec-4] INFO  c.n.d.s.i.DataRetrievalServiceImpl - ✅ 部门数据解析完成:
2025-06-09 12:03:32.250 [http-nio-8080-exec-4] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 主部门记录: 9 条 (对应 department 表)
2025-06-09 12:03:32.250 [http-nio-8080-exec-4] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 子部门记录: 0 条 (对应 department_child 表)
2025-06-09 12:03:32.250 [http-nio-8080-exec-4] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 总计: 9 条记录
2025-06-09 12:03:32.251 [http-nio-8080-exec-4] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 部门数据示例:
2025-06-09 12:03:32.251 [http-nio-8080-exec-4] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 部门: 资产室 | 代码: X06020000 | UUID: null | 层级: null
2025-06-09 12:03:32.251 [http-nio-8080-exec-4] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 部门: 投资财务室 | 代码: X06080000 | UUID: null | 层级: null
2025-06-09 12:03:32.251 [http-nio-8080-exec-4] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 部门: 检化验中心 | 代码: X83000101 | UUID: null | 层级: null
2025-06-09 12:03:32.251 [http-nio-8080-exec-4] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - ... 还有 6 个部门
2025-06-09 12:03:32.251 [http-nio-8080-exec-4] INFO  c.n.d.s.i.DataRetrievalServiceImpl - === 部门数据获取完成，总计: 9条记录 ===
2025-06-09 12:03:32.251 [http-nio-8080-exec-4] INFO  c.n.d.c.DataRetrievalController - ✅ [API响应] 部门数据获取成功:
2025-06-09 12:03:32.251 [http-nio-8080-exec-4] INFO  c.n.d.c.DataRetrievalController -    - 主部门记录: 9 条
2025-06-09 12:03:32.251 [http-nio-8080-exec-4] INFO  c.n.d.c.DataRetrievalController -    - 子部门记录: 0 条
2025-06-09 12:03:32.251 [http-nio-8080-exec-4] INFO  c.n.d.c.DataRetrievalController -    - 响应总记录数: 9 条
2025-06-09 12:04:17.232 [http-nio-8080-exec-1] INFO  c.n.d.c.DataRetrievalController - 🔍 [API请求] 获取部门数据 - 原始参数: startDate=2024-04-01%2000:00:00, endDate=2024-10-31%2023:59:59
2025-06-09 12:04:17.232 [http-nio-8080-exec-1] DEBUG c.n.d.utils.DateUtils - 尝试解析日期字符串: 原始=2024-04-01%2000:00:00, 解码后=2024-04-01 00:00:00
2025-06-09 12:04:17.232 [http-nio-8080-exec-1] DEBUG c.n.d.utils.DateUtils - 成功使用格式 yyyy-MM-dd HH:mm:ss 解析日期: Mon Apr 01 00:00:00 CST 2024
2025-06-09 12:04:17.232 [http-nio-8080-exec-1] DEBUG c.n.d.utils.DateUtils - 尝试解析日期字符串: 原始=2024-10-31%2023:59:59, 解码后=2024-10-31 23:59:59
2025-06-09 12:04:17.232 [http-nio-8080-exec-1] DEBUG c.n.d.utils.DateUtils - 成功使用格式 yyyy-MM-dd HH:mm:ss 解析日期: Thu Oct 31 23:59:59 CST 2024
2025-06-09 12:04:17.233 [http-nio-8080-exec-1] INFO  c.n.d.c.DataRetrievalController - 📅 [日期解析] 成功解析日期参数 - 开始时间: Mon Apr 01 00:00:00 CST 2024, 结束时间: Thu Oct 31 23:59:59 CST 2024
2025-06-09 12:04:17.233 [http-nio-8080-exec-1] INFO  c.n.d.c.DataRetrievalController - 🚀 [服务调用] 开始调用数据获取服务...
2025-06-09 12:04:17.233 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl - === 开始获取部门数据 ===
2025-06-09 12:04:17.233 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 查询时间范围: Mon Apr 01 00:00:00 CST 2024 至 Thu Oct 31 23:59:59 CST 2024
2025-06-09 12:04:17.233 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在调用远程SOAP接口获取部门数据...
2025-06-09 12:04:19.492 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 远程SOAP接口调用成功，响应数据长度: 396471 字符
2025-06-09 12:04:19.492 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在解析SOAP响应，提取XML数据...
2025-06-09 12:04:19.554 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl - XML数据提取成功，数据长度: 306145 字符
2025-06-09 12:04:19.554 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在解析XML数据为部门DTO对象...
2025-06-09 12:04:19.609 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl - ✅ 部门数据解析完成:
2025-06-09 12:04:19.609 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 主部门记录: 126 条 (对应 department 表)
2025-06-09 12:04:19.609 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 子部门记录: 0 条 (对应 department_child 表)
2025-06-09 12:04:19.609 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 总计: 126 条记录
2025-06-09 12:04:19.609 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 部门数据示例:
2025-06-09 12:04:19.610 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 部门: 钳工班 | 代码: X42090003 | UUID: null | 层级: null
2025-06-09 12:04:19.610 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 部门: 耐火五库班 | 代码: X52060007 | UUID: null | 层级: null
2025-06-09 12:04:19.610 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 部门: 炼钢库班 | 代码: X52060011 | UUID: null | 层级: null
2025-06-09 12:04:19.610 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - ... 还有 123 个部门
2025-06-09 12:04:19.610 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl - === 部门数据获取完成，总计: 126条记录 ===
2025-06-09 12:04:19.610 [http-nio-8080-exec-1] INFO  c.n.d.c.DataRetrievalController - ✅ [API响应] 部门数据获取成功:
2025-06-09 12:04:19.610 [http-nio-8080-exec-1] INFO  c.n.d.c.DataRetrievalController -    - 主部门记录: 126 条
2025-06-09 12:04:19.610 [http-nio-8080-exec-1] INFO  c.n.d.c.DataRetrievalController -    - 子部门记录: 0 条
2025-06-09 12:04:19.610 [http-nio-8080-exec-1] INFO  c.n.d.c.DataRetrievalController -    - 响应总记录数: 126 条
2025-06-09 14:24:33.450 [http-nio-8080-exec-1] INFO  c.n.d.c.DataRetrievalController - 🔍 [API请求] 获取部门数据 - 原始参数: startDate=2024-11-01%2000:00:00, endDate=2024-11-10%2023:59:59
2025-06-09 14:24:33.452 [http-nio-8080-exec-1] DEBUG c.n.d.utils.DateUtils - 尝试解析日期字符串: 原始=2024-11-01%2000:00:00, 解码后=2024-11-01 00:00:00
2025-06-09 14:24:33.454 [http-nio-8080-exec-1] DEBUG c.n.d.utils.DateUtils - 成功使用格式 yyyy-MM-dd HH:mm:ss 解析日期: Fri Nov 01 00:00:00 CST 2024
2025-06-09 14:24:33.454 [http-nio-8080-exec-1] DEBUG c.n.d.utils.DateUtils - 尝试解析日期字符串: 原始=2024-11-10%2023:59:59, 解码后=2024-11-10 23:59:59
2025-06-09 14:24:33.456 [http-nio-8080-exec-1] DEBUG c.n.d.utils.DateUtils - 成功使用格式 yyyy-MM-dd HH:mm:ss 解析日期: Sun Nov 10 23:59:59 CST 2024
2025-06-09 14:24:33.456 [http-nio-8080-exec-1] INFO  c.n.d.c.DataRetrievalController - 📅 [日期解析] 成功解析日期参数 - 开始时间: Fri Nov 01 00:00:00 CST 2024, 结束时间: Sun Nov 10 23:59:59 CST 2024
2025-06-09 14:24:33.456 [http-nio-8080-exec-1] INFO  c.n.d.c.DataRetrievalController - 🚀 [服务调用] 开始调用数据获取服务...
2025-06-09 14:24:33.460 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl - === 开始获取部门数据 ===
2025-06-09 14:24:33.461 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 查询时间范围: Fri Nov 01 00:00:00 CST 2024 至 Sun Nov 10 23:59:59 CST 2024
2025-06-09 14:24:33.461 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在调用远程SOAP接口获取部门数据...
2025-06-09 14:24:35.672 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 远程SOAP接口调用成功，响应数据长度: 3278 字符
2025-06-09 14:24:35.673 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在解析SOAP响应，提取XML数据...
2025-06-09 14:24:36.172 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl - XML数据提取成功，数据长度: 2232 字符
2025-06-09 14:24:36.174 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在解析XML数据为部门DTO对象...
2025-06-09 14:24:36.193 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl - ✅ 部门数据解析完成:
2025-06-09 14:24:36.194 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 主部门记录: 1 条 (对应 department 表)
2025-06-09 14:24:36.194 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 子部门记录: 0 条 (对应 department_child 表)
2025-06-09 14:24:36.194 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 总计: 1 条记录
2025-06-09 14:24:36.195 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 部门数据示例:
2025-06-09 14:24:36.201 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 部门: 万盛赋能组 | 代码: X57120403 | UUID: null | 层级: null
2025-06-09 14:24:36.202 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl - === 部门数据获取完成，总计: 1条记录 ===
2025-06-09 14:24:36.318 [http-nio-8080-exec-1] INFO  c.n.d.c.DataRetrievalController - ✅ [API响应] 部门数据获取成功:
2025-06-09 14:24:36.319 [http-nio-8080-exec-1] INFO  c.n.d.c.DataRetrievalController -    - 主部门记录: 1 条
2025-06-09 14:24:36.322 [http-nio-8080-exec-1] INFO  c.n.d.c.DataRetrievalController -    - 子部门记录: 0 条
2025-06-09 14:24:36.324 [http-nio-8080-exec-1] INFO  c.n.d.c.DataRetrievalController -    - 响应总记录数: 1 条
2025-06-09 14:24:48.684 [http-nio-8080-exec-2] INFO  c.n.d.c.DataRetrievalController - 🔍 [API请求] 获取部门数据 - 原始参数: startDate=2024-11-01%2000:00:00, endDate=2024-11-30%2023:59:59
2025-06-09 14:24:48.686 [http-nio-8080-exec-2] DEBUG c.n.d.utils.DateUtils - 尝试解析日期字符串: 原始=2024-11-01%2000:00:00, 解码后=2024-11-01 00:00:00
2025-06-09 14:24:48.686 [http-nio-8080-exec-2] DEBUG c.n.d.utils.DateUtils - 成功使用格式 yyyy-MM-dd HH:mm:ss 解析日期: Fri Nov 01 00:00:00 CST 2024
2025-06-09 14:24:48.687 [http-nio-8080-exec-2] DEBUG c.n.d.utils.DateUtils - 尝试解析日期字符串: 原始=2024-11-30%2023:59:59, 解码后=2024-11-30 23:59:59
2025-06-09 14:24:48.687 [http-nio-8080-exec-2] DEBUG c.n.d.utils.DateUtils - 成功使用格式 yyyy-MM-dd HH:mm:ss 解析日期: Sat Nov 30 23:59:59 CST 2024
2025-06-09 14:24:48.687 [http-nio-8080-exec-2] INFO  c.n.d.c.DataRetrievalController - 📅 [日期解析] 成功解析日期参数 - 开始时间: Fri Nov 01 00:00:00 CST 2024, 结束时间: Sat Nov 30 23:59:59 CST 2024
2025-06-09 14:24:48.687 [http-nio-8080-exec-2] INFO  c.n.d.c.DataRetrievalController - 🚀 [服务调用] 开始调用数据获取服务...
2025-06-09 14:24:48.687 [http-nio-8080-exec-2] INFO  c.n.d.s.i.DataRetrievalServiceImpl - === 开始获取部门数据 ===
2025-06-09 14:24:48.687 [http-nio-8080-exec-2] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 查询时间范围: Fri Nov 01 00:00:00 CST 2024 至 Sat Nov 30 23:59:59 CST 2024
2025-06-09 14:24:48.688 [http-nio-8080-exec-2] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在调用远程SOAP接口获取部门数据...
2025-06-09 14:24:49.434 [http-nio-8080-exec-2] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 远程SOAP接口调用成功，响应数据长度: 101927 字符
2025-06-09 14:24:49.434 [http-nio-8080-exec-2] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在解析SOAP响应，提取XML数据...
2025-06-09 14:24:49.481 [http-nio-8080-exec-2] INFO  c.n.d.s.i.DataRetrievalServiceImpl - XML数据提取成功，数据长度: 78885 字符
2025-06-09 14:24:49.482 [http-nio-8080-exec-2] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在解析XML数据为部门DTO对象...
2025-06-09 14:24:49.522 [http-nio-8080-exec-2] INFO  c.n.d.s.i.DataRetrievalServiceImpl - ✅ 部门数据解析完成:
2025-06-09 14:24:49.522 [http-nio-8080-exec-2] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 主部门记录: 26 条 (对应 department 表)
2025-06-09 14:24:49.522 [http-nio-8080-exec-2] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 子部门记录: 0 条 (对应 department_child 表)
2025-06-09 14:24:49.523 [http-nio-8080-exec-2] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 总计: 26 条记录
2025-06-09 14:24:49.523 [http-nio-8080-exec-2] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 部门数据示例:
2025-06-09 14:24:49.523 [http-nio-8080-exec-2] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 部门: 电炉连铸车间 | 代码: X42060000 | UUID: null | 层级: null
2025-06-09 14:24:49.523 [http-nio-8080-exec-2] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 部门: 电炉检验班 | 代码: X42060006 | UUID: null | 层级: null
2025-06-09 14:24:49.523 [http-nio-8080-exec-2] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 部门: 电炉运行车间 | 代码: X42070000 | UUID: null | 层级: null
2025-06-09 14:24:49.523 [http-nio-8080-exec-2] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - ... 还有 23 个部门
2025-06-09 14:24:49.524 [http-nio-8080-exec-2] INFO  c.n.d.s.i.DataRetrievalServiceImpl - === 部门数据获取完成，总计: 26条记录 ===
2025-06-09 14:24:49.525 [http-nio-8080-exec-2] INFO  c.n.d.c.DataRetrievalController - ✅ [API响应] 部门数据获取成功:
2025-06-09 14:24:49.527 [http-nio-8080-exec-2] INFO  c.n.d.c.DataRetrievalController -    - 主部门记录: 26 条
2025-06-09 14:24:49.528 [http-nio-8080-exec-2] INFO  c.n.d.c.DataRetrievalController -    - 子部门记录: 0 条
2025-06-09 14:24:49.529 [http-nio-8080-exec-2] INFO  c.n.d.c.DataRetrievalController -    - 响应总记录数: 26 条
2025-06-09 15:22:47.893 [http-nio-8080-exec-3] INFO  c.n.d.c.DataRetrievalController - 🔍 [API请求] 获取部门数据 - 原始参数: startDate=2024-11-01%2000:00:00, endDate=2024-11-30%2023:59:59
2025-06-09 15:22:47.933 [http-nio-8080-exec-3] DEBUG c.n.d.utils.DateUtils - 尝试解析日期字符串: 原始=2024-11-01%2000:00:00, 解码后=2024-11-01 00:00:00
2025-06-09 15:22:47.936 [http-nio-8080-exec-3] DEBUG c.n.d.utils.DateUtils - 成功使用格式 yyyy-MM-dd HH:mm:ss 解析日期: Fri Nov 01 00:00:00 CST 2024
2025-06-09 15:22:47.940 [http-nio-8080-exec-3] DEBUG c.n.d.utils.DateUtils - 尝试解析日期字符串: 原始=2024-11-30%2023:59:59, 解码后=2024-11-30 23:59:59
2025-06-09 15:22:47.940 [http-nio-8080-exec-3] DEBUG c.n.d.utils.DateUtils - 成功使用格式 yyyy-MM-dd HH:mm:ss 解析日期: Sat Nov 30 23:59:59 CST 2024
2025-06-09 15:22:47.940 [http-nio-8080-exec-3] INFO  c.n.d.c.DataRetrievalController - 📅 [日期解析] 成功解析日期参数 - 开始时间: Fri Nov 01 00:00:00 CST 2024, 结束时间: Sat Nov 30 23:59:59 CST 2024
2025-06-09 15:22:47.940 [http-nio-8080-exec-3] INFO  c.n.d.c.DataRetrievalController - 🚀 [服务调用] 开始调用数据获取服务...
2025-06-09 15:22:47.989 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl - === 开始获取部门数据 ===
2025-06-09 15:22:47.990 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 查询时间范围: Fri Nov 01 00:00:00 CST 2024 至 Sat Nov 30 23:59:59 CST 2024
2025-06-09 15:22:47.990 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在调用远程SOAP接口获取部门数据...
2025-06-09 15:22:50.363 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 远程SOAP接口调用成功，响应数据长度: 101927 字符
2025-06-09 15:22:50.364 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在解析SOAP响应，提取XML数据...
2025-06-09 15:22:50.609 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl - XML数据提取成功，数据长度: 78885 字符
2025-06-09 15:22:50.612 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在解析XML数据为部门DTO对象...
2025-06-09 15:22:50.663 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl - ✅ 部门数据解析完成:
2025-06-09 15:22:50.664 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 主部门记录: 26 条 (对应 department 表)
2025-06-09 15:22:50.664 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 子部门记录: 47 条 (对应 department_child 表)
2025-06-09 15:22:50.664 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 总计: 73 条记录
2025-06-09 15:22:50.664 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 部门数据示例:
2025-06-09 15:22:50.669 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 部门: 电炉连铸车间 | 代码: X42060000 | UUID: 103056bc-9dd0-4232-8b1e-a318874fbf9b | 层级: null
2025-06-09 15:22:50.670 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 部门: 电炉检验班 | 代码: X42060006 | UUID: 8e382b68-b3c7-4e2e-822b-81d760f8056d | 层级: null
2025-06-09 15:22:50.671 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 部门: 电炉运行车间 | 代码: X42070000 | UUID: 23d3ccd2-b41b-4f39-a4bb-d6a50e538a35 | 层级: null
2025-06-09 15:22:50.672 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - ... 还有 23 个部门
2025-06-09 15:22:50.672 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl - === 部门数据获取完成，总计: 26条记录 ===
2025-06-09 15:22:50.675 [http-nio-8080-exec-3] INFO  c.n.d.c.DataRetrievalController - ✅ [API响应] 部门数据获取成功:
2025-06-09 15:22:50.675 [http-nio-8080-exec-3] INFO  c.n.d.c.DataRetrievalController -    - 主部门记录: 26 条
2025-06-09 15:22:50.675 [http-nio-8080-exec-3] INFO  c.n.d.c.DataRetrievalController -    - 子部门记录: 47 条
2025-06-09 15:22:50.675 [http-nio-8080-exec-3] INFO  c.n.d.c.DataRetrievalController -    - 响应总记录数: 26 条
2025-06-09 15:24:28.521 [http-nio-8080-exec-7] INFO  c.n.d.c.DataRetrievalController - 🔍 [API请求] 获取员工数据 - 原始参数: startDate=2024-07-07%2000:00:00, endDate=2024-07-07%2023:59:59
2025-06-09 15:24:28.522 [http-nio-8080-exec-7] DEBUG c.n.d.utils.DateUtils - 尝试解析日期字符串: 原始=2024-07-07%2000:00:00, 解码后=2024-07-07 00:00:00
2025-06-09 15:24:28.523 [http-nio-8080-exec-7] DEBUG c.n.d.utils.DateUtils - 成功使用格式 yyyy-MM-dd HH:mm:ss 解析日期: Sun Jul 07 00:00:00 CST 2024
2025-06-09 15:24:28.523 [http-nio-8080-exec-7] DEBUG c.n.d.utils.DateUtils - 尝试解析日期字符串: 原始=2024-07-07%2023:59:59, 解码后=2024-07-07 23:59:59
2025-06-09 15:24:28.523 [http-nio-8080-exec-7] DEBUG c.n.d.utils.DateUtils - 成功使用格式 yyyy-MM-dd HH:mm:ss 解析日期: Sun Jul 07 23:59:59 CST 2024
2025-06-09 15:24:28.524 [http-nio-8080-exec-7] INFO  c.n.d.c.DataRetrievalController - 📅 [日期解析] 成功解析日期参数 - 开始时间: Sun Jul 07 00:00:00 CST 2024, 结束时间: Sun Jul 07 23:59:59 CST 2024
2025-06-09 15:24:28.525 [http-nio-8080-exec-7] INFO  c.n.d.c.DataRetrievalController - 🚀 [服务调用] 开始调用数据获取服务...
2025-06-09 15:24:28.526 [http-nio-8080-exec-7] INFO  c.n.d.s.i.DataRetrievalServiceImpl - === 开始获取员工数据 ===
2025-06-09 15:24:28.526 [http-nio-8080-exec-7] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 查询时间范围: Sun Jul 07 00:00:00 CST 2024 至 Sun Jul 07 23:59:59 CST 2024
2025-06-09 15:24:28.526 [http-nio-8080-exec-7] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在调用远程SOAP接口获取员工数据...
2025-06-09 15:24:32.561 [http-nio-8080-exec-7] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 远程SOAP接口调用成功，响应数据长度: 522442 字符
2025-06-09 15:24:32.562 [http-nio-8080-exec-7] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在解析SOAP响应，提取XML数据...
2025-06-09 15:24:32.619 [http-nio-8080-exec-7] INFO  c.n.d.s.i.DataRetrievalServiceImpl - XML数据提取成功，数据长度: 421672 字符
2025-06-09 15:24:32.619 [http-nio-8080-exec-7] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在解析XML数据为员工DTO对象...
2025-06-09 15:24:32.672 [http-nio-8080-exec-7] INFO  c.n.d.s.i.DataRetrievalServiceImpl - ✅ 员工数据解析完成:
2025-06-09 15:24:32.672 [http-nio-8080-exec-7] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 员工主记录: 52 条 (对应 employee 表)
2025-06-09 15:24:32.672 [http-nio-8080-exec-7] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 员工岗位记录: 140 条 (对应 employee_position 表)
2025-06-09 15:24:32.672 [http-nio-8080-exec-7] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 员工职称记录: 116 条 (对应 employee_title 表)
2025-06-09 15:24:32.672 [http-nio-8080-exec-7] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 员工系统记录: 52 条 (对应 employee_system 表)
2025-06-09 15:24:32.672 [http-nio-8080-exec-7] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 总计: 360 条记录
2025-06-09 15:24:32.672 [http-nio-8080-exec-7] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 员工数据示例:
2025-06-09 15:24:32.673 [http-nio-8080-exec-7] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 员工: 张清辉 | 工号: 019359 | MDM ID: 81ee8ed6-edab-4a1d-832b-812dcaf55aee | 状态: A
2025-06-09 15:24:32.673 [http-nio-8080-exec-7] INFO  c.n.d.s.i.DataRetrievalServiceImpl -     岗位: 3 个
2025-06-09 15:24:32.674 [http-nio-8080-exec-7] INFO  c.n.d.s.i.DataRetrievalServiceImpl -     职称: 3 个
2025-06-09 15:24:32.674 [http-nio-8080-exec-7] INFO  c.n.d.s.i.DataRetrievalServiceImpl -     系统: 1 个
2025-06-09 15:24:32.674 [http-nio-8080-exec-7] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 员工: 费燕 | 工号: 001421 | MDM ID: 9db5e257-45b9-41fb-996b-c5ecf4b2d9ba | 状态: A
2025-06-09 15:24:32.674 [http-nio-8080-exec-7] INFO  c.n.d.s.i.DataRetrievalServiceImpl -     岗位: 4 个
2025-06-09 15:24:32.674 [http-nio-8080-exec-7] INFO  c.n.d.s.i.DataRetrievalServiceImpl -     职称: 2 个
2025-06-09 15:24:32.674 [http-nio-8080-exec-7] INFO  c.n.d.s.i.DataRetrievalServiceImpl -     系统: 1 个
2025-06-09 15:24:32.674 [http-nio-8080-exec-7] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 员工: 刘继宏 | 工号: 009361 | MDM ID: 23ccc025-1a43-47ac-aa49-f448e6cfa311 | 状态: A
2025-06-09 15:24:32.674 [http-nio-8080-exec-7] INFO  c.n.d.s.i.DataRetrievalServiceImpl -     岗位: 2 个
2025-06-09 15:24:32.674 [http-nio-8080-exec-7] INFO  c.n.d.s.i.DataRetrievalServiceImpl -     职称: 2 个
2025-06-09 15:24:32.675 [http-nio-8080-exec-7] INFO  c.n.d.s.i.DataRetrievalServiceImpl -     系统: 1 个
2025-06-09 15:24:32.675 [http-nio-8080-exec-7] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - ... 还有 49 个员工
2025-06-09 15:24:32.675 [http-nio-8080-exec-7] INFO  c.n.d.s.i.DataRetrievalServiceImpl - === 员工数据获取完成，总计: 52条记录 ===
2025-06-09 15:24:32.677 [http-nio-8080-exec-7] INFO  c.n.d.c.DataRetrievalController - ✅ [API响应] 员工数据获取成功:
2025-06-09 15:24:32.678 [http-nio-8080-exec-7] INFO  c.n.d.c.DataRetrievalController -    - 员工主记录: 52 条
2025-06-09 15:24:32.678 [http-nio-8080-exec-7] INFO  c.n.d.c.DataRetrievalController -    - 岗位记录: 140 条
2025-06-09 15:24:32.678 [http-nio-8080-exec-7] INFO  c.n.d.c.DataRetrievalController -    - 职称记录: 116 条
2025-06-09 15:24:32.678 [http-nio-8080-exec-7] INFO  c.n.d.c.DataRetrievalController -    - 系统记录: 52 条
2025-06-09 15:24:32.679 [http-nio-8080-exec-7] INFO  c.n.d.c.DataRetrievalController -    - 响应总记录数: 52 条
2025-06-09 16:08:36.647 [http-nio-8080-exec-1] INFO  c.n.d.c.DataRetrievalController - 🔍 [API请求] 获取员工数据 - 原始参数: startDate=2024-07-07%2000:00:00, endDate=2024-07-07%2023:59:59
2025-06-09 16:08:36.649 [http-nio-8080-exec-1] DEBUG c.n.d.utils.DateUtils - 尝试解析日期字符串: 原始=2024-07-07%2000:00:00, 解码后=2024-07-07 00:00:00
2025-06-09 16:08:36.650 [http-nio-8080-exec-1] DEBUG c.n.d.utils.DateUtils - 成功使用格式 yyyy-MM-dd HH:mm:ss 解析日期: Sun Jul 07 00:00:00 CST 2024
2025-06-09 16:08:36.656 [http-nio-8080-exec-1] DEBUG c.n.d.utils.DateUtils - 尝试解析日期字符串: 原始=2024-07-07%2023:59:59, 解码后=2024-07-07 23:59:59
2025-06-09 16:08:36.657 [http-nio-8080-exec-1] DEBUG c.n.d.utils.DateUtils - 成功使用格式 yyyy-MM-dd HH:mm:ss 解析日期: Sun Jul 07 23:59:59 CST 2024
2025-06-09 16:08:36.658 [http-nio-8080-exec-1] INFO  c.n.d.c.DataRetrievalController - 📅 [日期解析] 成功解析日期参数 - 开始时间: Sun Jul 07 00:00:00 CST 2024, 结束时间: Sun Jul 07 23:59:59 CST 2024
2025-06-09 16:08:36.658 [http-nio-8080-exec-1] INFO  c.n.d.c.DataRetrievalController - 🚀 [服务调用] 开始调用数据获取服务...
2025-06-09 16:08:36.659 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl - === 开始获取员工数据 ===
2025-06-09 16:08:36.660 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 查询时间范围: Sun Jul 07 00:00:00 CST 2024 至 Sun Jul 07 23:59:59 CST 2024
2025-06-09 16:08:36.660 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在调用远程SOAP接口获取员工数据...
2025-06-09 16:08:40.769 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 远程SOAP接口调用成功，响应数据长度: 522442 字符
2025-06-09 16:08:40.769 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在解析SOAP响应，提取XML数据...
2025-06-09 16:08:40.866 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl - XML数据提取成功，数据长度: 421672 字符
2025-06-09 16:08:40.867 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在解析XML数据为员工DTO对象...
2025-06-09 16:08:40.954 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl - ✅ 员工数据解析完成:
2025-06-09 16:08:40.954 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 员工主记录: 52 条 (对应 employee 表)
2025-06-09 16:08:40.955 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 员工岗位记录: 140 条 (对应 employee_position 表)
2025-06-09 16:08:40.955 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 员工职称记录: 116 条 (对应 employee_title 表)
2025-06-09 16:08:40.955 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 员工系统记录: 52 条 (对应 employee_system 表)
2025-06-09 16:08:40.955 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 总计: 360 条记录
2025-06-09 16:08:40.955 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 员工数据示例:
2025-06-09 16:08:40.956 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 员工: 张清辉 | 工号: 019359 | MDM ID: 81ee8ed6-edab-4a1d-832b-812dcaf55aee | 状态: A
2025-06-09 16:08:40.956 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl -     岗位: 3 个
2025-06-09 16:08:40.956 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl -     职称: 3 个
2025-06-09 16:08:40.956 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl -     系统: 1 个
2025-06-09 16:08:40.956 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 员工: 费燕 | 工号: 001421 | MDM ID: 9db5e257-45b9-41fb-996b-c5ecf4b2d9ba | 状态: A
2025-06-09 16:08:40.956 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl -     岗位: 4 个
2025-06-09 16:08:40.957 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl -     职称: 2 个
2025-06-09 16:08:40.958 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl -     系统: 1 个
2025-06-09 16:08:40.958 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 员工: 刘继宏 | 工号: 009361 | MDM ID: 23ccc025-1a43-47ac-aa49-f448e6cfa311 | 状态: A
2025-06-09 16:08:40.959 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl -     岗位: 2 个
2025-06-09 16:08:40.959 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl -     职称: 2 个
2025-06-09 16:08:40.959 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl -     系统: 1 个
2025-06-09 16:08:40.959 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - ... 还有 49 个员工
2025-06-09 16:08:40.959 [http-nio-8080-exec-1] INFO  c.n.d.s.i.DataRetrievalServiceImpl - === 员工数据获取完成，总计: 52条记录 ===
2025-06-09 16:08:40.960 [http-nio-8080-exec-1] INFO  c.n.d.c.DataRetrievalController - ✅ [API响应] 员工数据获取成功:
2025-06-09 16:08:40.960 [http-nio-8080-exec-1] INFO  c.n.d.c.DataRetrievalController -    - 员工主记录: 52 条
2025-06-09 16:08:40.961 [http-nio-8080-exec-1] INFO  c.n.d.c.DataRetrievalController -    - 岗位记录: 140 条
2025-06-09 16:08:40.961 [http-nio-8080-exec-1] INFO  c.n.d.c.DataRetrievalController -    - 职称记录: 116 条
2025-06-09 16:08:40.961 [http-nio-8080-exec-1] INFO  c.n.d.c.DataRetrievalController -    - 系统记录: 52 条
2025-06-09 16:08:40.961 [http-nio-8080-exec-1] INFO  c.n.d.c.DataRetrievalController -    - 响应总记录数: 52 条
2025-06-09 16:31:13.350 [http-nio-8080-exec-5] INFO  c.n.d.c.DataRetrievalController - 🔍 [API请求] 获取部门数据 - 原始参数: startDate=2024-07-07%2000:00:00, endDate=2024-07-07%2023:59:59
2025-06-09 16:31:13.351 [http-nio-8080-exec-5] DEBUG c.n.d.utils.DateUtils - 尝试解析日期字符串: 原始=2024-07-07%2000:00:00, 解码后=2024-07-07 00:00:00
2025-06-09 16:31:13.353 [http-nio-8080-exec-5] DEBUG c.n.d.utils.DateUtils - 成功使用格式 yyyy-MM-dd HH:mm:ss 解析日期: Sun Jul 07 00:00:00 CST 2024
2025-06-09 16:31:13.356 [http-nio-8080-exec-5] DEBUG c.n.d.utils.DateUtils - 尝试解析日期字符串: 原始=2024-07-07%2023:59:59, 解码后=2024-07-07 23:59:59
2025-06-09 16:31:13.356 [http-nio-8080-exec-5] DEBUG c.n.d.utils.DateUtils - 成功使用格式 yyyy-MM-dd HH:mm:ss 解析日期: Sun Jul 07 23:59:59 CST 2024
2025-06-09 16:31:13.356 [http-nio-8080-exec-5] INFO  c.n.d.c.DataRetrievalController - 📅 [日期解析] 成功解析日期参数 - 开始时间: Sun Jul 07 00:00:00 CST 2024, 结束时间: Sun Jul 07 23:59:59 CST 2024
2025-06-09 16:31:13.356 [http-nio-8080-exec-5] INFO  c.n.d.c.DataRetrievalController - 🚀 [服务调用] 开始调用数据获取服务...
2025-06-09 16:31:13.357 [http-nio-8080-exec-5] INFO  c.n.d.s.i.DataRetrievalServiceImpl - === 开始获取部门数据 ===
2025-06-09 16:31:13.357 [http-nio-8080-exec-5] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 查询时间范围: Sun Jul 07 00:00:00 CST 2024 至 Sun Jul 07 23:59:59 CST 2024
2025-06-09 16:31:13.358 [http-nio-8080-exec-5] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在调用远程SOAP接口获取部门数据...
2025-06-09 16:31:14.216 [http-nio-8080-exec-5] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 远程SOAP接口调用成功，响应数据长度: 442 字符
2025-06-09 16:31:14.217 [http-nio-8080-exec-5] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在解析SOAP响应，提取XML数据...
2025-06-09 16:31:14.224 [http-nio-8080-exec-5] INFO  c.n.d.s.i.DataRetrievalServiceImpl - XML数据提取成功，数据长度: 50 字符
2025-06-09 16:31:14.224 [http-nio-8080-exec-5] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在解析XML数据为部门DTO对象...
2025-06-09 16:31:14.227 [http-nio-8080-exec-5] INFO  c.n.d.s.i.DataRetrievalServiceImpl - ✅ 部门数据解析完成:
2025-06-09 16:31:14.227 [http-nio-8080-exec-5] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 主部门记录: 0 条 (对应 department 表)
2025-06-09 16:31:14.227 [http-nio-8080-exec-5] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 子部门记录: 0 条 (对应 department_child 表)
2025-06-09 16:31:14.227 [http-nio-8080-exec-5] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 总计: 0 条记录
2025-06-09 16:31:14.227 [http-nio-8080-exec-5] INFO  c.n.d.s.i.DataRetrievalServiceImpl - === 部门数据获取完成，总计: 0条记录 ===
2025-06-09 16:31:14.228 [http-nio-8080-exec-5] INFO  c.n.d.c.DataRetrievalController - ✅ [API响应] 部门数据获取成功:
2025-06-09 16:31:14.228 [http-nio-8080-exec-5] INFO  c.n.d.c.DataRetrievalController -    - 主部门记录: 0 条
2025-06-09 16:31:14.228 [http-nio-8080-exec-5] INFO  c.n.d.c.DataRetrievalController -    - 子部门记录: 0 条
2025-06-09 16:31:14.228 [http-nio-8080-exec-5] INFO  c.n.d.c.DataRetrievalController -    - 响应总记录数: 0 条
2025-06-09 16:31:14.355 [http-nio-8080-exec-6] INFO  c.n.d.c.DataRetrievalController - 🔍 [API请求] 获取员工数据 - 原始参数: startDate=2024-07-07%2000:00:00, endDate=2024-07-07%2023:59:59
2025-06-09 16:31:14.355 [http-nio-8080-exec-6] DEBUG c.n.d.utils.DateUtils - 尝试解析日期字符串: 原始=2024-07-07%2000:00:00, 解码后=2024-07-07 00:00:00
2025-06-09 16:31:14.355 [http-nio-8080-exec-6] DEBUG c.n.d.utils.DateUtils - 成功使用格式 yyyy-MM-dd HH:mm:ss 解析日期: Sun Jul 07 00:00:00 CST 2024
2025-06-09 16:31:14.356 [http-nio-8080-exec-6] DEBUG c.n.d.utils.DateUtils - 尝试解析日期字符串: 原始=2024-07-07%2023:59:59, 解码后=2024-07-07 23:59:59
2025-06-09 16:31:14.356 [http-nio-8080-exec-6] DEBUG c.n.d.utils.DateUtils - 成功使用格式 yyyy-MM-dd HH:mm:ss 解析日期: Sun Jul 07 23:59:59 CST 2024
2025-06-09 16:31:14.356 [http-nio-8080-exec-6] INFO  c.n.d.c.DataRetrievalController - 📅 [日期解析] 成功解析日期参数 - 开始时间: Sun Jul 07 00:00:00 CST 2024, 结束时间: Sun Jul 07 23:59:59 CST 2024
2025-06-09 16:31:14.356 [http-nio-8080-exec-6] INFO  c.n.d.c.DataRetrievalController - 🚀 [服务调用] 开始调用数据获取服务...
2025-06-09 16:31:14.356 [http-nio-8080-exec-6] INFO  c.n.d.s.i.DataRetrievalServiceImpl - === 开始获取员工数据 ===
2025-06-09 16:31:14.356 [http-nio-8080-exec-6] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 查询时间范围: Sun Jul 07 00:00:00 CST 2024 至 Sun Jul 07 23:59:59 CST 2024
2025-06-09 16:31:14.357 [http-nio-8080-exec-6] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在调用远程SOAP接口获取员工数据...
2025-06-09 16:31:17.984 [http-nio-8080-exec-6] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 远程SOAP接口调用成功，响应数据长度: 522442 字符
2025-06-09 16:31:17.984 [http-nio-8080-exec-6] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在解析SOAP响应，提取XML数据...
2025-06-09 16:31:18.019 [http-nio-8080-exec-6] INFO  c.n.d.s.i.DataRetrievalServiceImpl - XML数据提取成功，数据长度: 421672 字符
2025-06-09 16:31:18.019 [http-nio-8080-exec-6] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在解析XML数据为员工DTO对象...
2025-06-09 16:31:18.043 [http-nio-8080-exec-6] INFO  c.n.d.s.i.DataRetrievalServiceImpl - ✅ 员工数据解析完成:
2025-06-09 16:31:18.044 [http-nio-8080-exec-6] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 员工主记录: 52 条 (对应 employee 表)
2025-06-09 16:31:18.044 [http-nio-8080-exec-6] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 员工岗位记录: 140 条 (对应 employee_position 表)
2025-06-09 16:31:18.044 [http-nio-8080-exec-6] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 员工职称记录: 116 条 (对应 employee_title 表)
2025-06-09 16:31:18.044 [http-nio-8080-exec-6] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 员工系统记录: 52 条 (对应 employee_system 表)
2025-06-09 16:31:18.044 [http-nio-8080-exec-6] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 总计: 360 条记录
2025-06-09 16:31:18.044 [http-nio-8080-exec-6] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 员工数据示例:
2025-06-09 16:31:18.044 [http-nio-8080-exec-6] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 员工: 张清辉 | 工号: 019359 | MDM ID: 81ee8ed6-edab-4a1d-832b-812dcaf55aee | 状态: A
2025-06-09 16:31:18.044 [http-nio-8080-exec-6] INFO  c.n.d.s.i.DataRetrievalServiceImpl -     岗位: 3 个
2025-06-09 16:31:18.044 [http-nio-8080-exec-6] INFO  c.n.d.s.i.DataRetrievalServiceImpl -     职称: 3 个
2025-06-09 16:31:18.045 [http-nio-8080-exec-6] INFO  c.n.d.s.i.DataRetrievalServiceImpl -     系统: 1 个
2025-06-09 16:31:18.045 [http-nio-8080-exec-6] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 员工: 费燕 | 工号: 001421 | MDM ID: 9db5e257-45b9-41fb-996b-c5ecf4b2d9ba | 状态: A
2025-06-09 16:31:18.045 [http-nio-8080-exec-6] INFO  c.n.d.s.i.DataRetrievalServiceImpl -     岗位: 4 个
2025-06-09 16:31:18.045 [http-nio-8080-exec-6] INFO  c.n.d.s.i.DataRetrievalServiceImpl -     职称: 2 个
2025-06-09 16:31:18.045 [http-nio-8080-exec-6] INFO  c.n.d.s.i.DataRetrievalServiceImpl -     系统: 1 个
2025-06-09 16:31:18.045 [http-nio-8080-exec-6] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 员工: 刘继宏 | 工号: 009361 | MDM ID: 23ccc025-1a43-47ac-aa49-f448e6cfa311 | 状态: A
2025-06-09 16:31:18.045 [http-nio-8080-exec-6] INFO  c.n.d.s.i.DataRetrievalServiceImpl -     岗位: 2 个
2025-06-09 16:31:18.045 [http-nio-8080-exec-6] INFO  c.n.d.s.i.DataRetrievalServiceImpl -     职称: 2 个
2025-06-09 16:31:18.045 [http-nio-8080-exec-6] INFO  c.n.d.s.i.DataRetrievalServiceImpl -     系统: 1 个
2025-06-09 16:31:18.045 [http-nio-8080-exec-6] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - ... 还有 49 个员工
2025-06-09 16:31:18.045 [http-nio-8080-exec-6] INFO  c.n.d.s.i.DataRetrievalServiceImpl - === 员工数据获取完成，总计: 52条记录 ===
2025-06-09 16:31:18.046 [http-nio-8080-exec-6] INFO  c.n.d.c.DataRetrievalController - ✅ [API响应] 员工数据获取成功:
2025-06-09 16:31:18.046 [http-nio-8080-exec-6] INFO  c.n.d.c.DataRetrievalController -    - 员工主记录: 52 条
2025-06-09 16:31:18.046 [http-nio-8080-exec-6] INFO  c.n.d.c.DataRetrievalController -    - 岗位记录: 140 条
2025-06-09 16:31:18.046 [http-nio-8080-exec-6] INFO  c.n.d.c.DataRetrievalController -    - 职称记录: 116 条
2025-06-09 16:31:18.046 [http-nio-8080-exec-6] INFO  c.n.d.c.DataRetrievalController -    - 系统记录: 52 条
2025-06-09 16:31:18.046 [http-nio-8080-exec-6] INFO  c.n.d.c.DataRetrievalController -    - 响应总记录数: 52 条
2025-06-09 16:31:22.835 [http-nio-8080-exec-9] INFO  c.n.d.c.DataRetrievalController - 🔍 [API请求] 获取员工数据 - 原始参数: startDate=2025-06-08%2016:31:22, endDate=2025-06-09%2016:31:22
2025-06-09 16:31:22.835 [http-nio-8080-exec-9] DEBUG c.n.d.utils.DateUtils - 尝试解析日期字符串: 原始=2025-06-08%2016:31:22, 解码后=2025-06-08 16:31:22
2025-06-09 16:31:22.836 [http-nio-8080-exec-9] DEBUG c.n.d.utils.DateUtils - 成功使用格式 yyyy-MM-dd HH:mm:ss 解析日期: Sun Jun 08 16:31:22 CST 2025
2025-06-09 16:31:22.837 [http-nio-8080-exec-9] DEBUG c.n.d.utils.DateUtils - 尝试解析日期字符串: 原始=2025-06-09%2016:31:22, 解码后=2025-06-09 16:31:22
2025-06-09 16:31:22.837 [http-nio-8080-exec-9] DEBUG c.n.d.utils.DateUtils - 成功使用格式 yyyy-MM-dd HH:mm:ss 解析日期: Mon Jun 09 16:31:22 CST 2025
2025-06-09 16:31:22.838 [http-nio-8080-exec-9] INFO  c.n.d.c.DataRetrievalController - 📅 [日期解析] 成功解析日期参数 - 开始时间: Sun Jun 08 16:31:22 CST 2025, 结束时间: Mon Jun 09 16:31:22 CST 2025
2025-06-09 16:31:22.838 [http-nio-8080-exec-9] INFO  c.n.d.c.DataRetrievalController - 🚀 [服务调用] 开始调用数据获取服务...
2025-06-09 16:31:22.839 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl - === 开始获取员工数据 ===
2025-06-09 16:31:22.839 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 查询时间范围: Sun Jun 08 16:31:22 CST 2025 至 Mon Jun 09 16:31:22 CST 2025
2025-06-09 16:31:22.839 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在调用远程SOAP接口获取员工数据...
2025-06-09 16:31:24.780 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 远程SOAP接口调用成功，响应数据长度: 190945 字符
2025-06-09 16:31:24.781 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在解析SOAP响应，提取XML数据...
2025-06-09 16:31:24.796 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl - XML数据提取成功，数据长度: 154159 字符
2025-06-09 16:31:24.796 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在解析XML数据为员工DTO对象...
2025-06-09 16:31:24.807 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl - ✅ 员工数据解析完成:
2025-06-09 16:31:24.807 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 员工主记录: 16 条 (对应 employee 表)
2025-06-09 16:31:24.807 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 员工岗位记录: 50 条 (对应 employee_position 表)
2025-06-09 16:31:24.807 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 员工职称记录: 46 条 (对应 employee_title 表)
2025-06-09 16:31:24.807 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 员工系统记录: 21 条 (对应 employee_system 表)
2025-06-09 16:31:24.807 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 总计: 133 条记录
2025-06-09 16:31:24.807 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 员工数据示例:
2025-06-09 16:31:24.807 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 员工: 王金生 | 工号: 007092 | MDM ID: de0f0b16-967f-4f61-b865-57ed5f85da25 | 状态: A
2025-06-09 16:31:24.807 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl -     岗位: 5 个
2025-06-09 16:31:24.807 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl -     职称: 4 个
2025-06-09 16:31:24.807 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl -     系统: 2 个
2025-06-09 16:31:24.807 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 员工: 薛宝东 | 工号: 021859 | MDM ID: 0367729b-f733-4676-ad6d-dcc08182f64a | 状态: A
2025-06-09 16:31:24.807 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl -     岗位: 4 个
2025-06-09 16:31:24.807 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl -     职称: 4 个
2025-06-09 16:31:24.807 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl -     系统: 1 个
2025-06-09 16:31:24.807 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 员工: 郑志祥 | 工号: 008098 | MDM ID: 8a4b0b08-1f0d-41ca-9b4f-b94237917d0c | 状态: A
2025-06-09 16:31:24.807 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl -     岗位: 9 个
2025-06-09 16:31:24.807 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl -     职称: 7 个
2025-06-09 16:31:24.807 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl -     系统: 3 个
2025-06-09 16:31:24.807 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - ... 还有 13 个员工
2025-06-09 16:31:24.807 [http-nio-8080-exec-9] INFO  c.n.d.s.i.DataRetrievalServiceImpl - === 员工数据获取完成，总计: 16条记录 ===
2025-06-09 16:31:24.807 [http-nio-8080-exec-9] INFO  c.n.d.c.DataRetrievalController - ✅ [API响应] 员工数据获取成功:
2025-06-09 16:31:24.808 [http-nio-8080-exec-9] INFO  c.n.d.c.DataRetrievalController -    - 员工主记录: 16 条
2025-06-09 16:31:24.808 [http-nio-8080-exec-9] INFO  c.n.d.c.DataRetrievalController -    - 岗位记录: 50 条
2025-06-09 16:31:24.808 [http-nio-8080-exec-9] INFO  c.n.d.c.DataRetrievalController -    - 职称记录: 46 条
2025-06-09 16:31:24.808 [http-nio-8080-exec-9] INFO  c.n.d.c.DataRetrievalController -    - 系统记录: 21 条
2025-06-09 16:31:24.808 [http-nio-8080-exec-9] INFO  c.n.d.c.DataRetrievalController -    - 响应总记录数: 16 条
2025-06-09 17:29:16.824 [http-nio-8080-exec-3] INFO  c.n.d.c.DataRetrievalController - 🔍 [API请求] 获取部门数据 - 原始参数: startDate=2024-07-01%2000:00:00, endDate=2024-07-31%2023:59:59
2025-06-09 17:29:16.827 [http-nio-8080-exec-3] DEBUG c.n.d.utils.DateUtils - 尝试解析日期字符串: 原始=2024-07-01%2000:00:00, 解码后=2024-07-01 00:00:00
2025-06-09 17:29:16.829 [http-nio-8080-exec-3] DEBUG c.n.d.utils.DateUtils - 成功使用格式 yyyy-MM-dd HH:mm:ss 解析日期: Mon Jul 01 00:00:00 CST 2024
2025-06-09 17:29:16.878 [http-nio-8080-exec-3] DEBUG c.n.d.utils.DateUtils - 尝试解析日期字符串: 原始=2024-07-31%2023:59:59, 解码后=2024-07-31 23:59:59
2025-06-09 17:29:16.882 [http-nio-8080-exec-3] DEBUG c.n.d.utils.DateUtils - 成功使用格式 yyyy-MM-dd HH:mm:ss 解析日期: Wed Jul 31 23:59:59 CST 2024
2025-06-09 17:29:16.883 [http-nio-8080-exec-3] INFO  c.n.d.c.DataRetrievalController - 📅 [日期解析] 成功解析日期参数 - 开始时间: Mon Jul 01 00:00:00 CST 2024, 结束时间: Wed Jul 31 23:59:59 CST 2024
2025-06-09 17:29:16.883 [http-nio-8080-exec-3] INFO  c.n.d.c.DataRetrievalController - 🚀 [服务调用] 开始调用数据获取服务...
2025-06-09 17:29:16.886 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl - === 开始获取部门数据 ===
2025-06-09 17:29:16.886 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 查询时间范围: Mon Jul 01 00:00:00 CST 2024 至 Wed Jul 31 23:59:59 CST 2024
2025-06-09 17:29:16.886 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在调用远程SOAP接口获取部门数据...
2025-06-09 17:29:18.229 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 远程SOAP接口调用成功，响应数据长度: 195206 字符
2025-06-09 17:29:18.230 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在解析SOAP响应，提取XML数据...
2025-06-09 17:29:18.267 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl - XML数据提取成功，数据长度: 150096 字符
2025-06-09 17:29:18.267 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在解析XML数据为部门DTO对象...
2025-06-09 17:29:18.291 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl - ✅ 部门数据解析完成:
2025-06-09 17:29:18.292 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 主部门记录: 69 条 (对应 department 表)
2025-06-09 17:29:18.292 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 子部门记录: 69 条 (对应 department_child 表)
2025-06-09 17:29:18.292 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 总计: 138 条记录
2025-06-09 17:29:18.292 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 部门数据示例:
2025-06-09 17:29:18.293 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 部门: 耐火五库班 | 代码: X52060007 | UUID: 390a9a2f-97ee-4e80-a8d5-493ca561c3d6 | 层级: null
2025-06-09 17:29:18.294 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 部门: 炼钢库班 | 代码: X52060011 | UUID: 2487c449-76d5-4dbc-85cc-5262c3d7d863 | 层级: null
2025-06-09 17:29:18.295 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - 部门: 带钢厂 | 代码: X64000000 | UUID: 284afdc4-06fb-481e-ac50-973e6755157b | 层级: null
2025-06-09 17:29:18.295 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl -   - ... 还有 66 个部门
2025-06-09 17:29:18.296 [http-nio-8080-exec-3] INFO  c.n.d.s.i.DataRetrievalServiceImpl - === 部门数据获取完成，总计: 69条记录 ===
2025-06-09 17:29:18.298 [http-nio-8080-exec-3] INFO  c.n.d.c.DataRetrievalController - ✅ [API响应] 部门数据获取成功:
2025-06-09 17:29:18.299 [http-nio-8080-exec-3] INFO  c.n.d.c.DataRetrievalController -    - 主部门记录: 69 条
2025-06-09 17:29:18.299 [http-nio-8080-exec-3] INFO  c.n.d.c.DataRetrievalController -    - 子部门记录: 69 条
2025-06-09 17:29:18.299 [http-nio-8080-exec-3] INFO  c.n.d.c.DataRetrievalController -    - 响应总记录数: 69 条
2025-06-09 17:29:30.692 [http-nio-8080-exec-4] INFO  c.n.d.c.DataRetrievalController - 🔍 [API请求] 获取员工数据 - 原始参数: startDate=2024-07-01%2000:00:00, endDate=2024-07-31%2023:59:59
2025-06-09 17:29:30.692 [http-nio-8080-exec-4] DEBUG c.n.d.utils.DateUtils - 尝试解析日期字符串: 原始=2024-07-01%2000:00:00, 解码后=2024-07-01 00:00:00
2025-06-09 17:29:30.693 [http-nio-8080-exec-4] DEBUG c.n.d.utils.DateUtils - 成功使用格式 yyyy-MM-dd HH:mm:ss 解析日期: Mon Jul 01 00:00:00 CST 2024
2025-06-09 17:29:30.693 [http-nio-8080-exec-4] DEBUG c.n.d.utils.DateUtils - 尝试解析日期字符串: 原始=2024-07-31%2023:59:59, 解码后=2024-07-31 23:59:59
2025-06-09 17:29:30.693 [http-nio-8080-exec-4] DEBUG c.n.d.utils.DateUtils - 成功使用格式 yyyy-MM-dd HH:mm:ss 解析日期: Wed Jul 31 23:59:59 CST 2024
2025-06-09 17:29:30.693 [http-nio-8080-exec-4] INFO  c.n.d.c.DataRetrievalController - 📅 [日期解析] 成功解析日期参数 - 开始时间: Mon Jul 01 00:00:00 CST 2024, 结束时间: Wed Jul 31 23:59:59 CST 2024
2025-06-09 17:29:30.693 [http-nio-8080-exec-4] INFO  c.n.d.c.DataRetrievalController - 🚀 [服务调用] 开始调用数据获取服务...
2025-06-09 17:29:30.693 [http-nio-8080-exec-4] INFO  c.n.d.s.i.DataRetrievalServiceImpl - === 开始获取员工数据 ===
2025-06-09 17:29:30.694 [http-nio-8080-exec-4] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 查询时间范围: Mon Jul 01 00:00:00 CST 2024 至 Wed Jul 31 23:59:59 CST 2024
2025-06-09 17:29:30.694 [http-nio-8080-exec-4] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在调用远程SOAP接口获取员工数据...
2025-06-09 17:30:28.943 [http-nio-8080-exec-4] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 远程SOAP接口调用成功，响应数据长度: 290 字符
2025-06-09 17:30:28.943 [http-nio-8080-exec-4] INFO  c.n.d.s.i.DataRetrievalServiceImpl - 正在解析SOAP响应，提取XML数据...
2025-06-09 17:30:29.026 [http-nio-8080-exec-4] WARN  c.n.d.s.i.DataRetrievalServiceImpl - ⚠️ 未获取到员工数据或数据为空
2025-06-09 17:30:29.026 [http-nio-8080-exec-4] INFO  c.n.d.s.i.DataRetrievalServiceImpl - === 员工数据获取完成，结果: 0条记录 ===
2025-06-09 17:30:29.026 [http-nio-8080-exec-4] INFO  c.n.d.c.DataRetrievalController - ✅ [API响应] 员工数据获取成功:
2025-06-09 17:30:29.026 [http-nio-8080-exec-4] INFO  c.n.d.c.DataRetrievalController -    - 员工主记录: 0 条
2025-06-09 17:30:29.026 [http-nio-8080-exec-4] INFO  c.n.d.c.DataRetrievalController -    - 岗位记录: 0 条
2025-06-09 17:30:29.026 [http-nio-8080-exec-4] INFO  c.n.d.c.DataRetrievalController -    - 职称记录: 0 条
2025-06-09 17:30:29.026 [http-nio-8080-exec-4] INFO  c.n.d.c.DataRetrievalController -    - 系统记录: 0 条
2025-06-09 17:30:29.027 [http-nio-8080-exec-4] INFO  c.n.d.c.DataRetrievalController -    - 响应总记录数: 0 条
