package com.nercar.datasynchronization.service;

import java.util.Date;

public interface DepartmentSyncService {
    
    /**
     * 同步部门数据
     *
     * @param xmlData XML数据
     */
    void syncDepartments(String xmlData);
    
    /**
     * 同步部门数据，使用指定的时间范围
     *
     * @param xmlData XML数据
     * @param startDate 开始时间
     * @param endDate 结束时间
     */
    void syncDepartments(String xmlData, Date startDate, Date endDate);
}