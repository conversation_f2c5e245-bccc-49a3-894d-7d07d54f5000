package com.nercar.datasynchronization.controller;

import com.nercar.datasynchronization.dto.ApiResponseDTO;
import com.nercar.datasynchronization.dto.DepartmentDataDTO;
import com.nercar.datasynchronization.dto.EmployeeDataDTO;
import com.nercar.datasynchronization.service.DataRetrievalService;
import com.nercar.datasynchronization.utils.DateUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/**
 * 数据获取API控制器
 * 为外部系统提供数据获取接口，作为远程数据的代理服务
 */
@Slf4j
@RestController
@RequestMapping("/api/data")
@Tag(name = "数据获取API", description = "为外部系统提供数据获取接口，从远程系统获取数据并返回解析后的结构化数据")
public class DataRetrievalController {
    
    @Autowired
    private DataRetrievalService dataRetrievalService;
    
    @GetMapping("/departments")
    @Operation(summary = "获取部门数据", description = "从远程MDM系统获取指定时间范围内的部门数据，返回解析后的结构化数据")
    public ResponseEntity<ApiResponseDTO<List<DepartmentDataDTO>>> getDepartments(
            @Parameter(description = "开始时间 (支持多种格式: yyyy-MM-dd HH:mm:ss, yyyy-MM-dd, yyyy/MM/dd HH:mm:ss等)", required = true)
            @RequestParam String startDate,
            @Parameter(description = "结束时间 (支持多种格式: yyyy-MM-dd HH:mm:ss, yyyy-MM-dd, yyyy/MM/dd HH:mm:ss等)", required = true)
            @RequestParam String endDate) {
        
        try {
            log.info("🔍 [API请求] 获取部门数据 - 原始参数: startDate={}, endDate={}", startDate, endDate);

            // 解析日期字符串
            Date parsedStartDate = DateUtils.parseDate(startDate);
            Date parsedEndDate = DateUtils.parseDate(endDate);

            log.info("📅 [日期解析] 成功解析日期参数 - 开始时间: {}, 结束时间: {}", parsedStartDate, parsedEndDate);

            // 调用服务获取部门数据
            log.info("🚀 [服务调用] 开始调用数据获取服务...");
            List<DepartmentDataDTO> departments = dataRetrievalService.getDepartmentData(parsedStartDate, parsedEndDate);

            // 统计返回数据
            long totalRecords = departments.size();
            long totalChildren = departments.stream()
                    .mapToLong(dept -> dept.getChildren() != null ? dept.getChildren().size() : 0)
                    .sum();

            // 构造成功响应
            ApiResponseDTO<List<DepartmentDataDTO>> response = ApiResponseDTO
                    .success(departments, "成功获取部门数据")
                    .withTotalCount((int) totalRecords);

            log.info("✅ [API响应] 部门数据获取成功:");
            log.info("   - 主部门记录: {} 条", totalRecords);
            log.info("   - 子部门记录: {} 条", totalChildren);
            log.info("   - 响应总记录数: {} 条", totalRecords);

            return ResponseEntity.ok(response);

        } catch (IllegalArgumentException e) {
            log.error("❌ [参数错误] 日期参数解析失败: startDate={}, endDate={}", startDate, endDate, e);
            ApiResponseDTO<List<DepartmentDataDTO>> response = ApiResponseDTO.error("日期参数格式错误: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            log.error("❌ [服务错误] 获取部门数据失败", e);
            ApiResponseDTO<List<DepartmentDataDTO>> response = ApiResponseDTO.error("获取部门数据失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    @GetMapping("/employees")
    @Operation(summary = "获取员工数据", description = "从远程MDM系统获取指定时间范围内的员工数据，返回解析后的结构化数据")
    public ResponseEntity<ApiResponseDTO<List<EmployeeDataDTO>>> getEmployees(
            @Parameter(description = "开始时间 (支持多种格式: yyyy-MM-dd HH:mm:ss, yyyy-MM-dd, yyyy/MM/dd HH:mm:ss等)", required = true)
            @RequestParam String startDate,
            @Parameter(description = "结束时间 (支持多种格式: yyyy-MM-dd HH:mm:ss, yyyy-MM-dd, yyyy/MM/dd HH:mm:ss等)", required = true)
            @RequestParam String endDate) {

        try {
            log.info("🔍 [API请求] 获取员工数据 - 原始参数: startDate={}, endDate={}", startDate, endDate);

            // 解析日期字符串
            Date parsedStartDate = DateUtils.parseDate(startDate);
            Date parsedEndDate = DateUtils.parseDate(endDate);

            log.info("📅 [日期解析] 成功解析日期参数 - 开始时间: {}, 结束时间: {}", parsedStartDate, parsedEndDate);

            // 调用服务获取员工数据
            log.info("🚀 [服务调用] 开始调用数据获取服务...");
            List<EmployeeDataDTO> employees = dataRetrievalService.getEmployeeData(parsedStartDate, parsedEndDate);

            // 统计返回数据
            long totalEmployees = employees.size();
            long totalPositions = employees.stream()
                    .mapToLong(emp -> emp.getPositions() != null ? emp.getPositions().size() : 0)
                    .sum();
            long totalTitles = employees.stream()
                    .mapToLong(emp -> emp.getTitles() != null ? emp.getTitles().size() : 0)
                    .sum();
            long totalSystems = employees.stream()
                    .mapToLong(emp -> emp.getSystems() != null ? emp.getSystems().size() : 0)
                    .sum();

            // 构造成功响应
            ApiResponseDTO<List<EmployeeDataDTO>> response = ApiResponseDTO
                    .success(employees, "成功获取员工数据")
                    .withTotalCount((int) totalEmployees);

            log.info("✅ [API响应] 员工数据获取成功:");
            log.info("   - 员工主记录: {} 条", totalEmployees);
            log.info("   - 岗位记录: {} 条", totalPositions);
            log.info("   - 职称记录: {} 条", totalTitles);
            log.info("   - 系统记录: {} 条", totalSystems);
            log.info("   - 响应总记录数: {} 条", totalEmployees);

            return ResponseEntity.ok(response);

        } catch (IllegalArgumentException e) {
            log.error("❌ [参数错误] 日期参数解析失败: startDate={}, endDate={}", startDate, endDate, e);
            ApiResponseDTO<List<EmployeeDataDTO>> response = ApiResponseDTO.error("日期参数格式错误: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            log.error("❌ [服务错误] 获取员工数据失败", e);
            ApiResponseDTO<List<EmployeeDataDTO>> response = ApiResponseDTO.error("获取员工数据失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    @GetMapping("/test")
    @Operation(summary = "测试数据获取API", description = "测试数据获取API是否正常工作")
    public ResponseEntity<ApiResponseDTO<String>> test() {
        try {
            log.info("接收到数据获取API测试请求");
            
            ApiResponseDTO<String> response = ApiResponseDTO.success(
                "数据获取API正常工作", 
                "API测试成功"
            );
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("数据获取API测试失败", e);
            ApiResponseDTO<String> response = ApiResponseDTO.error("API测试失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
}
