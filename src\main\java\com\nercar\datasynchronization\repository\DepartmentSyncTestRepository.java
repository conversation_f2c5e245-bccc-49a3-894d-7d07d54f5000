package com.nercar.datasynchronization.repository;

import com.nercar.datasynchronization.entity.DepartmentSyncTest;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * 部门同步测试数据访问层
 */
@Repository
public interface DepartmentSyncTestRepository extends JpaRepository<DepartmentSyncTest, Long> {

    /**
     * 根据组织编码查找部门
     */
    List<DepartmentSyncTest> findByOrgCode(String orgCode);

    /**
     * 根据组织编码查找单个部门（期望唯一）
     */
    Optional<DepartmentSyncTest> findFirstByOrgCode(String orgCode);

    /**
     * 根据父级编码查找子部门
     */
    List<DepartmentSyncTest> findByParentCode(String parentCode);

    /**
     * 查找特殊组织编码的部门（如根节点 org_code="X"）
     */
    List<DepartmentSyncTest> findByOrgCodeIn(List<String> orgCodes);

    /**
     * 查找父子关系异常的部门
     * 父级编码不为空但在系统中找不到对应的父级部门
     */
    @Query("SELECT d FROM DepartmentSyncTest d WHERE d.parentCode IS NOT NULL " +
           "AND d.parentCode != '' AND d.parentCode NOT IN " +
           "(SELECT d2.orgCode FROM DepartmentSyncTest d2)")
    List<DepartmentSyncTest> findDepartmentsWithBrokenRelations();

    /**
     * 根据同步日期查找部门
     */
    List<DepartmentSyncTest> findBySyncDate(LocalDate syncDate);

    /**
     * 统计总数
     */
    long count();

    /**
     * 统计正常状态的部门数量
     */
    long countByIsHistory(Integer isHistory);

    /**
     * 统计根据操作标识的部门数量
     */
    long countByUserPredef14(String userPredef14);

    /**
     * 查找根部门（parent_code为空或为特定值的部门）
     */
    @Query("SELECT d FROM DepartmentSyncTest d WHERE d.parentCode IS NULL OR d.parentCode = '' OR d.parentCode = '1'")
    List<DepartmentSyncTest> findRootDepartments();

    /**
     * 删除指定组织编码的部门
     */
    void deleteByOrgCode(String orgCode);

    /**
     * 批量删除指定组织编码的部门
     */
    void deleteByOrgCodeIn(List<String> orgCodes);

    /**
     * 检查组织编码是否存在
     */
    boolean existsByOrgCode(String orgCode);
}
