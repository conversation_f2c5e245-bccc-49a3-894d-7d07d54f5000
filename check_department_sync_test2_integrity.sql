-- =====================================================
-- 检查department_sync_test2.sql中的部门层级关系完整性
-- =====================================================

-- 1. 基础统计信息
SELECT 
    '总记录数' as 统计项,
    COUNT(*) as 数量
FROM department_sync_test
UNION ALL
SELECT 
    '正常状态记录数' as 统计项,
    COUNT(*) as 数量
FROM department_sync_test 
WHERE is_history = 0 AND user_predef_14 != 'D'
UNION ALL
SELECT 
    '历史记录数' as 统计项,
    COUNT(*) as 数量
FROM department_sync_test 
WHERE is_history = 1
UNION ALL
SELECT 
    '删除状态记录数' as 统计项,
    COUNT(*) as 数量
FROM department_sync_test 
WHERE user_predef_14 = 'D';

-- 2. 检查是否还有parent_code='1'的错误情况
SELECT 
    '仍有parent_code=1的部门数量' as 检查项,
    COUNT(*) as 数量
FROM department_sync_test 
WHERE is_history = 0 
AND user_predef_14 != 'D'
AND parent_code = '1';

-- 3. 显示所有parent_code='1'的部门详情
SELECT 
    org_code,
    org_name,
    parent_code,
    full_name
FROM department_sync_test 
WHERE is_history = 0 
AND user_predef_14 != 'D'
AND parent_code = '1'
ORDER BY org_code;

-- 4. 检查孤儿节点（最关键的检查）
-- 找出parent_code不为空但在系统中找不到对应父节点的部门
SELECT 
    '孤儿节点数量' as 检查项,
    COUNT(*) as 问题数量
FROM department_sync_test d1 
WHERE d1.is_history = 0 
AND d1.user_predef_14 != 'D'
AND d1.parent_code IS NOT NULL 
AND d1.parent_code != '' 
AND d1.parent_code != '1'  -- 排除根节点标识符
AND d1.parent_code NOT IN (
    SELECT d2.org_code 
    FROM department_sync_test d2 
    WHERE d2.is_history = 0 AND d2.user_predef_14 != 'D'
);

-- 5. 显示孤儿节点详情
SELECT 
    d1.org_code,
    d1.org_name,
    d1.parent_code as 找不到的父级编码,
    d1.full_name
FROM department_sync_test d1 
WHERE d1.is_history = 0 
AND d1.user_predef_14 != 'D'
AND d1.parent_code IS NOT NULL 
AND d1.parent_code != '' 
AND d1.parent_code != '1'
AND d1.parent_code NOT IN (
    SELECT d2.org_code 
    FROM department_sync_test d2 
    WHERE d2.is_history = 0 AND d2.user_predef_14 != 'D'
)
ORDER BY d1.parent_code, d1.org_code;

-- 6. 检查根节点情况
SELECT 
    '根节点数量' as 检查项,
    COUNT(*) as 数量
FROM department_sync_test 
WHERE is_history = 0 
AND user_predef_14 != 'D'
AND (parent_code IS NULL OR parent_code = '' OR parent_code = '1');

-- 7. 显示根节点详情
SELECT 
    org_code,
    org_name,
    parent_code,
    full_name
FROM department_sync_test 
WHERE is_history = 0 
AND user_predef_14 != 'D'
AND (parent_code IS NULL OR parent_code = '' OR parent_code = '1')
ORDER BY org_code;

-- 8. 检查是否有循环引用
SELECT 
    '循环引用数量' as 检查项,
    COUNT(*) as 问题数量
FROM department_sync_test 
WHERE is_history = 0 
AND user_predef_14 != 'D'
AND org_code = parent_code;

-- 9. 父级编码分布统计（前20名）
SELECT 
    parent_code as 父级编码,
    COUNT(*) as 子部门数量,
    GROUP_CONCAT(org_name SEPARATOR ', ') as 子部门示例
FROM department_sync_test 
WHERE is_history = 0 AND user_predef_14 != 'D'
GROUP BY parent_code 
ORDER BY COUNT(*) DESC
LIMIT 20;

-- 10. 检查特殊parent_code值的分布
SELECT 
    CASE 
        WHEN parent_code IS NULL THEN 'NULL'
        WHEN parent_code = '' THEN '空字符串'
        WHEN parent_code = '1' THEN '根节点标识符(1)'
        WHEN parent_code LIKE 'X%' THEN 'X开头编码'
        WHEN parent_code LIKE 'XB%' THEN 'XB开头编码'
        WHEN parent_code LIKE 'XC%' THEN 'XC开头编码'
        ELSE '其他编码'
    END as 父级编码类型,
    COUNT(*) as 数量
FROM department_sync_test 
WHERE is_history = 0 AND user_predef_14 != 'D'
GROUP BY 
    CASE 
        WHEN parent_code IS NULL THEN 'NULL'
        WHEN parent_code = '' THEN '空字符串'
        WHEN parent_code = '1' THEN '根节点标识符(1)'
        WHEN parent_code LIKE 'X%' THEN 'X开头编码'
        WHEN parent_code LIKE 'XB%' THEN 'XB开头编码'
        WHEN parent_code LIKE 'XC%' THEN 'XC开头编码'
        ELSE '其他编码'
    END
ORDER BY COUNT(*) DESC;

-- 11. 层级深度分析
WITH RECURSIVE dept_hierarchy AS (
    -- 根节点
    SELECT 
        org_code,
        org_name,
        parent_code,
        0 as level,
        CAST(org_name AS CHAR(1000)) as path
    FROM department_sync_test 
    WHERE is_history = 0 
    AND user_predef_14 != 'D'
    AND (parent_code IS NULL OR parent_code = '' OR parent_code = '1')
    
    UNION ALL
    
    -- 递归查找子节点
    SELECT 
        d.org_code,
        d.org_name,
        d.parent_code,
        h.level + 1,
        CONCAT(h.path, ' -> ', d.org_name)
    FROM department_sync_test d
    JOIN dept_hierarchy h ON d.parent_code = h.org_code
    WHERE d.is_history = 0 
    AND d.user_predef_14 != 'D'
    AND h.level < 10  -- 防止无限递归
)
SELECT 
    level as 层级,
    COUNT(*) as 部门数量
FROM dept_hierarchy
GROUP BY level
ORDER BY level;

-- 12. 检查可能的命名不一致问题
SELECT 
    '可能需要名称标准化的部门' as 检查项,
    COUNT(*) as 数量
FROM department_sync_test d
JOIN department_sync_test p ON d.parent_code = p.org_code
WHERE d.is_history = 0 
AND d.user_predef_14 != 'D'
AND p.is_history = 0 
AND p.user_predef_14 != 'D'
AND d.org_name LIKE CONCAT(p.org_name, '%')
AND LENGTH(d.org_name) > LENGTH(p.org_name) + 2;

-- 13. 显示需要名称标准化的部门示例
SELECT 
    d.org_code,
    d.org_name as 当前名称,
    p.org_name as 上级部门,
    TRIM(SUBSTRING(d.org_name, LENGTH(p.org_name) + 1)) as 建议简化名称
FROM department_sync_test d
JOIN department_sync_test p ON d.parent_code = p.org_code
WHERE d.is_history = 0 
AND d.user_predef_14 != 'D'
AND p.is_history = 0 
AND p.user_predef_14 != 'D'
AND d.org_name LIKE CONCAT(p.org_name, '%')
AND LENGTH(d.org_name) > LENGTH(p.org_name) + 2
ORDER BY d.org_code
LIMIT 20;
