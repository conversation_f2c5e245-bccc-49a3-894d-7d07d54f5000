package com.nercar.datasynchronization.service.impl;

import com.nercar.datasynchronization.client.SoapClient;
import com.nercar.datasynchronization.service.DepartmentSyncService;
import com.nercar.datasynchronization.service.EmployeeSyncService;
import com.nercar.datasynchronization.utils.XmlUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Service
public class AsyncSyncService {

    @Autowired
    private SoapClient soapClient;
    
    @Autowired
    private DepartmentSyncService departmentSyncService;
    
    @Autowired
    private EmployeeSyncService employeeSyncService;
    
    /**
     * 异步同步部门数据
     */
    @Async
    public CompletableFuture<String> syncDepartmentsAsync(Date startDate, Date endDate) {
        try {
            log.info("开始异步同步部门数据，开始时间: {}, 结束时间: {}", startDate, endDate);
            
            // 调用SOAP接口获取部门数据
            String orgXmlResponse = soapClient.getOrgInfo(startDate, endDate);
            
            // 解析SOAP响应，提取XML数据
            String orgXmlData = XmlUtils.extractXmlData(orgXmlResponse, "GetOrgInfoFromMDMResult");
            
            if (orgXmlData != null && !orgXmlData.isEmpty()) {
                // 同步部门数据
                departmentSyncService.syncDepartments(orgXmlData);
                log.info("部门数据异步同步完成");
                return CompletableFuture.completedFuture("部门数据同步成功");
            } else {
                log.warn("未获取到部门数据或数据为空");
                return CompletableFuture.completedFuture("未获取到部门数据或数据为空");
            }
        } catch (Exception e) {
            log.error("异步同步部门数据失败", e);
            return CompletableFuture.failedFuture(e);
        }
    }
    
    /**
     * 异步同步员工数据
     */
    @Async
    public CompletableFuture<String> syncEmployeesAsync(Date startDate, Date endDate) {
        try {
            log.info("开始异步同步员工数据，开始时间: {}, 结束时间: {}", startDate, endDate);
            
            // 调用SOAP接口获取员工数据
            String userXmlResponse = soapClient.getUserInfo(startDate, endDate);
            
            // 解析SOAP响应，提取XML数据
            String userXmlData = XmlUtils.extractXmlData(userXmlResponse, "GetUserInfoFromMDMResult");
            
            if (userXmlData != null && !userXmlData.isEmpty()) {
                // 同步员工数据
//                employeeSyncService.syncEmployees(userXmlData);
                // 修改后 - 传递时间范围:
                employeeSyncService.syncEmployees(userXmlData, startDate, endDate);

                log.info("员工数据异步同步完成");
                return CompletableFuture.completedFuture("员工数据同步成功");
            } else {
                log.warn("未获取到员工数据或数据为空");
                return CompletableFuture.completedFuture("未获取到员工数据或数据为空");
            }
        } catch (Exception e) {
            log.error("异步同步员工数据失败", e);
            return CompletableFuture.failedFuture(e);
        }
    }
}