package com.nercar.datasynchronization.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/health")
@Tag(name = "系统健康检查", description = "提供系统健康状态接口")
public class HealthController {

    @GetMapping
    @Operation(summary = "健康检查", description = "检查系统是否正常运行")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "UP");
        result.put("service", "data-synchronization");
        result.put("version", "1.0.0");
        
        return ResponseEntity.ok(result);
    }
}