package com.nercar.datasynchronization.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.util.Date;

/**
 * 员工-部门关联实体 - 存储员工与部门的隶属关系
 */
@Data
@Entity
@Table(name = "employee_department", 
       indexes = {
           @Index(name = "idx_emp_dept_emp_code", columnList = "employee_code"),
           @Index(name = "idx_emp_dept_dept_code", columnList = "department_code")
       },
       uniqueConstraints = {
           @UniqueConstraint(name = "uk_emp_dept_rel", 
                            columnNames = {"employee_code", "department_code"})
       })
@DynamicInsert
@DynamicUpdate
public class EmployeeDepartment {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "employee_code", nullable = false)
    private String employeeCode;
    
    @Column(name = "employee_name")
    private String employeeName;
    
    @Column(name = "department_id", nullable = false)
    private Integer departmentId;
    
    @Column(name = "department_code", nullable = false)
    private String departmentCode;
    
    @Column(name = "department_name")
    private String departmentName;
    
    @Column(name = "relationship_type")
    private String relationshipType;
    
    @Column(name = "is_active")
    private Boolean isActive;
    
    @Column(name = "teams_groups")
    private String teamsGroups;
    
    @Column(name = "created_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdTime;
    
    @Column(name = "updated_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedTime;
}