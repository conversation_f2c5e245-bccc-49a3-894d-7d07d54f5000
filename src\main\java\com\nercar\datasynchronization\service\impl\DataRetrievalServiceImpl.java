package com.nercar.datasynchronization.service.impl;

import com.nercar.datasynchronization.client.SoapClient;
import com.nercar.datasynchronization.config.RetryConfig;
import com.nercar.datasynchronization.dto.*;
import com.nercar.datasynchronization.service.DataRetrievalService;
import com.nercar.datasynchronization.utils.RetryUtils;
import com.nercar.datasynchronization.utils.TimeSliceUtils;
import com.nercar.datasynchronization.utils.XmlUtils;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.Element;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 数据获取服务实现类
 * 负责从远程系统获取数据并转换为DTO对象，不涉及数据库操作
 */
@Slf4j
@Service
public class DataRetrievalServiceImpl implements DataRetrievalService {

    @Autowired
    private SoapClient soapClient;

    @Autowired
    private RetryConfig retryConfig;

    // 日期格式化器，用于解析XML中的日期
    private static final ThreadLocal<SimpleDateFormat> DATE_FORMAT =
            ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy/MM/dd HH:mm:ss"));

    @Override
    public List<DepartmentDataDTO> getDepartmentData(Date startDate, Date endDate) {
        log.info("=== 开始获取部门数据 ===");
        log.info("查询时间范围: {} 至 {}", startDate, endDate);

        // 部门数据不使用分片处理，直接单次请求
        long totalHours = TimeSliceUtils.calculateTotalHours(startDate, endDate);
        log.info("📊 [处理策略] 部门数据使用单次请求模式，时间范围: {}小时", totalHours);

        try {
            return getSingleDepartmentData(startDate, endDate);
        } catch (Exception e) {
            log.error("❌ 获取部门数据失败", e);
            log.info("=== 部门数据获取失败 ===");
            throw new RuntimeException("获取部门数据失败: " + e.getMessage(), e);
        }
    }



    /**
     * 获取单个时间段的部门数据
     */
    private List<DepartmentDataDTO> getSingleDepartmentData(Date startDate, Date endDate) throws Exception {
        // 使用重试机制调用SOAP接口获取部门数据
        log.info("正在调用远程SOAP接口获取部门数据...");

        String orgXmlData;
        if (retryConfig.isEnabled()) {
            orgXmlData = RetryUtils.executeWithRetry(() -> {
                String response = soapClient.getOrgInfo(startDate, endDate);
                log.info("远程SOAP接口调用成功，响应数据长度: {} 字符", response != null ? response.length() : 0);

                // 解析SOAP响应，提取XML数据
                log.info("正在解析SOAP响应，提取XML数据...");
                return XmlUtils.extractXmlData(response, "GetOrgInfoFromMDMResult");
            }, retryConfig.getMaxRetries(), retryConfig.getRetryDelay(), "获取部门数据");
        } else {
            String response = soapClient.getOrgInfo(startDate, endDate);
            log.info("远程SOAP接口调用成功，响应数据长度: {} 字符", response != null ? response.length() : 0);

            // 解析SOAP响应，提取XML数据
            log.info("正在解析SOAP响应，提取XML数据...");
            orgXmlData = XmlUtils.extractXmlData(response, "GetOrgInfoFromMDMResult");
        }

        if (orgXmlData == null || orgXmlData.isEmpty()) {
            log.warn("⚠️ 未获取到部门数据或数据为空");
            return new ArrayList<>();
        }

        log.info("XML数据提取成功，数据长度: {} 字符", orgXmlData.length());

        // 解析XML数据为DTO对象
        List<DepartmentDataDTO> departments = new ArrayList<>();

        log.info("正在解析XML数据为部门DTO对象...");
        // 使用SAX解析器流式处理XML数据
        XmlUtils.parseXmlWithSAX(orgXmlData, "O_DATA",
            element -> {
                DepartmentDataDTO department = parseDepartmentElement(element);
                if (department != null) {
                    departments.add(department);
                    log.debug("解析部门: {} ({})", department.getOrgName(), department.getOrgCode());
                }
            });

        // 统计详细信息
        long mainDepartments = departments.size();
        long totalChildren = departments.stream()
                .mapToLong(dept -> dept.getChildren() != null ? dept.getChildren().size() : 0)
                .sum();

        log.info("✅ 部门数据解析完成:");
        log.info("  - 主部门记录: {} 条 (对应 department 表)", mainDepartments);
        log.info("  - 子部门记录: {} 条 (对应 department_child 表)", totalChildren);
        log.info("  - 总计: {} 条记录", mainDepartments + totalChildren);

        // 记录部分部门信息用于调试
        if (!departments.isEmpty()) {
            log.info("部门数据示例:");
            departments.stream().limit(3).forEach(dept -> {
                log.info("  - 部门: {} | 代码: {} | UUID: {} | 层级: {}",
                        dept.getOrgName(), dept.getOrgCode(), dept.getDeptUuid(), dept.getOrgLevel());
            });
            if (departments.size() > 3) {
                log.info("  - ... 还有 {} 个部门", departments.size() - 3);
            }
        }

        return departments;
    }

    @Override
    public List<EmployeeDataDTO> getEmployeeData(Date startDate, Date endDate) {
        log.info("=== 开始获取员工数据 ===");
        log.info("查询时间范围: {} 至 {}", startDate, endDate);

        // 检查是否需要分片处理（员工数据更容易超时，使用更小的阈值）
        long totalHours = TimeSliceUtils.calculateTotalHours(startDate, endDate);
        if (totalHours > 24) { // 超过1天就分片
            log.info("🔪 [分片处理] 时间范围较大({}小时)，启用分片处理", totalHours);
            return getEmployeeDataWithSlicing(startDate, endDate);
        }

        try {
            return getSingleEmployeeData(startDate, endDate);
        } catch (Exception e) {
            log.error("❌ 获取员工数据失败", e);
            log.info("=== 员工数据获取失败 ===");
            throw new RuntimeException("获取员工数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 分片获取员工数据
     */
    private List<EmployeeDataDTO> getEmployeeDataWithSlicing(Date startDate, Date endDate) {
        log.info("🔪 [分片模式] 开始分片获取员工数据");

        // 智能分片（员工数据使用更小的分片）
        List<TimeSliceUtils.TimeSlice> slices = TimeSliceUtils.sliceTimeRange(startDate, endDate, 12); // 12小时分片
        List<EmployeeDataDTO> allEmployees = new ArrayList<>();

        int successCount = 0;
        int failCount = 0;

        for (TimeSliceUtils.TimeSlice slice : slices) {
            try {
                log.info("🔄 [分片处理] 正在处理 {}", slice);

                List<EmployeeDataDTO> sliceEmployees = getSingleEmployeeData(slice.getStartDate(), slice.getEndDate());
                allEmployees.addAll(sliceEmployees);
                successCount++;

                log.info("✅ [分片成功] {} 完成，获取{}条记录", slice, sliceEmployees.size());

                // 分片间隔，避免对远程服务造成压力
                if (slice.getSliceIndex() < slice.getTotalSlices()) {
                    Thread.sleep(2000); // 2秒间隔，员工数据处理间隔更长
                }

            } catch (Exception e) {
                failCount++;

                // 检查是否是重试耗尽的异常
                if (e.getMessage() != null && e.getMessage().contains("次重试后仍然失败")) {
                    log.error("❌ [分片重试耗尽] {} 在重试后仍然失败: {}", slice, e.getMessage());

                    // 显示重试详情
                    if (e.getCause() != null) {
                        log.error("   └─ 最后一次失败原因: {}", e.getCause().getMessage());
                    }
                } else {
                    log.error("❌ [分片失败] {} 处理失败: {}", slice, e.getMessage());
                }

                // 记录失败详情
                log.warn("⚠️ [分片统计] 当前失败率: {}/{} ({:.1f}%)",
                        failCount, slice.getSliceIndex(), (double)failCount / slice.getSliceIndex() * 100);

                // 如果失败率过高，停止处理
                if (failCount > slices.size() / 2) {
                    log.error("💥 [分片终止] 失败率过高({:.1f}%)，停止处理", (double)failCount / slice.getSliceIndex() * 100);
                    throw new RuntimeException(String.format("分片处理失败率过高(%d/%d)，停止处理", failCount, slice.getSliceIndex()), e);
                }

                // 连续失败检查（员工数据更严格）
                if (failCount >= 2 && slice.getSliceIndex() - successCount >= 2) {
                    log.error("💥 [连续失败] 连续2个分片失败，停止处理（员工数据）");
                    throw new RuntimeException("连续分片失败，可能远程服务不可用", e);
                }

                // 单个分片重试失败后，给出建议
                if (e.getMessage() != null && e.getMessage().contains("次重试后仍然失败")) {
                    log.info("💡 [处理建议] 可以尝试缩小时间范围或稍后重试");
                }
            }
        }

        // 统计结果
        long totalEmployees = allEmployees.size();
        long totalPositions = allEmployees.stream()
                .mapToLong(emp -> emp.getPositions() != null ? emp.getPositions().size() : 0)
                .sum();
        long totalTitles = allEmployees.stream()
                .mapToLong(emp -> emp.getTitles() != null ? emp.getTitles().size() : 0)
                .sum();
        long totalSystems = allEmployees.stream()
                .mapToLong(emp -> emp.getSystems() != null ? emp.getSystems().size() : 0)
                .sum();

        log.info("🎯 [分片完成] 员工数据分片处理完成:");
        log.info("  - 成功片段: {}/{}", successCount, slices.size());
        log.info("  - 失败片段: {}", failCount);
        log.info("  - 员工主记录: {} 条", totalEmployees);
        log.info("  - 岗位记录: {} 条", totalPositions);
        log.info("  - 职称记录: {} 条", totalTitles);
        log.info("  - 系统记录: {} 条", totalSystems);
        log.info("=== 员工数据获取完成，总计: {}条记录 ===", totalEmployees);

        return allEmployees;
    }

    /**
     * 获取单个时间段的员工数据
     */
    private List<EmployeeDataDTO> getSingleEmployeeData(Date startDate, Date endDate) throws Exception {
        // 使用重试机制调用SOAP接口获取员工数据
        log.info("正在调用远程SOAP接口获取员工数据...");

        String userXmlData;
        if (retryConfig.isEnabled()) {
            userXmlData = RetryUtils.executeWithRetry(() -> {
                String response = soapClient.getUserInfo(startDate, endDate);
                log.info("远程SOAP接口调用成功，响应数据长度: {} 字符", response != null ? response.length() : 0);

                // 解析SOAP响应，提取XML数据
                log.info("正在解析SOAP响应，提取XML数据...");
                return XmlUtils.extractXmlData(response, "GetUserInfoFromMDMResult");
            }, retryConfig.getMaxRetries(), retryConfig.getRetryDelay(), "获取员工数据");
        } else {
            String response = soapClient.getUserInfo(startDate, endDate);
            log.info("远程SOAP接口调用成功，响应数据长度: {} 字符", response != null ? response.length() : 0);

            // 解析SOAP响应，提取XML数据
            log.info("正在解析SOAP响应，提取XML数据...");
            userXmlData = XmlUtils.extractXmlData(response, "GetUserInfoFromMDMResult");
        }

        if (userXmlData == null || userXmlData.isEmpty()) {
            log.warn("⚠️ 未获取到员工数据或数据为空");
            return new ArrayList<>();
        }

        log.info("XML数据提取成功，数据长度: {} 字符", userXmlData.length());

        // 解析XML数据为DTO对象
        List<EmployeeDataDTO> employees = new ArrayList<>();

        log.info("正在解析XML数据为员工DTO对象...");
        // 使用SAX解析器流式处理XML数据
        XmlUtils.parseXmlWithSAX(userXmlData, "O_DATA",
            element -> {
                EmployeeDataDTO employee = parseEmployeeElement(element);
                if (employee != null) {
                    employees.add(employee);
                    log.debug("解析员工: {} ({})", employee.getEmployeeName(), employee.getEmployeeCode());
                }
            });

        // 统计详细信息
        long mainEmployees = employees.size();
        long totalPositions = employees.stream()
                .mapToLong(emp -> emp.getPositions() != null ? emp.getPositions().size() : 0)
                .sum();
        long totalTitles = employees.stream()
                .mapToLong(emp -> emp.getTitles() != null ? emp.getTitles().size() : 0)
                .sum();
        long totalSystems = employees.stream()
                .mapToLong(emp -> emp.getSystems() != null ? emp.getSystems().size() : 0)
                .sum();

        log.info("✅ 员工数据解析完成:");
        log.info("  - 员工主记录: {} 条 (对应 employee 表)", mainEmployees);
        log.info("  - 员工岗位记录: {} 条 (对应 employee_position 表)", totalPositions);
        log.info("  - 员工职称记录: {} 条 (对应 employee_title 表)", totalTitles);
        log.info("  - 员工系统记录: {} 条 (对应 employee_system 表)", totalSystems);
        log.info("  - 总计: {} 条记录", mainEmployees + totalPositions + totalTitles + totalSystems);

        // 记录部分员工信息用于调试
        if (!employees.isEmpty()) {
            log.info("员工数据示例:");
            employees.stream().limit(3).forEach(emp -> {
                log.info("  - 员工: {} | 工号: {} | MDM ID: {} | 状态: {}",
                        emp.getEmployeeName(), emp.getEmployeeCode(), emp.getMdmId(), emp.getStatus());
                if (emp.getPositions() != null && !emp.getPositions().isEmpty()) {
                    log.info("    岗位: {} 个", emp.getPositions().size());
                }
                if (emp.getTitles() != null && !emp.getTitles().isEmpty()) {
                    log.info("    职称: {} 个", emp.getTitles().size());
                }
                if (emp.getSystems() != null && !emp.getSystems().isEmpty()) {
                    log.info("    系统: {} 个", emp.getSystems().size());
                }
            });
            if (employees.size() > 3) {
                log.info("  - ... 还有 {} 个员工", employees.size() - 3);
            }
        }

        return employees;
    }

    /**
     * 解析部门XML元素为DTO对象
     * 根据原有同步代码的逻辑进行解析
     *
     * @param dataElement XML元素
     * @return 部门DTO对象
     */
    private DepartmentDataDTO parseDepartmentElement(Element dataElement) {
        try {
            // 获取MDM ID（部门UUID），与原有代码逻辑一致
            String deptUuid = XmlUtils.getElementText(dataElement, "NM");
            if (deptUuid == null || deptUuid.isEmpty()) {
                log.warn("部门MDM ID为空，跳过处理");
                return null;
            }

            DepartmentDataDTO department = new DepartmentDataDTO();

            // 解析基本字段
            String orgCode = XmlUtils.getElementText(dataElement, "ORGCODE");
            String orgName = XmlUtils.getElementText(dataElement, "ORGNAME");

            if (orgCode == null || orgCode.isEmpty()) {
                log.warn("组织编码为空，跳过处理，部门ID: {}", deptUuid);
                return null;
            }

            department.setDeptUuid(deptUuid);  // 设置部门UUID
            department.setOrgCode(orgCode);
            department.setOrgName(orgName);
            department.setParentCode(XmlUtils.getElementText(dataElement, "PNODECODE"));
            department.setFullName(XmlUtils.getElementText(dataElement, "ORAALLNAME"));

            // 解析是否停用状态
            String isHistoryStr = XmlUtils.getElementText(dataElement, "ISHISTORY");
            if (isHistoryStr != null && !isHistoryStr.isEmpty()) {
                try {
                    department.setIsHistory(Integer.parseInt(isHistoryStr));
                } catch (NumberFormatException e) {
                    log.warn("部门 {} 的历史状态解析失败: {}, 使用默认值0", orgCode, isHistoryStr);
                    department.setIsHistory(0); // 默认值为启用
                }
            } else {
                department.setIsHistory(0); // 默认值为启用
            }

            // 解析其他字段
            department.setDescription(XmlUtils.getElementText(dataElement, "ORGDESC"));
            department.setFax(XmlUtils.getElementText(dataElement, "FAX"));
            department.setWebAddress(XmlUtils.getElementText(dataElement, "WEBADDR"));
            department.setOrgManager(XmlUtils.getElementText(dataElement, "ORGFRDB"));
            department.setPostCode(XmlUtils.getElementText(dataElement, "POSTCODE"));
            department.setUserPredef13(XmlUtils.getElementText(dataElement, "USERPREDEF_13"));
            department.setUserPredef14(XmlUtils.getElementText(dataElement, "USERPREDEF_14"));
            department.setUserPredef18(XmlUtils.getElementText(dataElement, "USERPREDEF_18"));

            // 添加新字段处理
            department.setParentNodeId(XmlUtils.getElementText(dataElement, "PNODEID"));  // 父级节点ID
            department.setOrgTypeCode(XmlUtils.getElementText(dataElement, "USERPREDEF_11"));  // 组织类型编码
            department.setBudgetCurrency(XmlUtils.getElementText(dataElement, "USERPREDEF_12"));  // 预算币种编码
            department.setUserPredef10(XmlUtils.getElementText(dataElement, "USERPREDEF_10"));  // 预留字段10
            department.setUserPredef17(XmlUtils.getElementText(dataElement, "USERPREDEF_17"));  // 预留字段17
            department.setIsLegalEntity(XmlUtils.getElementText(dataElement, "USERPREDEF_26"));  // 是否企业法人
            department.setRemarks(XmlUtils.getElementText(dataElement, "REMARKS"));  // 备注

            // 解析子部门信息 - 修正：应该使用O_CHILDS1，与原有同步代码保持一致
            Element children = dataElement.element("O_CHILDS1");
            List<DepartmentChildDTO> childList = new ArrayList<>();
            if (children != null) {
                List<Element> childElements = children.elements("O_CHILD");
                log.debug("部门 {} 包含 {} 个子记录", orgCode, childElements.size());
                for (Element childElement : childElements) {
                    DepartmentChildDTO child = parseDepartmentChildElement(childElement, deptUuid);  // 传递正确的deptUuid
                    if (child != null) {
                        childList.add(child);
                    }
                }
            }
            department.setChildren(childList);

            log.trace("成功解析部门: {} | 代码: {} | UUID: {} | 子记录: {} 个", orgName, orgCode, deptUuid, childList.size());
            return department;

        } catch (Exception e) {
            log.error("解析部门数据失败", e);
            return null;
        }
    }

    /**
     * 解析部门子表XML元素为DTO对象
     * 根据原有同步代码的逻辑进行解析
     *
     * @param childElement XML元素
     * @param deptUuid 部门UUID
     * @return 部门子表DTO对象
     */
    private DepartmentChildDTO parseDepartmentChildElement(Element childElement, String deptUuid) {
        try {
            // 检查GUID是否存在，与原有代码逻辑一致
            String guid = XmlUtils.getElementText(childElement, "NGTYYSB_GUID");
            if (guid == null || guid.isEmpty()) {
                log.debug("部门子表记录GUID为空，跳过处理");
                return null;
            }

            DepartmentChildDTO child = new DepartmentChildDTO();

            // 根据原有同步代码的字段映射进行解析
            child.setGuid(guid);
            child.setDeptUuid(deptUuid);  // 使用传入的部门UUID
            child.setSourceSystem(XmlUtils.getElementText(childElement, "NGTYYSB_LYXTBH"));
            child.setSourceDataNm(XmlUtils.getElementText(childElement, "NGTYYSB_LYXTDATANM"));
            child.setUdef1(XmlUtils.getElementText(childElement, "NGTYYSB_UDEF1"));
            child.setUdef2(XmlUtils.getElementText(childElement, "NGTYYSB_UDEF2"));
            child.setUdef3(XmlUtils.getElementText(childElement, "NGTYYSB_UDEF3"));
            child.setUdef4(XmlUtils.getElementText(childElement, "NGTYYSB_UDEF4"));
            child.setUdef5(XmlUtils.getElementText(childElement, "NGTYYSB_UDEF5"));
            child.setUdef6(XmlUtils.getElementText(childElement, "NGTYYSB_UDEF6"));

            // 添加新字段处理
            child.setDeptMdmId(XmlUtils.getElementText(childElement, "NGTYYSB_ZBNM"));  // 部门MDM关联ID
            child.setUdef7(XmlUtils.getElementText(childElement, "NGTYYSB_UDEF7"));  // 默认成本中心描述
            child.setUdef8(XmlUtils.getElementText(childElement, "NGTYYSB_UDEF8"));  // 用于审批组织编号
            child.setUdef9(XmlUtils.getElementText(childElement, "NGTYYSB_UDEF9"));  // 备用字段1
            child.setUdef10(XmlUtils.getElementText(childElement, "NGTYYSB_UDEF10"));  // 备用字段2
            child.setUdef11(XmlUtils.getElementText(childElement, "NGTYYSB_UDEF11"));  // 备用字段3
            child.setUdef12(XmlUtils.getElementText(childElement, "NGTYYSB_UDEF12"));  // 备用字段4
            child.setUdef13(XmlUtils.getElementText(childElement, "NGTYYSB_UDEF13"));  // 备用字段5
            child.setUdef14(XmlUtils.getElementText(childElement, "NGTYYSB_UDEF14"));  // 备用字段6
            child.setUdef15(XmlUtils.getElementText(childElement, "NGTYYSB_UDEF15"));  // 备用字段7
            child.setUdef16(XmlUtils.getElementText(childElement, "NGTYYSB_UDEF16"));  // 备用字段8
            child.setUdef17(XmlUtils.getElementText(childElement, "NGTYYSB_UDEF17"));  // 备用字段9
            child.setUdef18(XmlUtils.getElementText(childElement, "NGTYYSB_UDEF18"));  // 备用字段10

            log.trace("成功解析部门子表记录: GUID={}, 来源系统={}", guid, child.getSourceSystem());
            return child;

        } catch (Exception e) {
            log.error("解析部门子表数据失败", e);
            return null;
        }
    }

    /**
     * 解析员工XML元素为DTO对象
     *
     * @param dataElement XML元素
     * @return 员工DTO对象
     */
    private EmployeeDataDTO parseEmployeeElement(Element dataElement) {
        try {
            EmployeeDataDTO employee = new EmployeeDataDTO();

            // 解析员工基本信息
            String mdmId = XmlUtils.getElementText(dataElement, "MDMZGZD_NM");
            String employeeCode = XmlUtils.getElementText(dataElement, "MDMZGZD_ZGBH");
            String employeeName = XmlUtils.getElementText(dataElement, "MDMZGZD_ZGXM");

            if (mdmId == null || mdmId.isEmpty()) {
                log.warn("员工MDM ID为空，跳过处理 - 员工: {}, 工号: {}", employeeName, employeeCode);
                return null;
            }

            employee.setMdmId(mdmId);
            employee.setMdmHrdwnm(XmlUtils.getElementText(dataElement, "MDMZGZD_HRDWNM"));
            employee.setEmployeeCode(employeeCode);
            employee.setEmployeeName(employeeName);
            employee.setGender(XmlUtils.getElementText(dataElement, "MDMZGZD_ZGXB"));
            employee.setMobile(XmlUtils.getElementText(dataElement, "MDMZGZD_MOBILE"));
            employee.setStatus(XmlUtils.getElementText(dataElement, "USERPREDEF_16"));
            employee.setIdCard(XmlUtils.getElementText(dataElement, "USERPREDEF_21"));
            employee.setAccount(XmlUtils.getElementText(dataElement, "MDMZGZD_ZH"));

            // 解析出生日期
            String birthDateStr = XmlUtils.getElementText(dataElement, "MDMZGZD_CSRQ");
            if (birthDateStr != null && !birthDateStr.isEmpty()) {
                try {
                    employee.setBirthDate(DATE_FORMAT.get().parse(birthDateStr));
                } catch (ParseException e) {
                    log.warn("员工 {} 的出生日期解析失败: {}", employeeCode, birthDateStr);
                }
            }

            employee.setEmail(XmlUtils.getElementText(dataElement, "MDMZGZD_EMAIL"));
            employee.setOrgType(XmlUtils.getElementText(dataElement, "USERPREDEF_17"));
            employee.setOrgLevel1(XmlUtils.getElementText(dataElement, "USERPREDEF_18"));
            employee.setOrgLevel2(XmlUtils.getElementText(dataElement, "USERPREDEF_19"));
            employee.setOrgLevel3(XmlUtils.getElementText(dataElement, "USERPREDEF_20"));
            employee.setWechat(XmlUtils.getElementText(dataElement, "MDMZGZD_WECHAT"));
            employee.setTel(XmlUtils.getElementText(dataElement, "MDMZGZD_TEL"));
            employee.setNote(XmlUtils.getElementText(dataElement, "MDMZGZD_NOTE"));
            employee.setIsDisabled(XmlUtils.getElementText(dataElement, "MDMZGZD_TYBZ"));
            employee.setUserType(XmlUtils.getElementText(dataElement, "USERPREDEF_3"));
            employee.setOrgCode(XmlUtils.getElementText(dataElement, "USERPREDEF_4"));
            employee.setIdName(XmlUtils.getElementText(dataElement, "USERPREDEF_5"));
            employee.setUserCategory(XmlUtils.getElementText(dataElement, "USERPREDEF_7")); // 用户分类
            employee.setUserLevel(XmlUtils.getElementText(dataElement, "USERPREDEF_8")); // 用户级别
            employee.setUserStatus(XmlUtils.getElementText(dataElement, "USERPREDEF_9")); // 用户状态

            // 解析职位信息
            Element positions = dataElement.element("O_CHILDS1");
            List<EmployeePositionDTO> positionList = new ArrayList<>();
            if (positions != null) {
                List<Element> positionElements = positions.elements("O_CHILD");
                log.debug("员工 {} 包含 {} 个岗位记录", employeeCode, positionElements.size());
                for (Element posElement : positionElements) {
                    EmployeePositionDTO position = parseEmployeePositionElement(posElement, mdmId);
                    if (position != null) {
                        positionList.add(position);
                    }
                }
            }
            employee.setPositions(positionList);

            // 解析职称信息
            Element titles = dataElement.element("O_CHILDS2");
            List<EmployeeTitleDTO> titleList = new ArrayList<>();
            if (titles != null) {
                List<Element> titleElements = titles.elements("O_CHILD");
                log.debug("员工 {} 包含 {} 个职称记录", employeeCode, titleElements.size());
                for (Element titleElement : titleElements) {
                    EmployeeTitleDTO title = parseEmployeeTitleElement(titleElement, mdmId);
                    if (title != null) {
                        titleList.add(title);
                    }
                }
            }
            employee.setTitles(titleList);

            // 解析系统信息
            Element systems = dataElement.element("O_CHILDS3");
            List<EmployeeSystemDTO> systemList = new ArrayList<>();
            if (systems != null) {
                List<Element> systemElements = systems.elements("O_CHILD");
                log.debug("员工 {} 包含 {} 个系统记录", employeeCode, systemElements.size());
                for (Element systemElement : systemElements) {
                    EmployeeSystemDTO system = parseEmployeeSystemElement(systemElement, mdmId);
                    if (system != null) {
                        systemList.add(system);
                    }
                }
            }
            employee.setSystems(systemList);

            log.trace("成功解析员工: {} | 工号: {} | 岗位: {} 个 | 职称: {} 个 | 系统: {} 个",
                    employeeName, employeeCode, positionList.size(), titleList.size(), systemList.size());
            return employee;

        } catch (Exception e) {
            log.error("解析员工数据失败", e);
            return null;
        }
    }

    /**
     * 解析员工岗位XML元素为DTO对象
     * 根据原有同步代码的字段映射进行解析
     *
     * @param posElement XML元素
     * @param employeeMdmId 员工MDM ID
     * @return 员工岗位DTO对象
     */
    private EmployeePositionDTO parseEmployeePositionElement(Element posElement, String employeeMdmId) {
        try {
            String guid = XmlUtils.getElementText(posElement, "MDMRYZZGW_V4_GUID");
            if (guid == null || guid.isEmpty()) {
                log.debug("员工岗位记录GUID为空，跳过处理");
                return null;
            }

            EmployeePositionDTO position = new EmployeePositionDTO();

            position.setGuid(guid);
            // 设置员工关联ID（从XML中读取，而不是使用参数）
            String xmlEmployeeMdmId = XmlUtils.getElementText(posElement, "MDMRYZZGW_V4_ZBNM");
            position.setEmployeeMdmId(xmlEmployeeMdmId != null ? xmlEmployeeMdmId : employeeMdmId);

            // 根据原有同步代码的字段映射
            position.setPositionCode(XmlUtils.getElementText(posElement, "MDMRYZZGW_V4_PCXMZY")); // 判重项目值域
            position.setOrgCode(XmlUtils.getElementText(posElement, "MDMRYZZGW_V4_UDEF1")); // 所属组织编码
            position.setDepartmentCode(XmlUtils.getElementText(posElement, "MDMRYZZGW_V4_UDEF3")); // 所属职务工种编码
            position.setIsPrimary(XmlUtils.getElementText(posElement, "MDMRYZZGW_V4_UDEF7")); // 是否停用状态
            position.setStatus(XmlUtils.getElementText(posElement, "MDMRYZZGW_V4_UDEF8")); // 操作标识
            position.setIsActive(XmlUtils.getElementText(posElement, "MDMRYZZGW_V4_UDEF5")); // 是否主岗
            position.setPositionDetailCode(XmlUtils.getElementText(posElement, "MDMRYZZGW_V4_UDEF6")); // 主岗编码

            log.trace("成功解析员工岗位记录: GUID={}, 组织编码={}", guid, position.getOrgCode());
            return position;

        } catch (Exception e) {
            log.error("解析员工岗位数据失败", e);
            return null;
        }
    }

    /**
     * 解析员工职称XML元素为DTO对象
     * 根据原有同步代码的字段映射进行解析
     *
     * @param titleElement XML元素
     * @param employeeMdmId 员工MDM ID
     * @return 员工职称DTO对象
     */
    private EmployeeTitleDTO parseEmployeeTitleElement(Element titleElement, String employeeMdmId) {
        try {
            String guid = XmlUtils.getElementText(titleElement, "MDMRYGJ_V4_GUID");
            if (guid == null || guid.isEmpty()) {
                log.debug("员工职称记录GUID为空，跳过处理");
                return null;
            }

            EmployeeTitleDTO title = new EmployeeTitleDTO();

            title.setGuid(guid);
            // 设置员工关联ID（从XML中读取，而不是使用参数）
            String xmlEmployeeMdmId = XmlUtils.getElementText(titleElement, "MDMRYGJ_V4_ZBNM");
            title.setEmployeeMdmId(xmlEmployeeMdmId != null ? xmlEmployeeMdmId : employeeMdmId);

            // 根据原有同步代码的字段映射
            title.setTitleCode(XmlUtils.getElementText(titleElement, "MDMRYGJ_V4_PCXMZY")); // 判重项目值域
            title.setTitleType(XmlUtils.getElementText(titleElement, "MDMRYGJ_V4_UDEF1")); // 员工所属组织
            title.setTitleLevel(XmlUtils.getElementText(titleElement, "MDMRYGJ_V4_UDEF3")); // 职级编码
            title.setStatus(XmlUtils.getElementText(titleElement, "MDMRYGJ_V4_UDEF5")); // 操作标识
            title.setTitleName(XmlUtils.getElementText(titleElement, "MDMRYGJ_V4_UDEF8")); // 职称
            title.setTitleCategory(XmlUtils.getElementText(titleElement, "MDMRYGJ_V4_UDEF9")); // 层级O、E、C、S
            title.setDefaultOrgCode(XmlUtils.getElementText(titleElement, "MDMRYGJ_V4_UDEF2")); // 默认组织编码
            title.setIsDisabled(XmlUtils.getElementText(titleElement, "MDMRYGJ_V4_UDEF6")); // 是否停用状态

            log.trace("成功解析员工职称记录: GUID={}, 职称名称={}", guid, title.getTitleName());
            return title;

        } catch (Exception e) {
            log.error("解析员工职称数据失败", e);
            return null;
        }
    }

    /**
     * 解析员工系统标识XML元素为DTO对象
     * 根据原有同步代码的字段映射进行解析
     *
     * @param systemElement XML元素
     * @param employeeMdmId 员工MDM ID
     * @return 员工系统标识DTO对象
     */
    private EmployeeSystemDTO parseEmployeeSystemElement(Element systemElement, String employeeMdmId) {
        try {
            String guid = XmlUtils.getElementText(systemElement, "NGTYYSB_GUID");
            if (guid == null || guid.isEmpty()) {
                log.debug("员工系统记录GUID为空，跳过处理");
                return null;
            }

            EmployeeSystemDTO system = new EmployeeSystemDTO();

            system.setGuid(guid);
            // 设置员工关联ID（从XML中读取，而不是使用参数）
            String xmlEmployeeMdmId = XmlUtils.getElementText(systemElement, "NGTYYSB_ZBNM");
            system.setEmployeeMdmId(xmlEmployeeMdmId != null ? xmlEmployeeMdmId : employeeMdmId);

            // 根据原有同步代码的字段映射
            system.setSystemCode(XmlUtils.getElementText(systemElement, "NGTYYSB_LYXTBH")); // 来源系统编码
            system.setSystemDataId(XmlUtils.getElementText(systemElement, "NGTYYSB_LYXTDATANM")); // 来源系统人员内码
            system.setEmployeeCode(XmlUtils.getElementText(systemElement, "NGTYYSB_UDEF1")); // 来源系统人员编号
            system.setOrgCode(XmlUtils.getElementText(systemElement, "NGTYYSB_UDEF3")); // 来源系统组织编号
            system.setDepartmentCode(XmlUtils.getElementText(systemElement, "NGTYYSB_UDEF4")); // 来源系统职务工种编码
            system.setLoginAccount(XmlUtils.getElementText(systemElement, "NGTYYSB_UDEF5")); // 登录账号
            system.setCompanyCode(XmlUtils.getElementText(systemElement, "NGTYYSB_UDEF2")); // 公司别标识

            log.trace("成功解析员工系统记录: GUID={}, 系统编码={}", guid, system.getSystemCode());
            return system;

        } catch (Exception e) {
            log.error("解析员工系统标识数据失败", e);
            return null;
        }
    }
}
