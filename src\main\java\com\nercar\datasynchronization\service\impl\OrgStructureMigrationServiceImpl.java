package com.nercar.datasynchronization.service.impl;

import com.nercar.datasynchronization.entity.Department;
import com.nercar.datasynchronization.repository.DepartmentRepository;
import com.nercar.datasynchronization.service.OrgStructureMigrationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 组织架构迁移服务实现类 - SQL文件生成版本
 * 负责将MySQL中的department表数据生成PostgreSQL的INSERT SQL文件
 */
@Slf4j
@Service
public class OrgStructureMigrationServiceImpl implements OrgStructureMigrationService {

    @Autowired
    private DepartmentRepository departmentRepository;

    /**
     * 部门组织代码到PostgreSQL ID的映射缓存
     * key: org_code (组织代码)
     * value: PostgreSQL t_org_structure表的ID
     */
    private final Map<String, Long> orgCodeToIdMapping = new ConcurrentHashMap<>();

    /**
     * SQL文件输出目录
     */
    private static final String SQL_OUTPUT_DIR = "migration_sql";

    /**
     * 当前ID计数器
     */
    private long currentId = 1;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void migrateToOrgStructure() {
        migrateToOrgStructure(false);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void migrateToOrgStructure(boolean clearExisting) {
        long startTime = System.currentTimeMillis();
        log.info("=== 开始生成组织架构迁移SQL文件 ===");

        try {
            // 1. 清空映射缓存
            orgCodeToIdMapping.clear();
            currentId = 1;

            // 2. 查询所有正常状态的部门
            List<Department> activeDepartments = departmentRepository.findActiveDepartments();
            log.info("查询到 {} 个正常状态的部门", activeDepartments.size());

            if (activeDepartments.isEmpty()) {
                log.warn("没有找到需要迁移的部门数据");
                return;
            }

            // 3. 生成SQL文件
            String sqlFilePath = generateMigrationSQL(activeDepartments, clearExisting);

            long endTime = System.currentTimeMillis();
            log.info("=== 组织架构迁移SQL文件生成完成 ===");
            log.info("生成耗时: {} 毫秒", endTime - startTime);
            log.info("SQL文件路径: {}", sqlFilePath);
            log.info("迁移记录数: {}", activeDepartments.size());

        } catch (Exception e) {
            log.error("生成组织架构迁移SQL文件失败", e);
            throw new RuntimeException("生成组织架构迁移SQL文件失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void clearOrgStructureData() {
        log.info("生成清理组织架构同步数据的SQL...");

        try {
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String fileName = String.format("clear_org_structure_%s.sql", timestamp);
            String filePath = createOutputDirectory() + "/" + fileName;

            try (FileWriter writer = new FileWriter(filePath)) {
                writer.write("-- 清理组织架构同步数据SQL\n");
                writer.write("-- 生成时间: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + "\n\n");
                writer.write("-- 删除data_source=2的同步数据，保留手工录入数据\n");
                writer.write("DELETE FROM t_org_structure WHERE data_source = 2;\n\n");
                writer.write("-- 查看删除结果\n");
                writer.write("SELECT COUNT(*) as remaining_records FROM t_org_structure;\n");
            }

            log.info("清理SQL文件生成完成: {}", filePath);

        } catch (Exception e) {
            log.error("生成清理SQL文件失败", e);
            throw new RuntimeException("生成清理SQL文件失败: " + e.getMessage(), e);
        }
    }

    @Override
    public int validateMigrationResult() {
        log.info("生成验证SQL文件...");

        try {
            long mysqlCount = departmentRepository.countActiveDepartments();

            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String fileName = String.format("validate_migration_%s.sql", timestamp);
            String filePath = createOutputDirectory() + "/" + fileName;

            try (FileWriter writer = new FileWriter(filePath)) {
                writer.write("-- 验证组织架构迁移结果SQL\n");
                writer.write("-- 生成时间: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + "\n");
                writer.write("-- MySQL正常部门数量: " + mysqlCount + "\n\n");

                writer.write("-- 1. 检查PostgreSQL中的迁移记录数量\n");
                writer.write("SELECT COUNT(*) as postgresql_count FROM t_org_structure WHERE data_source = 2;\n\n");

                writer.write("-- 2. 检查根节点数量\n");
                writer.write("SELECT COUNT(*) as root_count FROM t_org_structure WHERE data_source = 2 AND pre_id IS NULL;\n\n");

                writer.write("-- 3. 检查是否有孤儿节点\n");
                writer.write("SELECT COUNT(*) as orphan_count FROM t_org_structure o1 \n");
                writer.write("WHERE o1.data_source = 2 AND o1.pre_id IS NOT NULL \n");
                writer.write("AND NOT EXISTS (SELECT 1 FROM t_org_structure o2 WHERE o2.id = o1.pre_id);\n\n");

                writer.write("-- 4. 检查循环引用\n");
                writer.write("SELECT COUNT(*) as cycle_count FROM t_org_structure WHERE data_source = 2 AND id = pre_id;\n\n");

                writer.write("-- 5. 查看所有迁移的数据\n");
                writer.write("SELECT id, organ_name, pre_id, order_info, is_del, create_time, data_source \n");
                writer.write("FROM t_org_structure WHERE data_source = 2 ORDER BY id;\n");
            }

            log.info("验证SQL文件生成完成: {}", filePath);
            return (int) mysqlCount;

        } catch (Exception e) {
            log.error("生成验证SQL文件失败", e);
            return -3;
        }
    }

    @Override
    public String getMigrationStatistics() {
        try {
            long mysqlCount = departmentRepository.countActiveDepartments();
            List<Department> rootDepts = departmentRepository.findRootDepartments();

            return String.format(
                "迁移统计信息:\n" +
                "- MySQL正常部门数量: %d\n" +
                "- 根节点数量: %d\n" +
                "- 子节点数量: %d\n" +
                "- 状态: SQL文件生成模式",
                mysqlCount, rootDepts.size(), mysqlCount - rootDepts.size()
            );
        } catch (Exception e) {
            return "获取统计信息失败: " + e.getMessage();
        }
    }

    @Override
    public boolean checkPostgreSQLConnection() {
        // SQL文件生成模式不需要PostgreSQL连接
        log.info("SQL文件生成模式，无需检查PostgreSQL连接");
        return true;
    }

    /**
     * 创建输出目录
     */
    private String createOutputDirectory() throws IOException {
        Path outputPath = Paths.get(SQL_OUTPUT_DIR);
        if (!Files.exists(outputPath)) {
            Files.createDirectories(outputPath);
        }
        return outputPath.toAbsolutePath().toString();
    }

    /**
     * 生成迁移SQL文件
     */
    private String generateMigrationSQL(List<Department> departments, boolean clearExisting) throws IOException {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String fileName = String.format("org_structure_migration_%s.sql", timestamp);
        String filePath = createOutputDirectory() + "/" + fileName;

        try (FileWriter writer = new FileWriter(filePath)) {
            // 写入文件头
            writeFileHeader(writer, departments.size(), clearExisting);

            // 如果需要清理现有数据
            if (clearExisting) {
                writeClearDataSQL(writer);
            }

            // 写入序列重置SQL
            writeSequenceResetSQL(writer);

            // 分离根节点和子节点（使用parent_code判断）
            List<Department> rootDepartments = new ArrayList<>();
            List<Department> childDepartments = new ArrayList<>();

            for (Department dept : departments) {
                if (dept.getParentCode() == null || dept.getParentCode().trim().isEmpty()) {
                    rootDepartments.add(dept);
                } else {
                    childDepartments.add(dept);
                }
            }

            log.info("根节点数量: {}, 子节点数量: {}", rootDepartments.size(), childDepartments.size());

            // 生成根节点SQL
            writeRootDepartmentsSQL(writer, rootDepartments);

            // 生成子节点SQL（按层级处理）
            writeChildDepartmentsSQL(writer, childDepartments, rootDepartments);

            // 写入验证SQL
            writeValidationSQL(writer);
        }

        return filePath;
    }

    /**
     * 写入文件头信息
     */
    private void writeFileHeader(FileWriter writer, int totalCount, boolean clearExisting) throws IOException {
        writer.write("-- =====================================================\n");
        writer.write("-- 组织架构数据迁移SQL文件\n");
        writer.write("-- 生成时间: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + "\n");
        writer.write("-- 迁移记录数: " + totalCount + "\n");
        writer.write("-- 清理现有数据: " + (clearExisting ? "是" : "否") + "\n");
        writer.write("-- =====================================================\n\n");

        writer.write("-- 使用说明:\n");
        writer.write("-- 1. 请在PostgreSQL数据库中执行此SQL文件\n");
        writer.write("-- 2. 执行前请确保t_org_structure表已存在\n");
        writer.write("-- 3. 执行前请确保t_org_structure_id_seq序列已存在\n");
        writer.write("-- 4. 建议在测试环境先执行验证\n\n");

        writer.write("-- 层级关系映射说明:\n");
        writer.write("-- MySQL department表: 使用parent_code指向父级的org_code\n");
        writer.write("-- PostgreSQL t_org_structure表: 使用pre_id指向父级的id\n");
        writer.write("-- 转换逻辑: parent_code -> 查找父级org_code -> 获取父级PostgreSQL ID -> 作为pre_id\n\n");
    }

    /**
     * 写入清理数据SQL
     */
    private void writeClearDataSQL(FileWriter writer) throws IOException {
        writer.write("-- =====================================================\n");
        writer.write("-- 清理现有同步数据\n");
        writer.write("-- =====================================================\n");
        writer.write("DELETE FROM t_org_structure WHERE data_source = 2;\n");
        writer.write("SELECT COUNT(*) as deleted_count FROM t_org_structure WHERE data_source = 2;\n\n");
    }

    /**
     * 写入序列重置SQL
     */
    private void writeSequenceResetSQL(FileWriter writer) throws IOException {
        writer.write("-- =====================================================\n");
        writer.write("-- 重置序列（可选）\n");
        writer.write("-- =====================================================\n");
        writer.write("-- 如果需要从1开始生成ID，请取消下面的注释\n");
        writer.write("-- SELECT setval('t_org_structure_id_seq', 1, false);\n\n");
    }

    /**
     * 写入根节点SQL
     */
    private void writeRootDepartmentsSQL(FileWriter writer, List<Department> rootDepartments) throws IOException {
        writer.write("-- =====================================================\n");
        writer.write("-- 插入根节点数据 (共 " + rootDepartments.size() + " 个)\n");
        writer.write("-- =====================================================\n");

        for (Department dept : rootDepartments) {
            long id = currentId++;
            // 建立org_code到PostgreSQL ID的映射，供子节点查找使用
            orgCodeToIdMapping.put(dept.getOrgCode(), id);

            String sql = String.format(
                "INSERT INTO t_org_structure (id, organ_name, pre_id, order_info, is_del, create_time, modify_time, data_source) " +
                "VALUES (%d, '%s', NULL, %d, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 2); -- 根节点 %s UUID:%s\n",
                id,
                escapeSqlString(dept.getOrgName()),
                generateOrderInfo(dept),
                dept.getOrgCode(),
                dept.getDeptUuid()
            );
            writer.write(sql);
        }
        writer.write("\n");
    }

    /**
     * 写入子节点SQL
     */
    private void writeChildDepartmentsSQL(FileWriter writer, List<Department> childDepartments, List<Department> rootDepartments) throws IOException {
        writer.write("-- =====================================================\n");
        writer.write("-- 插入子节点数据 (共 " + childDepartments.size() + " 个)\n");
        writer.write("-- =====================================================\n");

        // 按层级处理子节点
        Set<String> processedUuids = new HashSet<>();
        int maxLevels = 10; // 最大层级限制

        for (int level = 1; level <= maxLevels; level++) {
            List<Department> currentLevelDepts = new ArrayList<>();

            for (Department dept : childDepartments) {
                if (!processedUuids.contains(dept.getDeptUuid()) &&
                    orgCodeToIdMapping.containsKey(dept.getParentCode())) {
                    currentLevelDepts.add(dept);
                }
            }

            if (currentLevelDepts.isEmpty()) {
                break;
            }

            writer.write("-- 第 " + level + " 层子节点 (共 " + currentLevelDepts.size() + " 个)\n");

            for (Department dept : currentLevelDepts) {
                long id = currentId++;
                // 通过parent_code查找父级的PostgreSQL ID
                Long parentId = orgCodeToIdMapping.get(dept.getParentCode());
                // 将当前部门的org_code映射到新生成的PostgreSQL ID
                orgCodeToIdMapping.put(dept.getOrgCode(), id);
                processedUuids.add(dept.getDeptUuid());

                String sql = String.format(
                    "INSERT INTO t_org_structure (id, organ_name, pre_id, order_info, is_del, create_time, modify_time, data_source) " +
                    "VALUES (%d, '%s', %d, %d, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 2); -- %s (parent_code:%s -> pre_id:%d) UUID:%s\n",
                    id,
                    escapeSqlString(dept.getOrgName()),
                    parentId,
                    generateOrderInfo(dept),
                    dept.getOrgCode(),
                    dept.getParentCode(),
                    parentId,
                    dept.getDeptUuid()
                );
                writer.write(sql);
            }
            writer.write("\n");
        }

        // 检查是否有未处理的部门
        List<Department> unprocessed = new ArrayList<>();
        for (Department dept : childDepartments) {
            if (!processedUuids.contains(dept.getDeptUuid())) {
                unprocessed.add(dept);
            }
        }

        if (!unprocessed.isEmpty()) {
            writer.write("-- 警告：以下部门因父级不存在而未处理 (共 " + unprocessed.size() + " 个)\n");
            for (Department dept : unprocessed) {
                writer.write("-- " + dept.getOrgName() + " (代码: " + dept.getOrgCode() + ", 父级代码: " + dept.getParentCode() + ", UUID: " + dept.getDeptUuid() + ")\n");
            }
            writer.write("\n");
        }
    }

    /**
     * 写入验证SQL
     */
    private void writeValidationSQL(FileWriter writer) throws IOException {
        writer.write("-- =====================================================\n");
        writer.write("-- 验证SQL\n");
        writer.write("-- =====================================================\n");
        writer.write("-- 检查插入的记录数量\n");
        writer.write("SELECT COUNT(*) as total_inserted FROM t_org_structure WHERE data_source = 2;\n\n");

        writer.write("-- 检查根节点数量\n");
        writer.write("SELECT COUNT(*) as root_count FROM t_org_structure WHERE data_source = 2 AND pre_id IS NULL;\n\n");

        writer.write("-- 检查孤儿节点\n");
        writer.write("SELECT COUNT(*) as orphan_count FROM t_org_structure o1 \n");
        writer.write("WHERE o1.data_source = 2 AND o1.pre_id IS NOT NULL \n");
        writer.write("AND NOT EXISTS (SELECT 1 FROM t_org_structure o2 WHERE o2.id = o1.pre_id);\n\n");

        writer.write("-- 查看所有插入的数据\n");
        writer.write("SELECT id, organ_name, pre_id, order_info, create_time FROM t_org_structure \n");
        writer.write("WHERE data_source = 2 ORDER BY id;\n\n");
    }

    /**
     * 转义SQL字符串
     */
    private String escapeSqlString(String str) {
        if (str == null) return "";
        return str.replace("'", "''").replace("\\", "\\\\");
    }

    /**
     * 生成排序信息
     */
    private int generateOrderInfo(Department dept) {
        return Math.abs(dept.getOrgCode() != null ? dept.getOrgCode().hashCode() % 1000 : 1);
    }
}
