package com.nercar.datasynchronization.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

/**
 * 员工详情实体类 - 存储从质量系统同步的岗位数据
 */
@Data
@Entity
@Table(name = "hr_user_details")
@DynamicInsert
@DynamicUpdate
public class HrUserDetails {
    
    @Id
    @Column(name = "id")
    private String id;
    
    @Column(name = "education")
    private String education;
    
    @Column(name = "sex")
    private String sex;
    
    @Column(name = "birthday")
    private String birthday;
    
    @Column(name = "age")
    private String age;
    
    @Column(name = "user_no")
    private String userNo;
    
    @Column(name = "post")
    private String post;
    
    @Column(name = "name")
    private String name;
    
    @Column(name = "department")
    private String department;
    
    @Column(name = "department_id")
    private String departmentId;
    
    @Column(name = "company_id")
    private String companyId;
    
    @Column(name = "create_user_no")
    private String createUserNo;
    
    @Column(name = "create_date_time")
    private String createDateTime;
    
    @Column(name = "update_user_no")
    private String updateUserNo;
    
    @Column(name = "update_date_time")
    private String updateDateTime;
    
    @Column(name = "post_no")
    private String postNo;
    
    @Column(name = "skill_level")
    private String skillLevel;
    
    @Column(name = "work_exp")
    private String workExp;
    
    @Column(name = "teams_groups")
    private String teamsGroups;
}