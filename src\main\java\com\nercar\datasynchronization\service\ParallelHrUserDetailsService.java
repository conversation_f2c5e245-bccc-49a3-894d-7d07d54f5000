//package com.nercar.datasynchronization.service;
//
//import java.util.Date;
//
///**
// * 多线程处理HR用户详情服务接口
// */
//public interface ParallelHrUserDetailsService {
//    /**
//     * 使用多线程从ERP系统更新所有用户详情
//     */
//    void updateAllUserDetailFromErpParallel();
//
//    /**
//     * 使用多线程从ERP系统更新指定时间范围内的用户详情
//     * 注意：由于API限制，此方法仍然获取所有数据，时间范围仅用于记录同步任务
//     *
//     * @param startDate 开始时间
//     * @param endDate 结束时间
//     */
//    void updateUserDetailFromErpParallel(Date startDate, Date endDate);
//}