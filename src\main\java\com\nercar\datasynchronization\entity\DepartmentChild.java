package com.nercar.datasynchronization.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

/**
 * 部门子表实体 - 存储部门在其他系统中的标识信息
 * 对应文档1.5.1组织信息下发接口中的从表1（映射表）
 */
@Data
@Entity
@Table(name = "department_child")
@DynamicInsert
@DynamicUpdate
public class DepartmentChild {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "guid")
    private String guid;

    @Column(name = "dept_uuid")
    private String deptUuid;

    @Column(name = "source_system")
    private String sourceSystem;

    @Column(name = "source_data_nm")
    private String sourceDataNm;

    @Column(name = "udef1")
    private String udef1;

    @Column(name = "udef2")
    private String udef2;

    @Column(name = "udef3")
    private String udef3;

    @Column(name = "udef4")
    private String udef4;

    @Column(name = "udef5")
    private String udef5;

    @Column(name = "udef6")
    private String udef6;

    @Column(name = "dept_mdm_id")
    private String deptMdmId;

    @Column(name = "udef7")
    private String udef7;

    @Column(name = "udef8")
    private String udef8;

    @Column(name = "udef9")
    private String udef9;

    @Column(name = "udef10")
    private String udef10;

    @Column(name = "udef11")
    private String udef11;

    @Column(name = "udef12")
    private String udef12;

    @Column(name = "udef13")
    private String udef13;

    @Column(name = "udef14")
    private String udef14;

    @Column(name = "udef15")
    private String udef15;

    @Column(name = "udef16")
    private String udef16;

    @Column(name = "udef17")
    private String udef17;

    @Column(name = "udef18")
    private String udef18;
}