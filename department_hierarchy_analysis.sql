-- =====================================================
-- 基于department_sync_test2.sql的full_name分析部门层级结构
-- =====================================================

-- 第一步：提取所有部门的完整名称进行分析
SELECT 
    '=== 部门层级分析 ===' as section,
    org_code,
    org_name,
    full_name,
    -- 分析层级深度
    CASE 
        WHEN full_name LIKE '%事业部%厂%车间%班%' THEN 4
        WHEN full_name LIKE '%事业部%厂%车间%' THEN 3
        WHEN full_name LIKE '%事业部%厂%' THEN 2
        WHEN full_name LIKE '%事业部%' THEN 1
        WHEN full_name LIKE '%集团%部%室%' THEN 3
        WHEN full_name LIKE '%集团%部%' THEN 2
        WHEN full_name LIKE '%集团%' THEN 1
        WHEN full_name LIKE '%有限公司%部%' THEN 2
        WHEN full_name LIKE '%有限公司%' THEN 1
        ELSE 1
    END as estimated_level,
    
    -- 提取一级部门（事业部/集团/公司）
    CASE 
        WHEN full_name LIKE '%事业部%' THEN 
            CONCAT(SUBSTRING_INDEX(full_name, '事业部', 1), '事业部')
        WHEN full_name LIKE '%集团%' AND full_name NOT LIKE '%事业部%' THEN 
            CONCAT(SUBSTRING_INDEX(full_name, '集团', 1), '集团')
        WHEN full_name LIKE '%有限公司%' AND full_name NOT LIKE '%事业部%' AND full_name NOT LIKE '%集团%' THEN 
            CONCAT(SUBSTRING_INDEX(full_name, '有限公司', 1), '有限公司')
        WHEN full_name LIKE '%中心%' AND full_name NOT LIKE '%事业部%' AND full_name NOT LIKE '%集团%' AND full_name NOT LIKE '%有限公司%' THEN 
            CONCAT(SUBSTRING_INDEX(full_name, '中心', 1), '中心')
        WHEN full_name LIKE '%部' AND full_name NOT LIKE '%事业部%' THEN 
            full_name
        ELSE 
            SUBSTRING_INDEX(full_name, '（', 1)
    END as level1_name

FROM department_sync_test 
WHERE is_history = 0 AND user_predef_14 != 'D'
AND full_name IS NOT NULL AND full_name != ''
ORDER BY estimated_level DESC, full_name;

-- 第二步：统计一级部门
SELECT 
    '=== 一级部门统计 ===' as section,
    level1_name,
    COUNT(*) as sub_dept_count,
    GROUP_CONCAT(DISTINCT org_name ORDER BY org_name SEPARATOR '; ') as sample_depts
FROM (
    SELECT 
        org_name,
        CASE 
            WHEN full_name LIKE '%事业部%' THEN 
                CONCAT(SUBSTRING_INDEX(full_name, '事业部', 1), '事业部')
            WHEN full_name LIKE '%集团%' AND full_name NOT LIKE '%事业部%' THEN 
                CONCAT(SUBSTRING_INDEX(full_name, '集团', 1), '集团')
            WHEN full_name LIKE '%有限公司%' AND full_name NOT LIKE '%事业部%' AND full_name NOT LIKE '%集团%' THEN 
                CONCAT(SUBSTRING_INDEX(full_name, '有限公司', 1), '有限公司')
            WHEN full_name LIKE '%中心%' AND full_name NOT LIKE '%事业部%' AND full_name NOT LIKE '%集团%' AND full_name NOT LIKE '%有限公司%' THEN 
                CONCAT(SUBSTRING_INDEX(full_name, '中心', 1), '中心')
            WHEN full_name LIKE '%部' AND full_name NOT LIKE '%事业部%' THEN 
                full_name
            ELSE 
                SUBSTRING_INDEX(full_name, '（', 1)
        END as level1_name
    FROM department_sync_test 
    WHERE is_history = 0 AND user_predef_14 != 'D'
    AND full_name IS NOT NULL AND full_name != ''
) t
GROUP BY level1_name
ORDER BY sub_dept_count DESC;

-- 第三步：分析二级部门（厂/处级别）
SELECT 
    '=== 二级部门分析 ===' as section,
    level1_name,
    level2_name,
    COUNT(*) as sub_dept_count,
    GROUP_CONCAT(DISTINCT org_name ORDER BY org_name SEPARATOR '; ') as sample_depts
FROM (
    SELECT 
        org_name,
        CASE 
            WHEN full_name LIKE '%事业部%' THEN 
                CONCAT(SUBSTRING_INDEX(full_name, '事业部', 1), '事业部')
            WHEN full_name LIKE '%集团%' AND full_name NOT LIKE '%事业部%' THEN 
                CONCAT(SUBSTRING_INDEX(full_name, '集团', 1), '集团')
            WHEN full_name LIKE '%有限公司%' AND full_name NOT LIKE '%事业部%' AND full_name NOT LIKE '%集团%' THEN 
                CONCAT(SUBSTRING_INDEX(full_name, '有限公司', 1), '有限公司')
            ELSE 
                SUBSTRING_INDEX(full_name, '（', 1)
        END as level1_name,
        
        CASE 
            WHEN full_name LIKE '%事业部%厂%' THEN 
                CONCAT(SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '厂', 1), '事业部', -1), '厂')
            WHEN full_name LIKE '%事业部%处%' THEN 
                CONCAT(SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '处', 1), '事业部', -1), '处')
            WHEN full_name LIKE '%集团%部%' THEN 
                CONCAT(SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '部', 1), '集团', -1), '部')
            WHEN full_name LIKE '%有限公司%部%' THEN 
                CONCAT(SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '部', 1), '有限公司', -1), '部')
            WHEN full_name LIKE '%中心%厂%' THEN 
                CONCAT(SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '厂', 1), '中心', -1), '厂')
            ELSE ''
        END as level2_name
    FROM department_sync_test 
    WHERE is_history = 0 AND user_predef_14 != 'D'
    AND full_name IS NOT NULL AND full_name != ''
) t
WHERE level2_name != ''
GROUP BY level1_name, level2_name
ORDER BY level1_name, sub_dept_count DESC;

-- 第四步：分析三级部门（车间/室/科级别）
SELECT 
    '=== 三级部门分析 ===' as section,
    level1_name,
    level2_name,
    level3_name,
    COUNT(*) as sub_dept_count
FROM (
    SELECT 
        CASE 
            WHEN full_name LIKE '%事业部%' THEN 
                CONCAT(SUBSTRING_INDEX(full_name, '事业部', 1), '事业部')
            WHEN full_name LIKE '%集团%' AND full_name NOT LIKE '%事业部%' THEN 
                CONCAT(SUBSTRING_INDEX(full_name, '集团', 1), '集团')
            ELSE 
                SUBSTRING_INDEX(full_name, '（', 1)
        END as level1_name,
        
        CASE 
            WHEN full_name LIKE '%事业部%厂%' THEN 
                CONCAT(SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '厂', 1), '事业部', -1), '厂')
            WHEN full_name LIKE '%事业部%处%' THEN 
                CONCAT(SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '处', 1), '事业部', -1), '处')
            ELSE ''
        END as level2_name,
        
        CASE 
            WHEN full_name LIKE '%厂%车间%' THEN 
                CONCAT(SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '车间', 1), '厂', -1), '车间')
            WHEN full_name LIKE '%厂%室%' THEN 
                CONCAT(SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '室', 1), '厂', -1), '室')
            WHEN full_name LIKE '%处%室%' THEN 
                CONCAT(SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '室', 1), '处', -1), '室')
            WHEN full_name LIKE '%厂%科%' THEN 
                CONCAT(SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '科', 1), '厂', -1), '科')
            ELSE ''
        END as level3_name
    FROM department_sync_test 
    WHERE is_history = 0 AND user_predef_14 != 'D'
    AND full_name IS NOT NULL AND full_name != ''
) t
WHERE level3_name != ''
GROUP BY level1_name, level2_name, level3_name
ORDER BY level1_name, level2_name, sub_dept_count DESC;

-- 第五步：生成重构建议
SELECT 
    '=== 重构建议 ===' as section,
    '建议的标准化部门结构：' as recommendation,
    '' as details,
    '' as note
UNION ALL
SELECT 
    'LEVEL1',
    level1_name,
    CONCAT('下属部门数量: ', COUNT(*)) as details,
    '一级部门' as note
FROM (
    SELECT 
        CASE 
            WHEN full_name LIKE '%事业部%' THEN 
                CONCAT(SUBSTRING_INDEX(full_name, '事业部', 1), '事业部')
            WHEN full_name LIKE '%集团%' AND full_name NOT LIKE '%事业部%' THEN 
                CONCAT(SUBSTRING_INDEX(full_name, '集团', 1), '集团')
            WHEN full_name LIKE '%有限公司%' AND full_name NOT LIKE '%事业部%' AND full_name NOT LIKE '%集团%' THEN 
                CONCAT(SUBSTRING_INDEX(full_name, '有限公司', 1), '有限公司')
            WHEN full_name LIKE '%中心%' AND full_name NOT LIKE '%事业部%' AND full_name NOT LIKE '%集团%' AND full_name NOT LIKE '%有限公司%' THEN 
                CONCAT(SUBSTRING_INDEX(full_name, '中心', 1), '中心')
            WHEN full_name LIKE '%部' AND full_name NOT LIKE '%事业部%' THEN 
                full_name
            ELSE 
                SUBSTRING_INDEX(full_name, '（', 1)
        END as level1_name
    FROM department_sync_test 
    WHERE is_history = 0 AND user_predef_14 != 'D'
    AND full_name IS NOT NULL AND full_name != ''
) t
GROUP BY level1_name
ORDER BY COUNT(*) DESC;
