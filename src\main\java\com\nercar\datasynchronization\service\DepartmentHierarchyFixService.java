package com.nercar.datasynchronization.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

/**
 * 部门层级关系修正服务
 * 通过 full_name 验证和修正所有部门的层级关系
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DepartmentHierarchyFixService {

    private final JdbcTemplate jdbcTemplate;
    
    // 用于生成新的 org_code 的计数器
    private static long newOrgCodeCounter = 9000000L;

    /**
     * 执行全面的层级关系修正
     */
    @Transactional
    public FixReport executeHierarchyFix() {
        log.info("开始执行部门层级关系修正");
        
        FixReport report = new FixReport();
        
        try {
            // 1. 获取所有有效部门
            List<DepartmentRecord> allDepartments = getAllValidDepartments();
            report.totalDepartments = allDepartments.size();
            log.info("获取到 {} 个有效部门", allDepartments.size());
            
            // 2. 逐个验证和修正层级关系
            for (DepartmentRecord dept : allDepartments) {
                try {
                    FixResult result = fixSingleDepartment(dept);
                    updateReportStats(report, result);
                } catch (Exception e) {
                    log.error("修正部门失败: {} - {}", dept.orgName, e.getMessage(), e);
                    report.errorDepartments++;
                    logFixOperation(dept.orgCode, dept.orgName, "ERROR", 
                                  "修正失败: " + e.getMessage(), dept.parentCode, dept.parentCode);
                }
            }
            
            // 3. 验证修正结果
            validateFixResults(report);
            
            log.info("部门层级关系修正完成: {}", report);
            return report;
            
        } catch (Exception e) {
            log.error("层级关系修正过程中发生错误", e);
            throw new RuntimeException("层级关系修正失败: " + e.getMessage(), e);
        }
    }

    /**
     * 修正单个部门的层级关系
     */
    private FixResult fixSingleDepartment(DepartmentRecord dept) {
        // 1. 拆解 full_name 得到期望的层级路径
        List<String> expectedHierarchy = parseFullNameToHierarchy(dept.fullName);
        
        // 2. 获取当前的实际层级路径
        List<String> actualHierarchy = getCurrentHierarchyPath(dept);
        
        // 3. 比较是否一致
        if (isHierarchyCorrect(expectedHierarchy, actualHierarchy, dept.orgName)) {
            log.debug("部门层级关系正确: {}", dept.orgName);
            return FixResult.NO_CHANGE;
        }
        
        // 4. 需要修正，找到正确的父级
        String correctParentCode = findOrCreateCorrectParent(dept, expectedHierarchy);
        
        if (correctParentCode == null) {
            log.warn("无法确定正确的父级: {} (full_name: {})", dept.orgName, dept.fullName);
            return FixResult.SKIPPED;
        }
        
        // 5. 更新父级关系
        if (!dept.parentCode.equals(correctParentCode)) {
            updateParentCode(dept.orgCode, correctParentCode);
            logFixOperation(dept.orgCode, dept.orgName, "UPDATE", 
                          String.format("修正父级关系"), dept.parentCode, correctParentCode);
            log.info("修正部门父级: {} -> 新父级: {}", dept.orgName, correctParentCode);
            return FixResult.UPDATED;
        }
        
        return FixResult.NO_CHANGE;
    }

    /**
     * 智能解析 full_name 为层级列表
     */
    private List<String> parseFullNameToHierarchy(String fullName) {
        List<String> levels = new ArrayList<>();
        
        if (fullName == null || fullName.trim().isEmpty()) {
            return levels;
        }
        
        // 处理特殊的公司名称
        if (fullName.contains("江苏金珂水务有限公司")) {
            return parseCompanyHierarchy(fullName, "江苏金珂水务有限公司");
        }
        if (fullName.contains("江苏南钢鑫洋供应链有限公司")) {
            return parseCompanyHierarchy(fullName, "江苏南钢鑫洋供应链有限公司");
        }
        if (fullName.contains("蔚蓝高科技集团")) {
            return parseCompanyHierarchy(fullName, "蔚蓝高科技集团");
        }
        if (fullName.contains("新产业投资集团")) {
            return parseCompanyHierarchy(fullName, "新产业投资集团");
        }
        
        // 标准层级解析
        return parseStandardHierarchy(fullName);
    }

    /**
     * 处理公司层级结构
     */
    private List<String> parseCompanyHierarchy(String fullName, String companyName) {
        List<String> levels = new ArrayList<>();
        
        if (fullName.startsWith(companyName)) {
            levels.add(companyName);
            String remaining = fullName.substring(companyName.length());
            
            if (!remaining.trim().isEmpty()) {
                List<String> remainingLevels = parseStandardHierarchy(remaining.trim());
                levels.addAll(remainingLevels);
            }
        } else {
            // 如果不是以公司名开头，使用标准解析
            return parseStandardHierarchy(fullName);
        }
        
        return levels;
    }

    /**
     * 标准层级解析（改进版，避免错误分割）
     */
    private List<String> parseStandardHierarchy(String text) {
        List<String> levels = new ArrayList<>();
        
        if (text == null || text.trim().isEmpty()) {
            return levels;
        }
        
        // 使用更智能的分割策略
        // 优先识别完整的部门名称模式
        String[] knownPatterns = {
            "事业部", "有限公司", "集团", "研究院", "指挥部", "服务中心"
        };
        
        String remaining = text.trim();
        
        // 首先处理已知的完整模式
        for (String pattern : knownPatterns) {
            if (remaining.contains(pattern)) {
                int index = remaining.indexOf(pattern);
                if (index >= 0) {
                    String level = remaining.substring(0, index + pattern.length());
                    levels.add(level);
                    remaining = remaining.substring(index + pattern.length()).trim();
                    break;
                }
            }
        }
        
        // 然后处理剩余部分
        if (!remaining.isEmpty()) {
            List<String> remainingLevels = parseRemainingHierarchy(remaining);
            levels.addAll(remainingLevels);
        }
        
        // 如果没有分割出任何层级，整个文本作为一个层级
        if (levels.isEmpty()) {
            levels.add(text.trim());
        }
        
        log.debug("解析层级: {} -> {}", text, levels);
        return levels;
    }

    /**
     * 解析剩余的层级结构
     */
    private List<String> parseRemainingHierarchy(String text) {
        List<String> levels = new ArrayList<>();
        
        // 简化的分割策略：基于常见的单字符后缀
        String[] suffixes = {"厂", "车间", "科", "室", "部", "中心", "处", "班", "组", "站", "队", "所"};
        
        String remaining = text;
        
        while (!remaining.isEmpty() && levels.size() < 5) { // 限制最大层级数
            boolean found = false;
            
            for (String suffix : suffixes) {
                int index = remaining.indexOf(suffix);
                if (index > 0 && index < remaining.length() - suffix.length()) {
                    // 确保不是在开头或结尾
                    String level = remaining.substring(0, index + suffix.length());
                    levels.add(level);
                    remaining = remaining.substring(index + suffix.length()).trim();
                    found = true;
                    break;
                }
            }
            
            if (!found) {
                // 没找到更多分割点，剩余部分作为最后一级
                if (!remaining.trim().isEmpty()) {
                    levels.add(remaining.trim());
                }
                break;
            }
        }
        
        return levels;
    }

    /**
     * 获取所有有效部门
     */
    private List<DepartmentRecord> getAllValidDepartments() {
        String sql = """
            SELECT org_code, org_name, parent_code, dept_uuid, full_name, is_history, user_predef_14
            FROM department_sync_test 
            WHERE is_history = 0 AND user_predef_14 != 'D'
            ORDER BY org_code
            """;
        
        return jdbcTemplate.query(sql, new DepartmentRecordRowMapper());
    }

    /**
     * 获取当前部门的实际层级路径
     */
    private List<String> getCurrentHierarchyPath(DepartmentRecord dept) {
        List<String> path = new ArrayList<>();
        
        // 递归向上查找父级部门
        String currentCode = dept.orgCode;
        Set<String> visited = new HashSet<>(); // 防止循环引用
        
        while (currentCode != null && !currentCode.equals("1") && !visited.contains(currentCode)) {
            visited.add(currentCode);
            
            DepartmentRecord currentDept = findDepartmentByCode(currentCode);
            if (currentDept != null) {
                path.add(0, currentDept.orgName); // 添加到开头
                currentCode = currentDept.parentCode;
            } else {
                break;
            }
        }
        
        return path;
    }

    // 数据记录类
    public static class DepartmentRecord {
        public String orgCode;
        public String orgName;
        public String parentCode;
        public String deptUuid;
        public String fullName;
        public Integer isHistory;
        public String userPredef14;
    }

    // RowMapper 实现
    private static class DepartmentRecordRowMapper implements RowMapper<DepartmentRecord> {
        @Override
        public DepartmentRecord mapRow(ResultSet rs, int rowNum) throws SQLException {
            DepartmentRecord record = new DepartmentRecord();
            record.orgCode = rs.getString("org_code");
            record.orgName = rs.getString("org_name");
            record.parentCode = rs.getString("parent_code");
            record.deptUuid = rs.getString("dept_uuid");
            record.fullName = rs.getString("full_name");
            record.isHistory = rs.getInt("is_history");
            record.userPredef14 = rs.getString("user_predef_14");
            return record;
        }
    }

    // 修正结果枚举
    private enum FixResult {
        NO_CHANGE, UPDATED, CREATED, SKIPPED, ERROR
    }

    /**
     * 判断层级关系是否正确
     */
    private boolean isHierarchyCorrect(List<String> expectedHierarchy, List<String> actualHierarchy, String orgName) {
        // 如果期望层级只有1个，应该是一级部门
        if (expectedHierarchy.size() <= 1) {
            return actualHierarchy.size() <= 1;
        }

        // 比较层级路径是否一致（忽略最后一级，因为最后一级是当前部门）
        if (expectedHierarchy.size() != actualHierarchy.size() + 1) {
            return false;
        }

        // 比较除最后一级外的所有层级
        for (int i = 0; i < actualHierarchy.size(); i++) {
            if (!expectedHierarchy.get(i).equals(actualHierarchy.get(i))) {
                return false;
            }
        }

        return true;
    }

    /**
     * 查找或创建正确的父级部门
     */
    private String findOrCreateCorrectParent(DepartmentRecord dept, List<String> expectedHierarchy) {
        if (expectedHierarchy.size() <= 1) {
            return "1"; // 一级部门
        }

        // 获取直接父级名称（倒数第二级）
        String parentName = expectedHierarchy.get(expectedHierarchy.size() - 2);

        // 在数据库中查找该父级部门
        DepartmentRecord parentDept = findDepartmentByName(parentName);

        if (parentDept != null) {
            return parentDept.orgCode;
        } else {
            // 父级不存在，需要创建
            return createMissingParent(parentName, expectedHierarchy);
        }
    }

    /**
     * 根据部门名称查找部门
     */
    private DepartmentRecord findDepartmentByName(String orgName) {
        String sql = """
            SELECT org_code, org_name, parent_code, dept_uuid, full_name, is_history, user_predef_14
            FROM department_sync_test
            WHERE org_name = ? AND is_history = 0 AND user_predef_14 != 'D'
            LIMIT 1
            """;

        try {
            return jdbcTemplate.queryForObject(sql, new DepartmentRecordRowMapper(), orgName);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 根据部门代码查找部门
     */
    private DepartmentRecord findDepartmentByCode(String orgCode) {
        String sql = """
            SELECT org_code, org_name, parent_code, dept_uuid, full_name, is_history, user_predef_14
            FROM department_sync_test
            WHERE org_code = ?
            """;

        try {
            return jdbcTemplate.queryForObject(sql, new DepartmentRecordRowMapper(), orgCode);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 创建缺失的父级部门
     */
    private String createMissingParent(String parentName, List<String> fullHierarchy) {
        // 1. 生成新的 org_code
        String newOrgCode = generateNewOrgCode();

        // 2. 确定这个新部门的父级
        String grandParentCode = findGrandParent(parentName, fullHierarchy);

        // 3. 插入新部门记录
        insertNewDepartment(newOrgCode, parentName, grandParentCode, parentName);

        // 4. 记录日志
        logFixOperation(newOrgCode, parentName, "CREATE", "创建缺失的中间层级部门", null, grandParentCode);

        log.info("创建缺失的父级部门: {} (code: {}, parent: {})", parentName, newOrgCode, grandParentCode);
        return newOrgCode;
    }

    /**
     * 生成新的部门代码
     */
    private String generateNewOrgCode() {
        return "AUTO" + (++newOrgCodeCounter);
    }

    /**
     * 查找祖父级部门
     */
    private String findGrandParent(String parentName, List<String> fullHierarchy) {
        // 找到当前父级在层级中的位置
        int parentIndex = -1;
        for (int i = 0; i < fullHierarchy.size(); i++) {
            if (fullHierarchy.get(i).equals(parentName)) {
                parentIndex = i;
                break;
            }
        }

        if (parentIndex <= 0) {
            return "1"; // 没有祖父级，是一级部门
        }

        // 查找祖父级部门
        String grandParentName = fullHierarchy.get(parentIndex - 1);
        DepartmentRecord grandParent = findDepartmentByName(grandParentName);

        if (grandParent != null) {
            return grandParent.orgCode;
        } else {
            // 祖父级也不存在，递归创建
            return createMissingParent(grandParentName, fullHierarchy);
        }
    }

    /**
     * 插入新部门记录
     */
    private void insertNewDepartment(String orgCode, String orgName, String parentCode, String fullName) {
        String sql = """
            INSERT INTO department_sync_test
            (org_code, org_name, parent_code, dept_uuid, full_name, is_history, user_predef_14)
            VALUES (?, ?, ?, ?, ?, 0, '')
            """;

        String deptUuid = UUID.randomUUID().toString();
        jdbcTemplate.update(sql, orgCode, orgName, parentCode, deptUuid, fullName);
    }

    /**
     * 更新部门的父级代码
     */
    private void updateParentCode(String orgCode, String newParentCode) {
        String sql = "UPDATE department_sync_test SET parent_code = ? WHERE org_code = ?";
        jdbcTemplate.update(sql, newParentCode, orgCode);
    }

    /**
     * 记录修正操作日志
     */
    private void logFixOperation(String orgCode, String orgName, String operationType,
                                String reason, String oldParentCode, String newParentCode) {
        String sql = """
            INSERT INTO hierarchy_fix_log
            (org_code, org_name, operation_type, reason, old_parent_code, new_parent_code)
            VALUES (?, ?, ?, ?, ?, ?)
            """;

        try {
            jdbcTemplate.update(sql, orgCode, orgName, operationType, reason, oldParentCode, newParentCode);
        } catch (Exception e) {
            log.warn("记录修正日志失败: {}", e.getMessage());
        }
    }

    /**
     * 更新报告统计
     */
    private void updateReportStats(FixReport report, FixResult result) {
        switch (result) {
            case UPDATED -> report.fixedDepartments++;
            case CREATED -> report.createdDepartments++;
            case SKIPPED -> report.skippedDepartments++;
            case ERROR -> report.errorDepartments++;
            case NO_CHANGE -> {
                // 无需更新统计，部门层级关系已正确
            }
        }
    }

    /**
     * 验证修正结果
     */
    private void validateFixResults(FixReport report) {
        // 检查孤儿节点
        String orphanSql = """
            SELECT org_name FROM department_sync_test
            WHERE parent_code NOT IN (SELECT org_code FROM department_sync_test)
            AND parent_code != '1'
            AND is_history = 0 AND user_predef_14 != 'D'
            """;

        report.orphanNodes = jdbcTemplate.queryForList(orphanSql, String.class);

        // 计算最大层级深度
        report.maxHierarchyDepth = calculateMaxHierarchyDepth();

        log.info("验证结果: 孤儿节点数={}, 最大层级深度={}", report.orphanNodes.size(), report.maxHierarchyDepth);
    }

    /**
     * 计算最大层级深度
     */
    private int calculateMaxHierarchyDepth() {
        String sql = """
            WITH RECURSIVE dept_levels AS (
                SELECT org_code, org_name, parent_code, 1 as level
                FROM department_sync_test
                WHERE parent_code = '1' AND is_history = 0 AND user_predef_14 != 'D'

                UNION ALL

                SELECT d.org_code, d.org_name, d.parent_code, dl.level + 1
                FROM department_sync_test d
                JOIN dept_levels dl ON d.parent_code = dl.org_code
                WHERE d.is_history = 0 AND d.user_predef_14 != 'D'
            )
            SELECT COALESCE(MAX(level), 0) FROM dept_levels
            """;

        try {
            return jdbcTemplate.queryForObject(sql, Integer.class);
        } catch (Exception e) {
            log.warn("计算最大层级深度失败: {}", e.getMessage());
            return 0;
        }
    }

    // 修正报告类
    public static class FixReport {
        public int totalDepartments = 0;
        public int fixedDepartments = 0;
        public int createdDepartments = 0;
        public int skippedDepartments = 0;
        public int errorDepartments = 0;
        public int maxHierarchyDepth = 0;
        public List<String> orphanNodes = new ArrayList<>();

        @Override
        public String toString() {
            return String.format("FixReport{总部门数=%d, 修正=%d, 创建=%d, 跳过=%d, 错误=%d, 最大层级=%d, 孤儿节点=%d}",
                totalDepartments, fixedDepartments, createdDepartments, skippedDepartments,
                errorDepartments, maxHierarchyDepth, orphanNodes.size());
        }
    }
}
