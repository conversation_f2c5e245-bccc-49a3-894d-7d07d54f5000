package com.nercar.datasynchronization.dto;

import lombok.Data;
import java.util.Date;

/**
 * 员工职称数据传输对象
 * 对应employee_title表，存储员工的职称信息
 */
@Data
public class EmployeeTitleDTO {

    /**
     * 全局唯一标识符
     */
    private String guid;

    /**
     * 关联的员工MDM ID
     */
    private String employeeMdmId;

    /**
     * 职称代码
     */
    private String titleCode;

    /**
     * 职称类型
     */
    private String titleType;

    /**
     * 职称级别
     */
    private String titleLevel;

    /**
     * 职称名称
     */
    private String titleName;

    /**
     * 状态
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新时间
     */
    private Date updatedTime;

    /**
     * 职称类别
     */
    private String titleCategory;

    /**
     * 默认组织编码
     */
    private String defaultOrgCode;

    /**
     * 是否停用状态
     */
    private String isDisabled;
}
