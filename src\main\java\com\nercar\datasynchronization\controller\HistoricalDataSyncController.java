package com.nercar.datasynchronization.controller;

import com.nercar.datasynchronization.client.SoapClient;
import com.nercar.datasynchronization.service.EmployeeSyncService;
import com.nercar.datasynchronization.utils.XmlUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/api/historical")
@Tag(name = "历史数据同步API", description = "提供大规模历史数据同步功能")
public class HistoricalDataSyncController {

    @Autowired
    private SoapClient soapClient;
    
    @Autowired
    private EmployeeSyncService employeeSyncService;
    
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    
    // 声明一个全局的当前任务Map，用于跟踪任务状态
    private final Map<String, Map<String, Object>> taskStatusMap = new ConcurrentHashMap<>();
    
    // 任务ID生成器
    private final AtomicInteger taskIdGenerator = new AtomicInteger(1000);
    
    @GetMapping("/employees/start")
    @Operation(summary = "启动历史员工数据同步", description = "开始大规模历史员工数据同步任务，返回任务ID")
    public ResponseEntity<Map<String, Object>> startHistoricalSync(
            @Parameter(description = "开始时间 (格式: yyyy-MM-dd HH:mm:ss)")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startDate,
            @Parameter(description = "结束时间 (格式: yyyy-MM-dd HH:mm:ss)")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endDate,
            @Parameter(description = "每个分片的天数")
            @RequestParam(defaultValue = "7") int chunkDays,
            @Parameter(description = "并行度")
            @RequestParam(defaultValue = "2") int parallelism,
            @Parameter(description = "每个分片之间的间隔时间(秒)")
            @RequestParam(defaultValue = "10") int chunkIntervalSeconds) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 限制并行度，防止系统过载
            final int actualParallelism = Math.min(parallelism, 2);
            
            // 计算时间分片
            long totalMillis = endDate.getTime() - startDate.getTime();
            long chunkMillis = chunkDays * 24 * 60 * 60 * 1000L;
            int totalChunks = (int) Math.ceil((double) totalMillis / chunkMillis);
            
            log.info("准备开始历史数据同步，总时间范围：{} 至 {}，分片数：{}", 
                    DATE_FORMAT.format(startDate), DATE_FORMAT.format(endDate), totalChunks);
            
            // 生成唯一任务ID
            String taskId = "HIST-" + taskIdGenerator.incrementAndGet();
            
            // 准备时间分片
            List<Map<String, Object>> chunks = new ArrayList<>();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startDate);
            
            for (int i = 0; i < totalChunks; i++) {
                Date chunkStart = calendar.getTime();
                
                calendar.add(Calendar.DATE, chunkDays);
                Date chunkEnd = calendar.getTime();
                
                if (chunkEnd.after(endDate)) {
                    chunkEnd = endDate;
                }
                
                Map<String, Object> chunk = new HashMap<>();
                chunk.put("chunkNumber", i + 1);
                chunk.put("startDate", chunkStart);
                chunk.put("endDate", chunkEnd);
                chunk.put("status", "PENDING");
                chunks.add(chunk);
            }
            
            // 保存任务信息到内存
            Map<String, Object> taskInfo = new HashMap<>();
            taskInfo.put("taskId", taskId);
            taskInfo.put("startDate", startDate);
            taskInfo.put("endDate", endDate);
            taskInfo.put("chunkDays", chunkDays);
            taskInfo.put("parallelism", actualParallelism);
            taskInfo.put("totalChunks", totalChunks);
            taskInfo.put("completedChunks", 0);
            taskInfo.put("failedChunks", 0);
            taskInfo.put("status", "RUNNING");
            taskInfo.put("createdTime", new Date());
            taskInfo.put("lastUpdatedTime", new Date());
            taskInfo.put("chunks", chunks);
            taskInfo.put("chunkIntervalSeconds", chunkIntervalSeconds);
            
            taskStatusMap.put(taskId, taskInfo);
            
            // 启动异步处理线程
            CompletableFuture.runAsync(() -> {
                processHistoricalSyncTask(taskId, chunks, actualParallelism, chunkIntervalSeconds);
            });
            
            result.put("success", true);
            result.put("taskId", taskId);
            result.put("message", "历史数据同步任务已启动，可通过任务ID查询进度");
            result.put("totalChunks", totalChunks);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("启动历史数据同步任务失败", e);
            result.put("success", false);
            result.put("message", "启动历史数据同步任务失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }
    
    @GetMapping("/employees/status/{taskId}")
    @Operation(summary = "查询历史数据同步任务状态", description = "根据任务ID查询历史数据同步任务的执行状态")
    public ResponseEntity<Map<String, Object>> getTaskStatus(
            @PathVariable String taskId,
            @RequestParam(defaultValue = "false") boolean includeDetails) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            Map<String, Object> taskInfo = taskStatusMap.get(taskId);
            
            if (taskInfo != null) {
                result.put("taskId", taskInfo.get("taskId"));
                result.put("status", taskInfo.get("status"));
                result.put("totalChunks", taskInfo.get("totalChunks"));
                result.put("completedChunks", taskInfo.get("completedChunks"));
                result.put("failedChunks", taskInfo.get("failedChunks"));
                result.put("startDate", taskInfo.get("startDate"));
                result.put("endDate", taskInfo.get("endDate"));
                result.put("createdTime", taskInfo.get("createdTime"));
                result.put("lastUpdatedTime", taskInfo.get("lastUpdatedTime"));
                
                if (includeDetails) {
                    result.put("chunkDetails", taskInfo.get("chunks"));
                }
                
                return ResponseEntity.ok(result);
            } else {
                result.put("success", false);
                result.put("message", "未找到任务ID: " + taskId);
                return ResponseEntity.ok(result);
            }
            
        } catch (Exception e) {
            log.error("查询任务状态失败", e);
            result.put("success", false);
            result.put("message", "查询任务状态失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }
    
    @PostMapping("/employees/retry/{taskId}")
    @Operation(summary = "重试失败的分片", description = "重新执行指定任务中失败的分片")
    public ResponseEntity<Map<String, Object>> retryFailedChunks(@PathVariable String taskId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Map<String, Object> taskInfo = taskStatusMap.get(taskId);
            
            if (taskInfo != null) {
                String status = (String) taskInfo.get("status");
                
                if ("COMPLETED".equals(status) || "FAILED".equals(status)) {
                    // 将任务状态改为RUNNING
                    taskInfo.put("status", "RUNNING");
                    taskInfo.put("lastUpdatedTime", new Date());
                    
                    // 获取失败的分片
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> chunks = (List<Map<String, Object>>) taskInfo.get("chunks");
                    
                    // 过滤出失败的分片
                    List<Map<String, Object>> failedChunks = chunks.stream()
                            .filter(chunk -> "FAILED".equals(chunk.get("status")))
                            .collect(Collectors.toList());
                    
                    if (failedChunks.isEmpty()) {
                        taskInfo.put("status", "COMPLETED");
                        
                        result.put("success", true);
                        result.put("message", "没有失败的分片需要重试");
                        return ResponseEntity.ok(result);
                    }
                    
                    // 重置失败分片的状态
                    for (Map<String, Object> chunk : failedChunks) {
                        chunk.put("status", "PENDING");
                        Integer retryCount = (Integer) chunk.getOrDefault("retryCount", 0);
                        chunk.put("retryCount", retryCount + 1);
                    }
                    
                    // 启动异步处理线程
                    int parallelism = (Integer) taskInfo.get("parallelism");
                    int chunkIntervalSeconds = (Integer) taskInfo.get("chunkIntervalSeconds");
                    
                    CompletableFuture.runAsync(() -> {
                        processHistoricalSyncTask(taskId, failedChunks, parallelism, chunkIntervalSeconds);
                    });
                    
                    result.put("success", true);
                    result.put("message", "已开始重试失败分片，共" + failedChunks.size() + "个");
                    return ResponseEntity.ok(result);
                } else {
                    result.put("success", false);
                    result.put("message", "任务当前状态为" + status + "，无法重试");
                    return ResponseEntity.ok(result);
                }
            } else {
                result.put("success", false);
                result.put("message", "未找到任务ID: " + taskId);
                return ResponseEntity.ok(result);
            }
            
        } catch (Exception e) {
            log.error("重试失败分片失败", e);
            result.put("success", false);
            result.put("message", "重试失败分片失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }
    
    /**
     * 处理历史数据同步任务
     */
    private void processHistoricalSyncTask(String taskId, List<Map<String, Object>> chunks, 
                                           int parallelism, int chunkIntervalSeconds) {
        log.info("开始处理历史数据同步任务: {}, 分片数: {}, 并行度: {}", 
                taskId, chunks.size(), parallelism);
        
        // 创建可恢复的线程池，容纳最大并行度
        ExecutorService executor = new ThreadPoolExecutor(
            parallelism, 
            parallelism,
            30L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(chunks.size()),
            Executors.defaultThreadFactory(),
            new ThreadPoolExecutor.CallerRunsPolicy()
        );
        
        CompletionService<Map<String, Object>> completionService = 
                new ExecutorCompletionService<>(executor);
        
        List<Future<Map<String, Object>>> futures = new ArrayList<>();
        AtomicInteger activeChunks = new AtomicInteger(0);
        
        try {
            Map<String, Object> taskInfo = taskStatusMap.get(taskId);
            if (taskInfo == null) {
                log.error("任务不存在: {}", taskId);
                return;
            }
            
            // 对所有分片排序
            chunks.sort(Comparator.comparingInt(c -> (Integer) c.get("chunkNumber")));
            
            // 分批提交任务，控制并行度
            for (Map<String, Object> chunk : chunks) {
                int chunkNumber = (Integer) chunk.get("chunkNumber");
                Date startDate = (Date) chunk.get("startDate");
                Date endDate = (Date) chunk.get("endDate");
                
                // 确保每个分片的状态都是PENDING
                if (!"PENDING".equals(chunk.get("status"))) {
                    log.info("跳过非PENDING状态的分片 {}: {}", chunkNumber, chunk.get("status"));
                    continue;
                }
                
                // 等待活动分片数小于并行度
                while (activeChunks.get() >= parallelism) {
                    try {
                        Thread.sleep(1000);
                        
                        // 检查已完成的任务
                        Future<Map<String, Object>> completedFuture = completionService.poll();
                        if (completedFuture != null) {
                            activeChunks.decrementAndGet();
                            futures.remove(completedFuture);
                            
                            // 处理结果
                            try {
                                Map<String, Object> result = completedFuture.get(5, TimeUnit.SECONDS);
                                updateTaskStatus(taskId, result);
                            } catch (Exception e) {
                                log.error("获取分片结果失败", e);
                            }
                        }
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
                
                // 提交新的分片任务
                final int currentChunkNumber = chunkNumber;
                final Date finalChunkStart = startDate;
                final Date finalChunkEnd = endDate;
                
                Callable<Map<String, Object>> task = () -> {
                    Map<String, Object> chunkResult = new HashMap<>();
                    chunkResult.put("chunkNumber", currentChunkNumber);
                    chunkResult.put("startDate", finalChunkStart);
                    chunkResult.put("endDate", finalChunkEnd);
                    
                    try {
                        log.info("开始处理历史数据分片 {}: {} 至 {}", 
                                currentChunkNumber, DATE_FORMAT.format(finalChunkStart), DATE_FORMAT.format(finalChunkEnd));
                        
                        long chunkStartTime = System.currentTimeMillis();
                        chunk.put("status", "PROCESSING");
                        updateChunkStatus(taskId, chunks);
                        
                        // 调用SOAP接口获取数据
                        String userXmlResponse = soapClient.getUserInfo(finalChunkStart, finalChunkEnd);
                        String userXmlData = XmlUtils.extractXmlData(userXmlResponse, "GetUserInfoFromMDMResult");
                        
                        if (userXmlData != null && !userXmlData.isEmpty()) {
                            // 同步员工数据
                            try {
                                employeeSyncService.syncEmployees(userXmlData);
                                
                                long duration = System.currentTimeMillis() - chunkStartTime;
                                chunkResult.put("success", true);
                                chunkResult.put("status", "COMPLETED");
                                chunkResult.put("processingTime", duration);
                                chunkResult.put("message", "分片同步成功");
                                
                                log.info("历史数据分片 {} 同步成功，耗时: {}ms", 
                                        currentChunkNumber, duration);
                            } finally {
                                // 手动触发GC释放内存
                                System.gc();
                                Thread.sleep(100);
                            }
                        } else {
                            chunkResult.put("success", false);
                            chunkResult.put("status", "FAILED");
                            chunkResult.put("processingTime", System.currentTimeMillis() - chunkStartTime);
                            chunkResult.put("message", "未获取到数据或数据为空");
                            
                            log.warn("历史数据分片 {} 未获取到数据", currentChunkNumber);
                        }
                    } catch (Exception e) {
                        log.error("处理历史数据分片 {} 失败: {}", currentChunkNumber, e.getMessage(), e);
                        
                        chunkResult.put("success", false);
                        chunkResult.put("status", "FAILED");
                        chunkResult.put("message", "处理失败: " + e.getMessage());
                    } finally {
                        activeChunks.decrementAndGet();
                    }
                    
                    return chunkResult;
                };
                
                // 提交任务并添加到Future列表
                Future<Map<String, Object>> future = completionService.submit(task);
                futures.add(future);
                activeChunks.incrementAndGet();
                
                // 每个分片间隔一定时间，避免过载
                try {
                    Thread.sleep(chunkIntervalSeconds * 1000L);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
            
            // 等待所有任务完成
            for (int i = 0; i < futures.size(); i++) {
                try {
                    Future<Map<String, Object>> future = completionService.take();
                    Map<String, Object> result = future.get(60, TimeUnit.SECONDS);
                    updateTaskStatus(taskId, result);
                } catch (Exception e) {
                    log.error("获取分片结果失败", e);
                }
            }
            
            // 更新最终任务状态
            taskInfo = taskStatusMap.get(taskId);
            if (taskInfo != null) {
                int failedChunks = (Integer) taskInfo.get("failedChunks");
                
                if (failedChunks == 0) {
                    taskInfo.put("status", "COMPLETED");
                } else {
                    taskInfo.put("status", "FAILED");
                }
                
                taskInfo.put("lastUpdatedTime", new Date());
                
                log.info("历史数据同步任务 {} 已完成，状态: {}, 成功分片: {}, 失败分片: {}",
                        taskId, taskInfo.get("status"), taskInfo.get("completedChunks"), failedChunks);
            }
            
        } catch (Exception e) {
            log.error("处理历史数据同步任务失败", e);
            
            // 更新任务状态为失败
            Map<String, Object> taskInfo = taskStatusMap.get(taskId);
            if (taskInfo != null) {
                taskInfo.put("status", "FAILED");
                taskInfo.put("lastUpdatedTime", new Date());
            }
        } finally {
            // 关闭线程池
            try {
                executor.shutdown();
                if (!executor.awaitTermination(30, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
    
    /**
     * 更新任务状态
     */
    private void updateTaskStatus(String taskId, Map<String, Object> chunkResult) {
        try {
            Map<String, Object> taskInfo = taskStatusMap.get(taskId);
            if (taskInfo != null) {
                // 获取分片列表
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> chunks = (List<Map<String, Object>>) taskInfo.get("chunks");
                
                // 更新特定分片状态
                int chunkNumber = (Integer) chunkResult.get("chunkNumber");
                for (Map<String, Object> chunk : chunks) {
                    if (chunkNumber == (Integer) chunk.get("chunkNumber")) {
                        chunk.put("status", chunkResult.get("status"));
                        chunk.put("message", chunkResult.get("message"));
                        if (chunkResult.containsKey("processingTime")) {
                            chunk.put("processingTime", chunkResult.get("processingTime"));
                        }
                        break;
                    }
                }
                
                // 更新完成和失败的分片计数
                int completedCount = (int) chunks.stream()
                        .filter(c -> "COMPLETED".equals(c.get("status")))
                        .count();
                int failedCount = (int) chunks.stream()
                        .filter(c -> "FAILED".equals(c.get("status")))
                        .count();
                
                taskInfo.put("completedChunks", completedCount);
                taskInfo.put("failedChunks", failedCount);
                taskInfo.put("lastUpdatedTime", new Date());
            }
        } catch (Exception e) {
            log.error("更新任务状态失败: {}", taskId, e);
        }
    }
    
    /**
     * 更新分片状态
     */
    private void updateChunkStatus(String taskId, List<Map<String, Object>> chunks) {
        try {
            Map<String, Object> taskInfo = taskStatusMap.get(taskId);
            if (taskInfo != null) {
                taskInfo.put("chunks", chunks);
                taskInfo.put("lastUpdatedTime", new Date());
            }
        } catch (Exception e) {
            log.error("更新分片状态失败: {}", taskId, e);
        }
    }
}