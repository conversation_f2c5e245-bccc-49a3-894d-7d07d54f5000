package com.nercar.datasynchronization.service;

import com.nercar.datasynchronization.entity.DepartmentSyncTest;
import com.nercar.datasynchronization.repository.DepartmentSyncTestRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据完整性检查服务
 * 用于检查department_sync_test表中的数据是否能构成完整的树结构
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataIntegrityCheckService {

    private final DepartmentSyncTestRepository repository;

    /**
     * 执行完整的数据完整性检查
     */
    public Map<String, Object> performFullIntegrityCheck() {
        log.info("开始执行数据完整性检查...");
        
        Map<String, Object> result = new HashMap<>();
        
        // 1. 基础统计
        result.put("basicStats", getBasicStats());
        
        // 2. 孤儿节点检查
        result.put("orphanCheck", checkOrphanNodes());
        
        // 3. 根节点检查
        result.put("rootCheck", checkRootNodes());
        
        // 4. 循环引用检查
        result.put("cycleCheck", checkCyclicReferences());
        
        // 5. 树结构深度分析
        result.put("depthAnalysis", analyzeTreeDepth());
        
        // 6. 父级编码分布
        result.put("parentCodeDistribution", analyzeParentCodeDistribution());
        
        // 7. 最终评估
        result.put("finalAssessment", getFinalAssessment(result));
        
        log.info("数据完整性检查完成");
        return result;
    }

    /**
     * 获取基础统计信息
     */
    private Map<String, Object> getBasicStats() {
        Map<String, Object> stats = new HashMap<>();
        
        long totalCount = repository.count();
        long activeCount = repository.countByIsHistory(0) - 
                          repository.findAll().stream()
                          .filter(d -> d.getIsHistory() == 0 && "D".equals(d.getUserPredef14()))
                          .count();
        long historyCount = repository.countByIsHistory(1);
        long deletedCount = repository.countByUserPredef14("D");
        
        stats.put("totalRecords", totalCount);
        stats.put("activeRecords", activeCount);
        stats.put("historyRecords", historyCount);
        stats.put("deletedRecords", deletedCount);
        
        log.info("基础统计 - 总记录数: {}, 正常记录数: {}, 历史记录数: {}, 删除记录数: {}", 
                totalCount, activeCount, historyCount, deletedCount);
        
        return stats;
    }

    /**
     * 检查孤儿节点
     */
    private Map<String, Object> checkOrphanNodes() {
        Map<String, Object> orphanCheck = new HashMap<>();
        
        // 获取所有正常状态的部门
        List<DepartmentSyncTest> activeDepts = repository.findAll().stream()
                .filter(d -> d.getIsHistory() == 0 && !"D".equals(d.getUserPredef14()))
                .collect(Collectors.toList());
        
        // 创建组织编码集合
        Set<String> orgCodes = activeDepts.stream()
                .map(DepartmentSyncTest::getOrgCode)
                .collect(Collectors.toSet());
        
        // 查找孤儿节点
        List<DepartmentSyncTest> orphans = activeDepts.stream()
                .filter(d -> d.getParentCode() != null 
                        && !d.getParentCode().isEmpty() 
                        && !"1".equals(d.getParentCode())  // 排除根节点标识符
                        && !orgCodes.contains(d.getParentCode()))
                .collect(Collectors.toList());
        
        orphanCheck.put("orphanCount", orphans.size());
        orphanCheck.put("orphanNodes", orphans.stream()
                .map(d -> Map.of(
                        "orgCode", d.getOrgCode(),
                        "orgName", d.getOrgName(),
                        "parentCode", d.getParentCode(),
                        "issue", "父级编码不存在"
                ))
                .collect(Collectors.toList()));
        
        log.info("孤儿节点检查 - 发现 {} 个孤儿节点", orphans.size());
        
        return orphanCheck;
    }

    /**
     * 检查根节点
     */
    private Map<String, Object> checkRootNodes() {
        Map<String, Object> rootCheck = new HashMap<>();
        
        List<DepartmentSyncTest> rootNodes = repository.findAll().stream()
                .filter(d -> d.getIsHistory() == 0 && !"D".equals(d.getUserPredef14()))
                .filter(d -> d.getParentCode() == null 
                        || d.getParentCode().isEmpty() 
                        || "1".equals(d.getParentCode()))
                .collect(Collectors.toList());
        
        rootCheck.put("rootCount", rootNodes.size());
        rootCheck.put("rootNodes", rootNodes.stream()
                .map(d -> Map.of(
                        "orgCode", d.getOrgCode(),
                        "orgName", d.getOrgName(),
                        "parentCode", d.getParentCode() == null ? "NULL" : d.getParentCode()
                ))
                .collect(Collectors.toList()));
        
        log.info("根节点检查 - 发现 {} 个根节点", rootNodes.size());
        
        return rootCheck;
    }

    /**
     * 检查循环引用
     */
    private Map<String, Object> checkCyclicReferences() {
        Map<String, Object> cycleCheck = new HashMap<>();
        
        List<DepartmentSyncTest> cycles = repository.findAll().stream()
                .filter(d -> d.getIsHistory() == 0 && !"D".equals(d.getUserPredef14()))
                .filter(d -> d.getOrgCode().equals(d.getParentCode()))
                .collect(Collectors.toList());
        
        cycleCheck.put("cycleCount", cycles.size());
        cycleCheck.put("cyclicNodes", cycles.stream()
                .map(d -> Map.of(
                        "orgCode", d.getOrgCode(),
                        "orgName", d.getOrgName(),
                        "issue", "自引用循环"
                ))
                .collect(Collectors.toList()));
        
        log.info("循环引用检查 - 发现 {} 个循环引用", cycles.size());
        
        return cycleCheck;
    }

    /**
     * 分析树结构深度
     */
    private Map<String, Object> analyzeTreeDepth() {
        Map<String, Object> depthAnalysis = new HashMap<>();
        
        // 获取所有正常状态的部门
        List<DepartmentSyncTest> activeDepts = repository.findAll().stream()
                .filter(d -> d.getIsHistory() == 0 && !"D".equals(d.getUserPredef14()))
                .collect(Collectors.toList());
        
        // 构建父子关系映射
        Map<String, List<String>> parentToChildren = new HashMap<>();
        Map<String, String> childToParent = new HashMap<>();
        
        for (DepartmentSyncTest dept : activeDepts) {
            String orgCode = dept.getOrgCode();
            String parentCode = dept.getParentCode();
            
            if (parentCode != null && !parentCode.isEmpty() && !"1".equals(parentCode)) {
                parentToChildren.computeIfAbsent(parentCode, k -> new ArrayList<>()).add(orgCode);
                childToParent.put(orgCode, parentCode);
            }
        }
        
        // 计算最大深度
        int maxDepth = 0;
        Map<Integer, Integer> levelCounts = new HashMap<>();
        
        // 从根节点开始计算深度
        List<String> rootCodes = activeDepts.stream()
                .filter(d -> d.getParentCode() == null 
                        || d.getParentCode().isEmpty() 
                        || "1".equals(d.getParentCode()))
                .map(DepartmentSyncTest::getOrgCode)
                .collect(Collectors.toList());
        
        for (String rootCode : rootCodes) {
            int depth = calculateDepth(rootCode, parentToChildren, new HashSet<>());
            maxDepth = Math.max(maxDepth, depth);
        }
        
        depthAnalysis.put("maxDepth", maxDepth);
        depthAnalysis.put("rootNodesCount", rootCodes.size());
        depthAnalysis.put("canFormCompleteTree", maxDepth > 0);
        
        log.info("树深度分析 - 最大深度: {}, 根节点数: {}", maxDepth, rootCodes.size());
        
        return depthAnalysis;
    }

    /**
     * 递归计算树的深度
     */
    private int calculateDepth(String nodeCode, Map<String, List<String>> parentToChildren, Set<String> visited) {
        if (visited.contains(nodeCode)) {
            return 0; // 防止循环引用
        }
        
        visited.add(nodeCode);
        
        List<String> children = parentToChildren.get(nodeCode);
        if (children == null || children.isEmpty()) {
            return 1;
        }
        
        int maxChildDepth = 0;
        for (String child : children) {
            int childDepth = calculateDepth(child, parentToChildren, new HashSet<>(visited));
            maxChildDepth = Math.max(maxChildDepth, childDepth);
        }
        
        return maxChildDepth + 1;
    }

    /**
     * 分析父级编码分布
     */
    private Map<String, Object> analyzeParentCodeDistribution() {
        Map<String, Object> distribution = new HashMap<>();
        
        List<DepartmentSyncTest> activeDepts = repository.findAll().stream()
                .filter(d -> d.getIsHistory() == 0 && !"D".equals(d.getUserPredef14()))
                .collect(Collectors.toList());
        
        Map<String, Long> parentCodeCounts = activeDepts.stream()
                .collect(Collectors.groupingBy(
                        d -> {
                            String parentCode = d.getParentCode();
                            if (parentCode == null) return "NULL";
                            if (parentCode.isEmpty()) return "EMPTY";
                            if ("1".equals(parentCode)) return "ROOT_IDENTIFIER(1)";
                            if (parentCode.startsWith("X")) return "X_PREFIX";
                            return "OTHER";
                        },
                        Collectors.counting()
                ));
        
        distribution.put("parentCodeTypes", parentCodeCounts);
        
        log.info("父级编码分布: {}", parentCodeCounts);
        
        return distribution;
    }

    /**
     * 获取最终评估结果
     */
    private Map<String, Object> getFinalAssessment(Map<String, Object> checkResults) {
        Map<String, Object> assessment = new HashMap<>();
        
        @SuppressWarnings("unchecked")
        Map<String, Object> orphanCheck = (Map<String, Object>) checkResults.get("orphanCheck");
        @SuppressWarnings("unchecked")
        Map<String, Object> cycleCheck = (Map<String, Object>) checkResults.get("cycleCheck");
        @SuppressWarnings("unchecked")
        Map<String, Object> depthAnalysis = (Map<String, Object>) checkResults.get("depthAnalysis");
        
        int orphanCount = (Integer) orphanCheck.get("orphanCount");
        int cycleCount = (Integer) cycleCheck.get("cycleCount");
        boolean canFormTree = (Boolean) depthAnalysis.get("canFormCompleteTree");
        
        boolean isDataComplete = orphanCount == 0 && cycleCount == 0 && canFormTree;
        
        assessment.put("canFormCompleteTree", isDataComplete);
        assessment.put("issues", new ArrayList<String>());
        
        @SuppressWarnings("unchecked")
        List<String> issues = (List<String>) assessment.get("issues");
        
        if (orphanCount > 0) {
            issues.add("存在 " + orphanCount + " 个孤儿节点");
        }
        if (cycleCount > 0) {
            issues.add("存在 " + cycleCount + " 个循环引用");
        }
        if (!canFormTree) {
            issues.add("无法构成完整的树结构");
        }
        
        String conclusion = isDataComplete ? 
                "✅ 数据完整，可以构成完整的树结构" : 
                "❌ 数据存在问题，无法构成完整的树结构";
        
        assessment.put("conclusion", conclusion);
        
        log.info("最终评估: {}", conclusion);
        
        return assessment;
    }
}
