package com.nercar.datasynchronization.service;

import com.nercar.datasynchronization.entity.Department;
import com.nercar.datasynchronization.repository.DepartmentRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据诊断服务
 * 用于分析部门数据同步不全的问题
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DataDiagnosticService {

    private final DepartmentRepository departmentRepository;

    /**
     * 执行完整的数据诊断分析
     */
    public Map<String, Object> performFullDiagnosis() {
        log.info("开始执行数据诊断分析...");
        
        Map<String, Object> result = new HashMap<>();
        
        // 1. 基础统计
        result.put("basicStats", getBasicStatistics());
        
        // 2. 状态分析
        result.put("statusAnalysis", getStatusAnalysis());
        
        // 3. 层级关系分析
        result.put("hierarchyAnalysis", getHierarchyAnalysis());
        
        // 4. 数据质量问题
        result.put("dataQualityIssues", getDataQualityIssues());
        
        // 5. 可能遗漏的数据
        result.put("possiblyMissingData", getPossiblyMissingData());
        
        log.info("数据诊断分析完成");
        return result;
    }

    /**
     * 获取基础统计信息
     */
    public Map<String, Object> getBasicStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        // 总部门数
        long totalCount = departmentRepository.count();
        stats.put("totalDepartments", totalCount);
        
        // 正常部门数（我们当前的过滤条件）
        long activeCount = departmentRepository.countActiveDepartments();
        stats.put("activeDepartments", activeCount);
        
        // 历史部门数
        long historyCount = departmentRepository.countHistoryDepartments();
        stats.put("historyDepartments", historyCount);
        
        // 删除状态部门数
        long deletedCount = departmentRepository.countDeletedDepartments();
        stats.put("deletedDepartments", deletedCount);
        
        // 既是历史又是删除的部门数
        long historyAndDeletedCount = departmentRepository.countHistoryAndDeletedDepartments();
        stats.put("historyAndDeletedDepartments", historyAndDeletedCount);
        
        // 计算覆盖率
        double coverageRate = totalCount > 0 ? (double) activeCount / totalCount * 100 : 0;
        stats.put("coverageRate", String.format("%.2f%%", coverageRate));
        
        log.info("基础统计 - 总数: {}, 正常: {}, 历史: {}, 删除: {}, 覆盖率: {:.2f}%", 
                totalCount, activeCount, historyCount, deletedCount, coverageRate);
        
        return stats;
    }

    /**
     * 获取状态分析
     */
    public Map<String, Object> getStatusAnalysis() {
        Map<String, Object> analysis = new HashMap<>();
        
        // 按状态分组统计
        List<Object[]> statusCounts = departmentRepository.countByStatus();
        Map<String, Long> statusMap = new HashMap<>();
        
        for (Object[] row : statusCounts) {
            Integer isHistory = (Integer) row[0];
            String userPredef14 = (String) row[1];
            Long count = (Long) row[2];
            
            String statusKey = String.format("isHistory=%d,userPredef14=%s", 
                    isHistory != null ? isHistory : -1, 
                    userPredef14 != null ? userPredef14 : "NULL");
            statusMap.put(statusKey, count);
        }
        
        analysis.put("statusDistribution", statusMap);
        
        // 查找可能被误过滤的数据
        List<Department> possiblyMissing = departmentRepository.findPossiblyMissingDepartments();
        analysis.put("possiblyMissingCount", possiblyMissing.size());
        analysis.put("possiblyMissingDepartments", possiblyMissing.stream()
                .map(d -> Map.of(
                        "orgCode", d.getOrgCode(),
                        "orgName", d.getOrgName(),
                        "isHistory", d.getIsHistory(),
                        "userPredef14", d.getUserPredef14()
                ))
                .collect(Collectors.toList()));
        
        return analysis;
    }

    /**
     * 获取层级关系分析
     */
    public Map<String, Object> getHierarchyAnalysis() {
        Map<String, Object> analysis = new HashMap<>();
        
        // 查找根节点候选
        List<Department> rootCandidates = departmentRepository.findRootCandidates();
        analysis.put("rootCandidatesCount", rootCandidates.size());
        analysis.put("rootCandidates", rootCandidates.stream()
                .map(d -> Map.of(
                        "orgCode", d.getOrgCode(),
                        "orgName", d.getOrgName(),
                        "parentCode", d.getParentCode() != null ? d.getParentCode() : "NULL",
                        "isHistory", d.getIsHistory(),
                        "userPredef14", d.getUserPredef14()
                ))
                .collect(Collectors.toList()));
        
        // 查找孤儿部门
        List<Department> orphanDepartments = departmentRepository.findOrphanDepartments();
        analysis.put("orphanDepartmentsCount", orphanDepartments.size());
        analysis.put("orphanDepartments", orphanDepartments.stream()
                .map(d -> Map.of(
                        "orgCode", d.getOrgCode(),
                        "orgName", d.getOrgName(),
                        "parentCode", d.getParentCode(),
                        "reason", "父级代码在正常数据中不存在"
                ))
                .collect(Collectors.toList()));
        
        return analysis;
    }

    /**
     * 获取数据质量问题
     */
    public Map<String, Object> getDataQualityIssues() {
        Map<String, Object> issues = new HashMap<>();
        
        // 检查重复的组织代码
        List<Department> allDepartments = departmentRepository.findAllDepartmentsForAnalysis();
        Map<String, List<Department>> duplicateOrgCodes = allDepartments.stream()
                .collect(Collectors.groupingBy(Department::getOrgCode))
                .entrySet().stream()
                .filter(entry -> entry.getValue().size() > 1)
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue
                ));
        
        issues.put("duplicateOrgCodesCount", duplicateOrgCodes.size());
        issues.put("duplicateOrgCodes", duplicateOrgCodes.entrySet().stream()
                .map(entry -> Map.of(
                        "orgCode", entry.getKey(),
                        "count", entry.getValue().size(),
                        "departments", entry.getValue().stream()
                                .map(d -> Map.of(
                                        "id", d.getId(),
                                        "orgName", d.getOrgName(),
                                        "isHistory", d.getIsHistory(),
                                        "userPredef14", d.getUserPredef14(),
                                        "updateTime", d.getUpdateTime()
                                ))
                                .collect(Collectors.toList())
                ))
                .collect(Collectors.toList()));
        
        return issues;
    }

    /**
     * 获取可能遗漏的数据
     */
    public Map<String, Object> getPossiblyMissingData() {
        Map<String, Object> missing = new HashMap<>();
        
        // 分析：如果我们放宽过滤条件，会增加多少数据
        
        // 1. 只过滤历史数据，不过滤删除标记
        List<Department> onlyHistoryFiltered = departmentRepository.findAll().stream()
                .filter(d -> d.getIsHistory() == 0)
                .collect(Collectors.toList());
        
        long currentCount = departmentRepository.countActiveDepartments();
        long onlyHistoryCount = onlyHistoryFiltered.size();
        
        missing.put("currentFilteredCount", currentCount);
        missing.put("onlyHistoryFilteredCount", onlyHistoryCount);
        missing.put("potentialAdditionalData", onlyHistoryCount - currentCount);
        
        // 2. 分析这些额外数据的特征
        List<Department> additionalData = onlyHistoryFiltered.stream()
                .filter(d -> !"D".equals(d.getUserPredef14()))
                .collect(Collectors.toList());
        
        missing.put("additionalDataSample", additionalData.stream()
                .limit(10)
                .map(d -> Map.of(
                        "orgCode", d.getOrgCode(),
                        "orgName", d.getOrgName(),
                        "parentCode", d.getParentCode() != null ? d.getParentCode() : "NULL",
                        "userPredef14", d.getUserPredef14(),
                        "updateTime", d.getUpdateTime()
                ))
                .collect(Collectors.toList()));
        
        return missing;
    }

    /**
     * 分析特定组织代码的历史变更
     */
    public Map<String, Object> analyzeOrgCodeHistory(String orgCode) {
        List<Department> history = departmentRepository.findAllByOrgCode(orgCode);
        
        Map<String, Object> analysis = new HashMap<>();
        analysis.put("orgCode", orgCode);
        analysis.put("totalRecords", history.size());
        analysis.put("history", history.stream()
                .map(d -> Map.of(
                        "id", d.getId(),
                        "orgName", d.getOrgName(),
                        "parentCode", d.getParentCode() != null ? d.getParentCode() : "NULL",
                        "isHistory", d.getIsHistory(),
                        "userPredef14", d.getUserPredef14(),
                        "updateTime", d.getUpdateTime()
                ))
                .collect(Collectors.toList()));
        
        return analysis;
    }
}
