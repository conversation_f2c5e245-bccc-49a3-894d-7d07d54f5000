package com.nercar.datasynchronization.utils;

import lombok.extern.slf4j.Slf4j;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.springframework.util.StringUtils;
import org.xml.sax.Attributes;
import org.xml.sax.InputSource;
import org.xml.sax.SAXException;
import org.xml.sax.helpers.DefaultHandler;

import javax.xml.parsers.SAXParser;
import javax.xml.parsers.SAXParserFactory;
import java.io.StringReader;
import java.util.function.Consumer;

@Slf4j
public class XmlUtils {

    /**
     * 解析SOAP响应，提取结果XML
     *
     * @param soapResponse SOAP响应字符串
     * @param resultTagName 结果标签名称
     * @return 提取的XML字符串
     */
    public static String extractXmlData(String soapResponse, String resultTagName) {
        if (soapResponse == null || soapResponse.isEmpty()) {
            log.error("SOAP响应为空");
            return null;
        }

        // 检查是否是错误响应（JSON格式）
        String trimmedResponse = soapResponse.trim();
        if (trimmedResponse.startsWith("{")) {
            log.error("远程服务返回错误响应: {}", soapResponse);

            // 尝试解析错误信息
            if (soapResponse.contains("\"retType\": \"E\"")) {
                String errorCode = extractJsonValue(soapResponse, "retCode");
                String errorMessage = extractJsonValue(soapResponse, "retMessage");
                throw new RuntimeException(String.format("远程MDM服务调用失败 - 错误码: %s, 错误信息: %s",
                        errorCode, errorMessage));
            } else {
                throw new RuntimeException("远程MDM服务返回非XML格式响应: " + soapResponse);
            }
        }

        try {
            // 记录原始响应的部分内容和长度
            String logResponse = soapResponse.length() > 200 ?
                    soapResponse.substring(0, 200) + "..." : soapResponse;
            log.debug("开始提取XML数据，SOAP响应长度: {} 字符，前200字符: {}", soapResponse.length(), logResponse);

            // 检查XML数据的完整性
            if (!soapResponse.trim().endsWith("</soap:Envelope>") && !soapResponse.trim().endsWith("</Envelope>")) {
                log.error("XML数据可能不完整，末尾不是预期的结束标签。数据长度: {}, 末尾100字符: {}",
                    soapResponse.length(),
                    soapResponse.length() > 100 ? soapResponse.substring(soapResponse.length() - 100) : soapResponse);
                return null;
            }

            Document document = DocumentHelper.parseText(soapResponse);
            Element envelope = document.getRootElement();

            if (envelope == null) {
                log.error("无法获取SOAP Envelope元素");
                return null;
            }

            // 命名空间处理
            String soapNamespace = envelope.getNamespaceURI();
            log.debug("SOAP命名空间: {}", soapNamespace);

            Element body = envelope.element("Body");
            if (body == null) {
                // 尝试使用命名空间
                body = envelope.element(DocumentHelper.createQName("Body", envelope.getNamespace()));
            }

            if (body == null) {
                log.error("无法获取SOAP Body元素");
                return null;
            }

            log.debug("成功获取SOAP Body元素");

            if (body.elements().isEmpty()) {
                log.error("SOAP Body元素为空");
                return null;
            }

            Element responseElement = (Element) body.elements().get(0);
            log.debug("响应元素名称: {}", responseElement.getName());

            // 检查是否为Fault响应
            if ("Fault".equals(responseElement.getName())) {
                Element faultString = responseElement.element("faultstring");
                if (faultString != null) {
                    log.error("SOAP Fault: {}", faultString.getText());
                } else {
                    log.error("SOAP Fault响应，但未找到faultstring元素");
                }
                return null;
            }

            if (responseElement != null) {
                Element resultElement = responseElement.element(resultTagName);

                if (resultElement != null) {
                    String result = resultElement.getText();
                    if (result != null && !result.isEmpty()) {
                        // 记录提取结果的部分内容
                        String logResult = result.length() > 100 ?
                                result.substring(0, 100) + "..." : result;
                        log.debug("成功提取XML数据(前100字符): {}", logResult);
                        return result;
                    } else {
                        log.warn("结果元素内容为空: {}", resultTagName);
                    }
                } else {
                    log.warn("未找到结果元素: {}", resultTagName);
                    // 输出响应元素的所有子元素名称以便调试
                    StringBuilder childElements = new StringBuilder();
                    for (Object obj : responseElement.elements()) {
                        if (obj instanceof Element) {
                            childElements.append(((Element) obj).getName()).append(", ");
                        }
                    }
                    log.debug("响应元素包含的子元素: {}", childElements.toString());
                }
            } else {
                log.error("无法获取响应元素");
            }
        } catch (DocumentException e) {
            log.error("解析SOAP响应失败: {}", e.getMessage(), e);
        }
        return null;
    }

    /**
     * 获取元素文本内容
     *
     * @param parent 父元素
     * @param elementName 元素名称
     * @return 文本内容
     */
    public static String getElementText(Element parent, String elementName) {
        Element element = parent.element(elementName);
        return element != null ? element.getTextTrim() : null;
    }

    /**
     * 使用SAX解析器流式处理XML数据
     *
     * @param xmlData XML数据
     * @param dataElementName 待处理的数据元素名称
     * @param dataProcessor 数据处理器
     */
    public static void parseXmlWithSAX(String xmlData, String dataElementName, Consumer<Element> dataProcessor) {
        if (!StringUtils.hasText(xmlData)) {
            log.warn("XML数据为空，无法解析");
            return;
        }

        try {
            SAXParserFactory factory = SAXParserFactory.newInstance();
            SAXParser saxParser = factory.newSAXParser();

            // 创建自定义处理器并设置回调
            SaxHandler handler = new SaxHandler(dataElementName, dataProcessor);

            // 开始解析
            InputSource is = new InputSource(new StringReader(xmlData));
            saxParser.parse(is, handler);

            log.info("XML数据解析完成，共处理{}条记录", handler.getProcessedCount());
        } catch (Exception e) {
            log.error("SAX解析XML失败", e);
            throw new RuntimeException("解析XML数据失败", e);
        }
    }

    /**
     * SAX解析处理器
     */
    private static class SaxHandler extends DefaultHandler {
        private final String targetElementName;
        private final Consumer<Element> dataProcessor;
        private StringBuilder currentValue;
        private boolean inTargetElement;
        private Document currentDocument;
        private Element currentElement;
        private int processedCount;

        public SaxHandler(String targetElementName, Consumer<Element> dataProcessor) {
            this.targetElementName = targetElementName;
            this.dataProcessor = dataProcessor;
            this.processedCount = 0;
        }

        @Override
        public void startElement(String uri, String localName, String qName, Attributes attributes) throws SAXException {
            if (qName.equals(targetElementName)) {
                inTargetElement = true;
                // 创建新的文档和元素
                currentDocument = DocumentHelper.createDocument();
                currentElement = currentDocument.addElement(qName);
            } else if (inTargetElement) {
                // 在目标元素内部，添加子元素
                currentElement = currentElement.addElement(qName);
            }

            // 初始化内容缓冲区
            currentValue = new StringBuilder();
        }

        @Override
        public void characters(char[] ch, int start, int length) throws SAXException {
            if (inTargetElement && currentValue != null) {
                currentValue.append(new String(ch, start, length));
            }
        }

        @Override
        public void endElement(String uri, String localName, String qName) throws SAXException {
            if (inTargetElement) {
                if (qName.equals(targetElementName)) {
                    // 结束目标元素，处理并重置
                    inTargetElement = false;
                    dataProcessor.accept(currentElement);
                    processedCount++;
                    currentElement = null;
                    currentDocument = null;
                } else {
                    // 设置当前元素的文本并返回到父元素
                    if (currentElement != null) {
                        currentElement.setText(currentValue.toString().trim());
                        currentElement = currentElement.getParent();
                    }
                }
            }
        }

        public int getProcessedCount() {
            return processedCount;
        }
    }

    /**
     * 从JSON字符串中提取指定字段的值
     * @param jsonStr JSON字符串
     * @param fieldName 字段名
     * @return 字段值
     */
    private static String extractJsonValue(String jsonStr, String fieldName) {
        try {
            String pattern = "\"" + fieldName + "\"\\s*:\\s*\"([^\"]+)\"";
            java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern);
            java.util.regex.Matcher m = p.matcher(jsonStr);
            if (m.find()) {
                return m.group(1);
            }
        } catch (Exception e) {
            log.debug("提取JSON字段失败: {}", fieldName);
        }
        return null;
    }
}