package com.nercar.datasynchronization.controller;

import com.nercar.datasynchronization.client.SoapClient;
import com.nercar.datasynchronization.service.DepartmentSyncService;
import com.nercar.datasynchronization.service.EmployeeSyncService;
import com.nercar.datasynchronization.service.impl.AsyncSyncService;
import com.nercar.datasynchronization.utils.XmlUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/api/sync")
@Tag(name = "数据同步API", description = "提供部门和人员数据同步的接口")
public class SyncController {
    @Autowired
    private SoapClient soapClient;

    @Autowired
    private DepartmentSyncService departmentSyncService;

    @Autowired
    private EmployeeSyncService employeeSyncService;

    @Autowired
    private AsyncSyncService asyncSyncService;

    @GetMapping("/departments")
    @Operation(summary = "同步部门数据", description = "根据指定的时间范围获取并同步部门数据")
    public ResponseEntity<Map<String, Object>> syncDepartments(
            @Parameter(description = "开始时间 (格式: yyyy-MM-dd HH:mm:ss)")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startDate,
            @Parameter(description = "结束时间 (格式: yyyy-MM-dd HH:mm:ss)")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endDate) {

        Map<String, Object> result = new HashMap<>();
        try {
            log.info("开始同步部门数据，开始时间: {}, 结束时间: {}", startDate, endDate);

            // 调用SOAP接口获取部门数据
            String orgXmlResponse = soapClient.getOrgInfo(startDate, endDate);

            // 解析SOAP响应，提取XML数据
            String orgXmlData = XmlUtils.extractXmlData(orgXmlResponse, "GetOrgInfoFromMDMResult");

            if (orgXmlData != null && !orgXmlData.isEmpty()) {
                // 同步部门数据，传递时间范围
                departmentSyncService.syncDepartments(orgXmlData, startDate, endDate);

                result.put("success", true);
                result.put("message", "部门数据同步成功");
                return ResponseEntity.ok(result);
            } else {
                result.put("success", false);
                result.put("message", "未获取到部门数据或数据为空");
                return ResponseEntity.ok(result);
            }
        } catch (Exception e) {
            log.error("同步部门数据失败", e);
            result.put("success", false);
            result.put("message", "同步部门数据失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    @GetMapping("/employees")
    @Operation(summary = "同步员工数据", description = "根据指定的时间范围获取并同步员工数据")
    public ResponseEntity<Map<String, Object>> syncEmployees(
            @Parameter(description = "开始时间 (格式: yyyy-MM-dd HH:mm:ss)")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startDate,
            @Parameter(description = "结束时间 (格式: yyyy-MM-dd HH:mm:ss)")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endDate) {

        Map<String, Object> result = new HashMap<>();
        try {
            log.info("开始同步员工数据，开始时间: {}, 结束时间: {}", startDate, endDate);

            // 调用SOAP接口获取员工数据
            String userXmlResponse = soapClient.getUserInfo(startDate, endDate);

            // 解析SOAP响应，提取XML数据
            String userXmlData = XmlUtils.extractXmlData(userXmlResponse, "GetUserInfoFromMDMResult");

            if (userXmlData != null && !userXmlData.isEmpty()) {
                // 同步员工数据，传递时间范围
                employeeSyncService.syncEmployees(userXmlData, startDate, endDate);

                result.put("success", true);
                result.put("message", "员工数据同步成功");
                return ResponseEntity.ok(result);
            } else {
                result.put("success", false);
                result.put("message", "未获取到员工数据或数据为空");
                return ResponseEntity.ok(result);
            }
        } catch (Exception e) {
            log.error("同步员工数据失败", e);
            result.put("success", false);
            result.put("message", "同步员工数据失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }
    
    @GetMapping("/test")
    @Operation(summary = "测试API", description = "测试API是否正常工作")
    public ResponseEntity<Map<String, Object>> test() {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("timestamp", new Date());
        result.put("message", "API 正常工作");
        return ResponseEntity.ok(result);
    }
    
    @GetMapping("/employees/async")
    @Operation(summary = "异步同步员工数据", description = "异步方式根据指定的时间范围获取并同步员工数据")
    public ResponseEntity<Map<String, Object>> syncEmployeesAsync(
            @Parameter(description = "开始时间 (格式: yyyy-MM-dd HH:mm:ss)")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startDate,
            @Parameter(description = "结束时间 (格式: yyyy-MM-dd HH:mm:ss)")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endDate) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            log.info("开始异步同步员工数据，开始时间: {}, 结束时间: {}", startDate, endDate);
            
            // 异步同步员工数据
            CompletableFuture<String> future = asyncSyncService.syncEmployeesAsync(startDate, endDate);
            
            result.put("success", true);
            result.put("message", "异步同步员工数据已开始，处理结果将在后台完成");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("异步同步员工数据失败", e);
            result.put("success", false);
            result.put("message", "异步同步员工数据失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    @GetMapping("/employees/chunked")
    @Operation(summary = "分片同步员工数据", description = "将长时间段数据分成多个小时间段进行同步")
    public ResponseEntity<Map<String, Object>> syncEmployeesChunked(
            @Parameter(description = "开始时间 (格式: yyyy-MM-dd HH:mm:ss)")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startDate,
            @Parameter(description = "结束时间 (格式: yyyy-MM-dd HH:mm:ss)")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endDate,
            @Parameter(description = "每个分片的天数")
            @RequestParam(defaultValue = "1") int chunkDays,
            @Parameter(description = "失败重试次数")
            @RequestParam(defaultValue = "3") int retryCount) {

        Map<String, Object> result = new HashMap<>();
        try {
            log.info("开始分片同步员工数据，开始时间: {}, 结束时间: {}, 分片天数: {}, 重试次数: {}",
                    startDate, endDate, chunkDays, retryCount);

            // 计算总天数
            long totalDays = (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24);
            int totalChunks = (int) Math.ceil((double) totalDays / chunkDays);

            log.info("总天数: {}, 总分片数: {}", totalDays, totalChunks);

            // 创建包含处理结果的列表
            List<Map<String, Object>> chunkResults = new ArrayList<>();

            // 处理每个分片
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startDate);

            for (int i = 0; i < totalChunks; i++) {
                Date chunkStart = calendar.getTime();

                // 计算分片结束时间
                calendar.add(Calendar.DATE, chunkDays);
                Date chunkEnd = calendar.getTime();

                // 如果分片结束时间超过了总结束时间，则使用总结束时间
                if (chunkEnd.after(endDate)) {
                    chunkEnd = endDate;
                }

                log.info("处理分片 {}/{}: 开始时间={}, 结束时间={}", (i + 1), totalChunks, chunkStart, chunkEnd);

                // 添加重试逻辑
                boolean success = false;
                Exception lastException = null;

                for (int attempt = 0; attempt < retryCount && !success; attempt++) {
                    if (attempt > 0) {
                        log.info("重试分片 {}/{}: 第{}次尝试", (i + 1), totalChunks, attempt + 1);
                        // 在重试之前等待一段时间
                        try {
                            Thread.sleep(3000); // 等待3秒后重试
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                        }
                    }

                    try {
                        // 调用SOAP接口获取员工数据
                        String userXmlResponse = soapClient.getUserInfo(chunkStart, chunkEnd);

                        // 解析SOAP响应，提取XML数据
                        String userXmlData = XmlUtils.extractXmlData(userXmlResponse, "GetUserInfoFromMDMResult");

                        if (userXmlData != null && !userXmlData.isEmpty()) {
                            // 同步员工数据
                            employeeSyncService.syncEmployees(userXmlData, chunkStart, chunkEnd);

                            Map<String, Object> chunkResult = new HashMap<>();
                            chunkResult.put("chunkNumber", i + 1);
                            chunkResult.put("startDate", chunkStart);
                            chunkResult.put("endDate", chunkEnd);
                            chunkResult.put("success", true);
                            chunkResult.put("message", "分片同步成功");
                            chunkResults.add(chunkResult);

                            log.info("分片 {}/{} 同步成功", (i + 1), totalChunks);
                            success = true;
                        } else {
                            lastException = new Exception("未获取到员工数据或数据为空");
                            if (attempt == retryCount - 1) { // 最后一次尝试失败
                                Map<String, Object> chunkResult = new HashMap<>();
                                chunkResult.put("chunkNumber", i + 1);
                                chunkResult.put("startDate", chunkStart);
                                chunkResult.put("endDate", chunkEnd);
                                chunkResult.put("success", false);
                                chunkResult.put("message", "未获取到员工数据或数据为空");
                                chunkResults.add(chunkResult);

                                log.warn("分片 {}/{} 未获取到员工数据或数据为空", (i + 1), totalChunks);
                            }
                        }
                    } catch (Exception e) {
                        lastException = e;
                        log.error("分片 {}/{} 处理失败，尝试 {}/{}: {}",
                                (i + 1), totalChunks, (attempt + 1), retryCount, e.getMessage());

                        if (attempt == retryCount - 1) { // 最后一次尝试失败
                            Map<String, Object> chunkResult = new HashMap<>();
                            chunkResult.put("chunkNumber", i + 1);
                            chunkResult.put("startDate", chunkStart);
                            chunkResult.put("endDate", chunkEnd);
                            chunkResult.put("success", false);
                            chunkResult.put("message", "处理失败: " + e.getMessage());
                            chunkResults.add(chunkResult);

                            log.error("分片 {}/{} 处理失败: {}", (i + 1), totalChunks, e.getMessage());
                        }
                    }
                }
            }

            // 汇总结果
            int successCount = (int) chunkResults.stream().filter(r -> (boolean) r.get("success")).count();

            result.put("success", true);
            result.put("totalChunks", totalChunks);
            result.put("successfulChunks", successCount);
            result.put("details", chunkResults);
            result.put("message", String.format("分片同步完成，共%d个分片，成功%d个，失败%d个",
                    totalChunks, successCount, totalChunks - successCount));

            log.info("分片同步完成，共{}个分片，成功{}个，失败{}个",
                    totalChunks, successCount, totalChunks - successCount);

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("分片同步员工数据失败", e);
            result.put("success", false);
            result.put("message", "分片同步员工数据失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    @GetMapping("/departments/chunked")
    @Operation(summary = "分片同步部门数据", description = "将长时间段数据分成多个小时间段进行同步")
    public ResponseEntity<Map<String, Object>> syncDepartmentsChunked(
            @Parameter(description = "开始时间 (格式: yyyy-MM-dd HH:mm:ss)")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startDate,
            @Parameter(description = "结束时间 (格式: yyyy-MM-dd HH:mm:ss)")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endDate,
            @Parameter(description = "每个分片的天数")
            @RequestParam(defaultValue = "1") int chunkDays,
            @Parameter(description = "失败重试次数")
            @RequestParam(defaultValue = "3") int retryCount) {

        Map<String, Object> result = new HashMap<>();
        try {
            log.info("开始分片同步部门数据，开始时间: {}, 结束时间: {}, 分片天数: {}, 重试次数: {}",
                    startDate, endDate, chunkDays, retryCount);

            // 计算总天数
            long totalDays = (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24);
            int totalChunks = (int) Math.ceil((double) totalDays / chunkDays);

            log.info("总天数: {}, 总分片数: {}", totalDays, totalChunks);

            // 创建包含处理结果的列表
            List<Map<String, Object>> chunkResults = new ArrayList<>();

            // 处理每个分片
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startDate);

            for (int i = 0; i < totalChunks; i++) {
                Date chunkStart = calendar.getTime();

                // 计算分片结束时间
                calendar.add(Calendar.DATE, chunkDays);
                Date chunkEnd = calendar.getTime();

                // 如果分片结束时间超过了总结束时间，则使用总结束时间
                if (chunkEnd.after(endDate)) {
                    chunkEnd = endDate;
                }

                log.info("处理分片 {}/{}: 开始时间={}, 结束时间={}", (i + 1), totalChunks, chunkStart, chunkEnd);

                // 添加重试逻辑
                boolean success = false;
                Exception lastException = null;

                for (int attempt = 0; attempt < retryCount && !success; attempt++) {
                    if (attempt > 0) {
                        log.info("重试分片 {}/{}: 第{}次尝试", (i + 1), totalChunks, attempt + 1);
                        // 在重试之前等待一段时间
                        try {
                            Thread.sleep(3000); // 等待3秒后重试
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                        }
                    }

                    try {
                        // 调用SOAP接口获取部门数据
                        String orgXmlResponse = soapClient.getOrgInfo(chunkStart, chunkEnd);

                        // 解析SOAP响应，提取XML数据
                        String orgXmlData = XmlUtils.extractXmlData(orgXmlResponse, "GetOrgInfoFromMDMResult");

                        if (orgXmlData != null && !orgXmlData.isEmpty()) {
                            // 同步部门数据，传递时间范围参数
                            departmentSyncService.syncDepartments(orgXmlData, chunkStart, chunkEnd);

                            Map<String, Object> chunkResult = new HashMap<>();
                            chunkResult.put("chunkNumber", i + 1);
                            chunkResult.put("startDate", chunkStart);
                            chunkResult.put("endDate", chunkEnd);
                            chunkResult.put("success", true);
                            chunkResult.put("message", "分片同步成功");
                            chunkResults.add(chunkResult);

                            log.info("分片 {}/{} 同步成功", (i + 1), totalChunks);
                            success = true;
                        } else {
                            lastException = new Exception("未获取到部门数据或数据为空");
                            if (attempt == retryCount - 1) { // 最后一次尝试失败
                                Map<String, Object> chunkResult = new HashMap<>();
                                chunkResult.put("chunkNumber", i + 1);
                                chunkResult.put("startDate", chunkStart);
                                chunkResult.put("endDate", chunkEnd);
                                chunkResult.put("success", false);
                                chunkResult.put("message", "未获取到部门数据或数据为空");
                                chunkResults.add(chunkResult);

                                log.warn("分片 {}/{} 未获取到部门数据或数据为空", (i + 1), totalChunks);
                            }
                        }
                    } catch (Exception e) {
                        lastException = e;
                        log.error("分片 {}/{} 处理失败，尝试 {}/{}: {}",
                                (i + 1), totalChunks, (attempt + 1), retryCount, e.getMessage());

                        if (attempt == retryCount - 1) { // 最后一次尝试失败
                            Map<String, Object> chunkResult = new HashMap<>();
                            chunkResult.put("chunkNumber", i + 1);
                            chunkResult.put("startDate", chunkStart);
                            chunkResult.put("endDate", chunkEnd);
                            chunkResult.put("success", false);
                            chunkResult.put("message", "处理失败: " + e.getMessage());
                            chunkResults.add(chunkResult);

                            log.error("分片 {}/{} 处理失败: {}", (i + 1), totalChunks, e.getMessage());
                        }
                    }
                }
            }

            // 汇总结果
            int successCount = (int) chunkResults.stream().filter(r -> (boolean) r.get("success")).count();

            result.put("success", true);
            result.put("totalChunks", totalChunks);
            result.put("successfulChunks", successCount);
            result.put("details", chunkResults);
            result.put("message", String.format("分片同步完成，共%d个分片，成功%d个，失败%d个",
                    totalChunks, successCount, totalChunks - successCount));

            log.info("分片同步完成，共{}个分片，成功{}个，失败{}个",
                    totalChunks, successCount, totalChunks - successCount);

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("分片同步部门数据失败", e);
            result.put("success", false);
            result.put("message", "分片同步部门数据失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }
}