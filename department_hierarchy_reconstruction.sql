-- =====================================================
-- 基于department_sync_test2.sql的full_name重构部门层级结构
-- =====================================================

-- 第一步：分析所有full_name的层级模式（简化版本）
SELECT
    org_code,
    org_name,
    full_name,
    -- 简化的层级分析，使用LIKE替代REGEXP
    CASE
        WHEN full_name LIKE '%事业部%' THEN
            SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '事业部', 1), '（', 1) + '事业部'
        WHEN full_name LIKE '%集团%' AND full_name NOT LIKE '%事业部%' THEN
            SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '集团', 1), '（', 1) + '集团'
        WHEN full_name LIKE '%有限公司%' AND full_name NOT LIKE '%事业部%' THEN
            SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '有限公司', 1), '（', 1) + '有限公司'
        WHEN full_name LIKE '%中心%' AND full_name NOT LIKE '%事业部%' AND full_name NOT LIKE '%集团%' THEN
            SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '中心', 1), '（', 1) + '中心'
        WHEN full_name LIKE '%部' AND full_name NOT LIKE '%事业部%' THEN
            full_name
        ELSE
            SUBSTRING_INDEX(full_name, '（', 1)
    END as level1_dept,

    CASE
        WHEN full_name LIKE '%事业部%厂%' THEN
            SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '厂', 1), '事业部', -1) + '厂'
        WHEN full_name LIKE '%事业部%处%' THEN
            SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '处', 1), '事业部', -1) + '处'
        WHEN full_name LIKE '%集团%部%' THEN
            SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '部', 1), '集团', -1) + '部'
        WHEN full_name LIKE '%有限公司%部%' THEN
            SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '部', 1), '有限公司', -1) + '部'
        ELSE ''
    END as level2_dept,

    CASE
        WHEN full_name LIKE '%厂%车间%' THEN
            SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '车间', 1), '厂', -1) + '车间'
        WHEN full_name LIKE '%厂%室%' THEN
            SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '室', 1), '厂', -1) + '室'
        WHEN full_name LIKE '%处%室%' THEN
            SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '室', 1), '处', -1) + '室'
        WHEN full_name LIKE '%部%科%' THEN
            SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '科', 1), '部', -1) + '科'
        WHEN full_name LIKE '%厂%科%' THEN
            SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '科', 1), '厂', -1) + '科'
        ELSE ''
    END as level3_dept,

    CASE
        WHEN full_name LIKE '%车间%班%' THEN
            SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '班', 1), '车间', -1) + '班'
        WHEN full_name LIKE '%室%班%' THEN
            SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '班', 1), '室', -1) + '班'
        WHEN full_name LIKE '%科%班%' THEN
            SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '班', 1), '科', -1) + '班'
        ELSE ''
    END as level4_dept

FROM department_sync_test
WHERE is_history = 0 AND user_predef_14 != 'D'
AND full_name IS NOT NULL AND full_name != ''
ORDER BY full_name;

-- 第二步：生成标准化的层级结构
-- 创建临时表存储重构后的部门结构
DROP TEMPORARY TABLE IF EXISTS dept_hierarchy_new;
CREATE TEMPORARY TABLE dept_hierarchy_new (
    new_dept_id VARCHAR(20),
    dept_name VARCHAR(200),
    dept_level INT,
    parent_dept_id VARCHAR(20),
    full_path VARCHAR(500),
    original_org_codes TEXT,
    dept_type VARCHAR(50)
);

-- 插入Level 1部门（事业部/集团/公司级别）
INSERT INTO dept_hierarchy_new (new_dept_id, dept_name, dept_level, parent_dept_id, full_path, dept_type)
SELECT DISTINCT
    CONCAT('L1_', ROW_NUMBER() OVER (ORDER BY level1)) as new_dept_id,
    level1 as dept_name,
    1 as dept_level,
    NULL as parent_dept_id,
    level1 as full_path,
    CASE 
        WHEN level1 LIKE '%事业部%' THEN '事业部'
        WHEN level1 LIKE '%集团%' THEN '集团'
        WHEN level1 LIKE '%有限公司%' THEN '公司'
        WHEN level1 LIKE '%中心%' THEN '中心'
        WHEN level1 LIKE '%部' THEN '部门'
        ELSE '其他'
    END as dept_type
FROM (
    SELECT DISTINCT
        CASE 
            WHEN full_name REGEXP '.*事业部.*' THEN 
                REGEXP_REPLACE(REGEXP_EXTRACT(full_name, '^([^事业部]*事业部)'), '^.*?([^事业部]*事业部).*$', '\\1')
            WHEN full_name REGEXP '.*集团.*' THEN 
                REGEXP_REPLACE(REGEXP_EXTRACT(full_name, '^([^集团]*集团)'), '^.*?([^集团]*集团).*$', '\\1')
            WHEN full_name REGEXP '.*有限公司.*' AND full_name NOT REGEXP '.*事业部.*' THEN 
                REGEXP_REPLACE(REGEXP_EXTRACT(full_name, '^([^有限公司]*有限公司)'), '^.*?([^有限公司]*有限公司).*$', '\\1')
            WHEN full_name REGEXP '.*中心.*' AND full_name NOT REGEXP '.*(事业部|集团|有限公司).*' THEN 
                REGEXP_REPLACE(REGEXP_EXTRACT(full_name, '^([^中心]*中心)'), '^.*?([^中心]*中心).*$', '\\1')
            WHEN full_name REGEXP '.*部$' AND full_name NOT REGEXP '.*(事业部|集团|有限公司|中心).*' THEN 
                full_name
            ELSE 
                SUBSTRING_INDEX(full_name, '（', 1)
        END as level1
    FROM department_sync_test 
    WHERE is_history = 0 AND user_predef_14 != 'D'
    AND full_name IS NOT NULL AND full_name != ''
) t1
WHERE level1 IS NOT NULL AND level1 != '';

-- 显示重构后的Level 1部门
SELECT 
    '=== Level 1 部门（事业部/集团级别） ===' as section,
    new_dept_id,
    dept_name,
    dept_type,
    '统计信息' as note
FROM dept_hierarchy_new 
WHERE dept_level = 1
UNION ALL
SELECT 
    'SUMMARY',
    '',
    CONCAT('共 ', COUNT(*), ' 个一级部门') as dept_name,
    '',
    ''
FROM dept_hierarchy_new 
WHERE dept_level = 1
ORDER BY new_dept_id;

-- 第三步：插入Level 2部门（厂/处级别）
INSERT INTO dept_hierarchy_new (new_dept_id, dept_name, dept_level, parent_dept_id, full_path, dept_type)
SELECT DISTINCT
    CONCAT('L2_', ROW_NUMBER() OVER (ORDER BY level1, level2)) as new_dept_id,
    level2 as dept_name,
    2 as dept_level,
    (SELECT new_dept_id FROM dept_hierarchy_new WHERE dept_name = t2.level1 AND dept_level = 1 LIMIT 1) as parent_dept_id,
    CONCAT(level1, ' -> ', level2) as full_path,
    CASE
        WHEN level2 LIKE '%厂%' THEN '厂'
        WHEN level2 LIKE '%处%' THEN '处'
        WHEN level2 LIKE '%部%' THEN '部'
        WHEN level2 LIKE '%室%' THEN '室'
        ELSE '其他'
    END as dept_type
FROM (
    SELECT DISTINCT
        -- Level 1
        CASE
            WHEN full_name REGEXP '.*事业部.*' THEN
                REGEXP_REPLACE(REGEXP_EXTRACT(full_name, '^([^事业部]*事业部)'), '^.*?([^事业部]*事业部).*$', '\\1')
            WHEN full_name REGEXP '.*集团.*' THEN
                REGEXP_REPLACE(REGEXP_EXTRACT(full_name, '^([^集团]*集团)'), '^.*?([^集团]*集团).*$', '\\1')
            WHEN full_name REGEXP '.*有限公司.*' AND full_name NOT REGEXP '.*事业部.*' THEN
                REGEXP_REPLACE(REGEXP_EXTRACT(full_name, '^([^有限公司]*有限公司)'), '^.*?([^有限公司]*有限公司).*$', '\\1')
            WHEN full_name REGEXP '.*中心.*' AND full_name NOT REGEXP '.*(事业部|集团|有限公司).*' THEN
                REGEXP_REPLACE(REGEXP_EXTRACT(full_name, '^([^中心]*中心)'), '^.*?([^中心]*中心).*$', '\\1')
            ELSE SUBSTRING_INDEX(full_name, '（', 1)
        END as level1,
        -- Level 2
        CASE
            WHEN full_name REGEXP '.*事业部.*厂.*' THEN
                REGEXP_REPLACE(REGEXP_EXTRACT(full_name, '事业部([^厂]*厂)'), '^([^厂]*厂).*$', '\\1')
            WHEN full_name REGEXP '.*事业部.*处.*' THEN
                REGEXP_REPLACE(REGEXP_EXTRACT(full_name, '事业部([^处]*处)'), '^([^处]*处).*$', '\\1')
            WHEN full_name REGEXP '.*集团.*部.*' THEN
                REGEXP_REPLACE(REGEXP_EXTRACT(full_name, '集团([^部]*部)'), '^([^部]*部).*$', '\\1')
            WHEN full_name REGEXP '.*有限公司.*部.*' THEN
                REGEXP_REPLACE(REGEXP_EXTRACT(full_name, '有限公司([^部]*部)'), '^([^部]*部).*$', '\\1')
            WHEN full_name REGEXP '.*中心.*厂.*' THEN
                REGEXP_REPLACE(REGEXP_EXTRACT(full_name, '中心([^厂]*厂)'), '^([^厂]*厂).*$', '\\1')
            ELSE ''
        END as level2
    FROM department_sync_test
    WHERE is_history = 0 AND user_predef_14 != 'D'
    AND full_name IS NOT NULL AND full_name != ''
) t2
WHERE level1 IS NOT NULL AND level1 != ''
AND level2 IS NOT NULL AND level2 != '';

-- 第四步：插入Level 3部门（车间/室/科级别）
INSERT INTO dept_hierarchy_new (new_dept_id, dept_name, dept_level, parent_dept_id, full_path, dept_type)
SELECT DISTINCT
    CONCAT('L3_', ROW_NUMBER() OVER (ORDER BY level1, level2, level3)) as new_dept_id,
    level3 as dept_name,
    3 as dept_level,
    (SELECT new_dept_id FROM dept_hierarchy_new WHERE dept_name = t3.level2 AND dept_level = 2 LIMIT 1) as parent_dept_id,
    CONCAT(level1, ' -> ', level2, ' -> ', level3) as full_path,
    CASE
        WHEN level3 LIKE '%车间%' THEN '车间'
        WHEN level3 LIKE '%室%' THEN '室'
        WHEN level3 LIKE '%科%' THEN '科'
        WHEN level3 LIKE '%站%' THEN '站'
        ELSE '其他'
    END as dept_type
FROM (
    SELECT DISTINCT
        level1, level2,
        CASE
            WHEN full_name REGEXP '.*厂.*车间.*' THEN
                REGEXP_REPLACE(REGEXP_EXTRACT(full_name, '厂([^车间]*车间)'), '^([^车间]*车间).*$', '\\1')
            WHEN full_name REGEXP '.*厂.*室.*' THEN
                REGEXP_REPLACE(REGEXP_EXTRACT(full_name, '厂([^室]*室)'), '^([^室]*室).*$', '\\1')
            WHEN full_name REGEXP '.*处.*室.*' THEN
                REGEXP_REPLACE(REGEXP_EXTRACT(full_name, '处([^室]*室)'), '^([^室]*室).*$', '\\1')
            WHEN full_name REGEXP '.*部.*科.*' THEN
                REGEXP_REPLACE(REGEXP_EXTRACT(full_name, '部([^科]*科)'), '^([^科]*科).*$', '\\1')
            WHEN full_name REGEXP '.*厂.*科.*' THEN
                REGEXP_REPLACE(REGEXP_EXTRACT(full_name, '厂([^科]*科)'), '^([^科]*科).*$', '\\1')
            ELSE ''
        END as level3,
        -- 重复level1和level2的逻辑...
        CASE
            WHEN full_name REGEXP '.*事业部.*' THEN
                REGEXP_REPLACE(REGEXP_EXTRACT(full_name, '^([^事业部]*事业部)'), '^.*?([^事业部]*事业部).*$', '\\1')
            WHEN full_name REGEXP '.*集团.*' THEN
                REGEXP_REPLACE(REGEXP_EXTRACT(full_name, '^([^集团]*集团)'), '^.*?([^集团]*集团).*$', '\\1')
            WHEN full_name REGEXP '.*有限公司.*' AND full_name NOT REGEXP '.*事业部.*' THEN
                REGEXP_REPLACE(REGEXP_EXTRACT(full_name, '^([^有限公司]*有限公司)'), '^.*?([^有限公司]*有限公司).*$', '\\1')
            WHEN full_name REGEXP '.*中心.*' AND full_name NOT REGEXP '.*(事业部|集团|有限公司).*' THEN
                REGEXP_REPLACE(REGEXP_EXTRACT(full_name, '^([^中心]*中心)'), '^.*?([^中心]*中心).*$', '\\1')
            ELSE SUBSTRING_INDEX(full_name, '（', 1)
        END as level1,
        CASE
            WHEN full_name REGEXP '.*事业部.*厂.*' THEN
                REGEXP_REPLACE(REGEXP_EXTRACT(full_name, '事业部([^厂]*厂)'), '^([^厂]*厂).*$', '\\1')
            WHEN full_name REGEXP '.*事业部.*处.*' THEN
                REGEXP_REPLACE(REGEXP_EXTRACT(full_name, '事业部([^处]*处)'), '^([^处]*处).*$', '\\1')
            WHEN full_name REGEXP '.*集团.*部.*' THEN
                REGEXP_REPLACE(REGEXP_EXTRACT(full_name, '集团([^部]*部)'), '^([^部]*部).*$', '\\1')
            WHEN full_name REGEXP '.*有限公司.*部.*' THEN
                REGEXP_REPLACE(REGEXP_EXTRACT(full_name, '有限公司([^部]*部)'), '^([^部]*部).*$', '\\1')
            WHEN full_name REGEXP '.*中心.*厂.*' THEN
                REGEXP_REPLACE(REGEXP_EXTRACT(full_name, '中心([^厂]*厂)'), '^([^厂]*厂).*$', '\\1')
            ELSE ''
        END as level2
    FROM department_sync_test
    WHERE is_history = 0 AND user_predef_14 != 'D'
    AND full_name IS NOT NULL AND full_name != ''
) t3
WHERE level1 IS NOT NULL AND level1 != ''
AND level2 IS NOT NULL AND level2 != ''
AND level3 IS NOT NULL AND level3 != '';

-- 第五步：显示重构后的完整层级结构
SELECT
    '=== 重构后的部门层级结构 ===' as section,
    new_dept_id,
    CONCAT(REPEAT('  ', dept_level-1), dept_name) as dept_hierarchy,
    dept_level,
    dept_type,
    full_path
FROM dept_hierarchy_new
ORDER BY dept_level, full_path;

-- 第六步：统计信息
SELECT
    '=== 层级统计信息 ===' as section,
    dept_level as level,
    dept_type,
    COUNT(*) as count,
    '' as sample_dept
FROM dept_hierarchy_new
GROUP BY dept_level, dept_type
ORDER BY dept_level, dept_type;
