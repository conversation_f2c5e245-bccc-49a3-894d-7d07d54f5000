package com.nercar.datasynchronization.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 重试机制配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "app.retry")
public class RetryConfig {
    
    /**
     * 最大重试次数
     */
    private int maxRetries = 3;
    
    /**
     * 重试间隔（毫秒）
     */
    private long retryDelay = 5000;
    
    /**
     * 是否启用指数退避
     */
    private boolean exponentialBackoff = false;
    
    /**
     * 指数退避基础延迟（毫秒）
     */
    private long baseDelay = 2000;
    
    /**
     * 指数退避最大延迟（毫秒）
     */
    private long maxDelay = 30000;
    
    /**
     * 是否启用重试机制
     */
    private boolean enabled = true;
}
