package com.nercar.datasynchronization.job;

import com.nercar.datasynchronization.service.HrUserDetailsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.scheduling.annotation.Scheduled; // 已注释定时任务，暂时不需要
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 员工岗位详情同步定时任务
 */
@Slf4j
@Component
public class RelationSyncJob {

    @Autowired
    private HrUserDetailsService hrUserDetailsService;

    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    /**
     * 每天凌晨2点执行员工岗位详情全量同步
     * 注释：根据业务需求简化，只保留每天同步前一天部门和员工数据的任务
     */
    // @Scheduled(cron = "0 0 2 * * ?")
    public void syncHrUserDetailsDaily() {
        log.info("开始执行员工岗位详情全量同步定时任务: {}", dateFormat.format(new Date()));

        try {
            hrUserDetailsService.updateAllUserDetailFromErp();
            log.info("员工岗位详情全量同步定时任务完成");
        } catch (Exception e) {
            log.error("员工岗位详情全量同步定时任务失败: {}", e.getMessage(), e);
        }
    }
}