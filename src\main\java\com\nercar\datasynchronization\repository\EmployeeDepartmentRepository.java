package com.nercar.datasynchronization.repository;

import com.nercar.datasynchronization.entity.EmployeeDepartment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EmployeeDepartmentRepository extends JpaRepository<EmployeeDepartment, Long> {
    
    List<EmployeeDepartment> findByEmployeeCode(String employeeCode);
    
    List<EmployeeDepartment> findByDepartmentCode(String departmentCode);
    
    EmployeeDepartment findByEmployeeCodeAndDepartmentCode(String employeeCode, String departmentCode);
}