package com.nercar.datasynchronization.repository;

import com.nercar.datasynchronization.entity.DepartmentSyncOperationLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * 部门同步操作日志数据访问层
 */
@Repository
public interface DepartmentSyncOperationLogRepository extends JpaRepository<DepartmentSyncOperationLog, Long> {

    /**
     * 根据同步日期查找日志
     */
    List<DepartmentSyncOperationLog> findBySyncDate(LocalDate syncDate);

    /**
     * 根据同步日期范围查找日志
     */
    List<DepartmentSyncOperationLog> findBySyncDateBetween(LocalDate startDate, LocalDate endDate);

    /**
     * 根据组织编码查找日志
     */
    List<DepartmentSyncOperationLog> findByOrgCode(String orgCode);

    /**
     * 根据操作类型查找日志
     */
    List<DepartmentSyncOperationLog> findByOperationType(String operationType);

    /**
     * 根据操作结果查找日志
     */
    List<DepartmentSyncOperationLog> findByOperationResult(String operationResult);

    /**
     * 统计指定日期的操作数量
     */
    @Query("SELECT l.operationType, COUNT(l) FROM DepartmentSyncOperationLog l " +
           "WHERE l.syncDate = :syncDate GROUP BY l.operationType")
    List<Object[]> countOperationsByTypeAndDate(@Param("syncDate") LocalDate syncDate);

    /**
     * 统计指定日期范围的操作数量
     */
    @Query("SELECT l.operationType, COUNT(l) FROM DepartmentSyncOperationLog l " +
           "WHERE l.syncDate BETWEEN :startDate AND :endDate GROUP BY l.operationType")
    List<Object[]> countOperationsByTypeAndDateRange(@Param("startDate") LocalDate startDate, 
                                                     @Param("endDate") LocalDate endDate);

    /**
     * 统计成功和失败的操作数量
     */
    @Query("SELECT l.operationResult, COUNT(l) FROM DepartmentSyncOperationLog l " +
           "WHERE l.syncDate = :syncDate GROUP BY l.operationResult")
    List<Object[]> countOperationsByResultAndDate(@Param("syncDate") LocalDate syncDate);

    /**
     * 查找失败的操作日志
     */
    List<DepartmentSyncOperationLog> findByOperationResultAndSyncDate(String operationResult, LocalDate syncDate);

    /**
     * 删除指定日期之前的日志（清理历史日志）
     */
    void deleteBySyncDateBefore(LocalDate date);
}
