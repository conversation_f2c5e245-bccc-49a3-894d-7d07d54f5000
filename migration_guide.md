# 部门数据迁移指南

## 📋 **迁移概述**

将 `department_sync_test2.sql` 中已矫正的部门层级数据迁移到 `t_org_structure` 表中。

## 📁 **相关文件**

1. **`department_sync_test2.sql`** - 源数据（已矫正的部门层级结构，1399条记录）
2. **`t_org_structure.sql`** - 目标表结构（PostgreSQL）
3. **`department_sync_test2_to_org_structure.sql`** - 迁移脚本
4. **`migration_guide.md`** - 本使用指南

## 🏗️ **表结构映射**

### **源表：department_sync_test**
```sql
org_code VARCHAR(20)        -- 部门编码（如：X50000000）
org_name VARCHAR(200)       -- 部门名称
parent_code VARCHAR(20)     -- 上级部门编码
full_name VARCHAR(500)      -- 完整名称
is_history TINYINT          -- 是否历史数据
user_predef_14 VARCHAR(10)  -- 删除标识
```

### **目标表：t_org_structure**
```sql
id int8                     -- 组织编码（数字，来源于org_code转换）
organ_name varchar(255)     -- 组织名称（来源于org_name）
pre_id int8                 -- 父级ID（来源于parent_code转换）
order_info int4             -- 排序信息
is_del bool                 -- 删除标识（false=正常）
create_time timestamp       -- 创建时间
modify_time timestamp       -- 修改时间
data_source int4            -- 数据来源（2=同步数据）
```

## 🔄 **数据转换规则**

### **1. ID转换**
```
X50000000 → 50000000
X38060001 → 38060001
XA1000000 → 865634712 (特殊编码转换)
XB1000000 → 1875040113 (特殊编码转换)
XC5000000 → 1642783431 (特殊编码转换)
```

### **2. 层级关系转换**
```
parent_code='1' → pre_id=NULL (根节点)
parent_code='X50000000' → pre_id=50000000
```

### **3. 数据过滤**
- 只迁移 `is_history=0` 的记录（非历史数据）
- 只迁移 `user_predef_14!='D'` 的记录（非删除数据）

## 🚀 **执行步骤**

### **第一步：备份现有数据**
```sql
-- 备份现有的t_org_structure数据
CREATE TABLE t_org_structure_backup AS 
SELECT * FROM t_org_structure WHERE data_source = 2;
```

### **第二步：执行迁移脚本**
```sql
-- 在PostgreSQL数据库中执行迁移脚本
\i department_sync_test2_to_org_structure.sql
```

### **第三步：验证迁移结果**
```sql
-- 查看迁移统计
SELECT 
    data_source,
    COUNT(*) as record_count,
    COUNT(CASE WHEN pre_id IS NULL THEN 1 END) as root_count,
    COUNT(CASE WHEN pre_id IS NOT NULL THEN 1 END) as child_count
FROM t_org_structure 
WHERE data_source = 2
GROUP BY data_source;

-- 查看层级关系
SELECT 
    p.organ_name as parent_name,
    COUNT(c.id) as child_count
FROM t_org_structure p
LEFT JOIN t_org_structure c ON p.id = c.pre_id
WHERE p.data_source = 2 AND p.pre_id IS NULL
GROUP BY p.id, p.organ_name
ORDER BY child_count DESC
LIMIT 10;
```

## 📊 **预期结果**

### **迁移数据量：**
- **总记录数**: 约200-300条（主要层级结构）
- **根节点数**: 约40个（一级部门）
- **子节点数**: 约160-260个（二、三、四级部门）

### **层级分布：**
- **Level 1**: 事业部、集团、公司、职能部门（约40个）
- **Level 2**: 厂、处级别（约80个）
- **Level 3**: 车间、科、室级别（约100个）
- **Level 4**: 班组级别（约80个）

### **主要事业部结构：**
```
板材事业部 (50000000)
├── 中厚板卷厂 (32000000)
├── 宽厚板厂 (38000000)
│   ├── 板加车间 (38060000)
│   │   ├── 加热炉甲班 (38060001)
│   │   ├── 加热炉乙班 (38060002)
│   │   └── ...
│   ├── 热轧车间 (38070000)
│   └── 精整车间 (38080000)
├── 第一炼钢厂 (73000000)
├── 中板厂 (66000000)
└── 金石材料厂 (84000000)

特钢事业部 (49000000)
├── 第二炼钢厂 (63000000)
├── 棒材厂 (65000000)
├── 精整厂 (41000000)
└── ...

炼铁事业部 (47000000)
├── 第一炼铁厂 (31000000)
├── 第二炼铁厂 (62000000)
├── 原料厂 (43000000)
└── ...
```

## ⚠️ **注意事项**

### **1. 数据完整性**
- 迁移脚本只包含主要的层级结构（约200-300条记录）
- 完整的1399条记录需要额外的脚本生成
- 建议分批迁移，先迁移主要结构，再补充详细数据

### **2. ID冲突处理**
- 执行前会清理 `data_source=2` 的现有数据
- 特殊编码（XA、XB、XC开头）使用预定义的数字ID
- 确保ID不与现有的 `data_source=1` 数据冲突

### **3. 字符编码**
- 确保数据库连接使用UTF-8编码
- 部门名称包含中文字符，需要正确的字符集支持

### **4. 事务处理**
- 建议在事务中执行迁移脚本
- 如果出现错误，可以回滚到迁移前状态

## 🔧 **故障排除**

### **常见问题：**

1. **ID冲突错误**
   ```sql
   -- 检查ID冲突
   SELECT id, COUNT(*) FROM t_org_structure GROUP BY id HAVING COUNT(*) > 1;
   ```

2. **层级关系错误**
   ```sql
   -- 检查孤儿节点
   SELECT * FROM t_org_structure 
   WHERE pre_id IS NOT NULL 
   AND pre_id NOT IN (SELECT id FROM t_org_structure);
   ```

3. **字符编码问题**
   ```sql
   -- 检查中文字符显示
   SELECT id, organ_name FROM t_org_structure 
   WHERE data_source = 2 AND organ_name LIKE '%事业部%';
   ```

## ✅ **验证清单**

- [ ] 备份现有数据
- [ ] 执行迁移脚本
- [ ] 验证记录数量
- [ ] 检查层级关系
- [ ] 验证中文字符显示
- [ ] 测试查询性能
- [ ] 确认业务功能正常

---

**🎉 迁移完成后，t_org_structure表将包含来自department_sync_test2.sql的标准化部门层级结构！**
