package com.nercar.datasynchronization.service.impl;

import com.nercar.datasynchronization.entity.*;
import com.nercar.datasynchronization.repository.*;
import com.nercar.datasynchronization.service.RelationExtractService;
import com.nercar.datasynchronization.utils.HttpUtil;
import com.nercar.datasynchronization.utils.IDUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.time.Year;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 关系提取服务实现类 - 直接从HR API获取数据并分别保存到关系表
 */
@Slf4j
@Service
public class RelationExtractServiceImpl implements RelationExtractService {

    @Autowired
    private PositionInfoRepository positionInfoRepository;
    
    @Autowired
    private DepartmentPositionRepository departmentPositionRepository;
    
    @Autowired
    private EmployeePositionRelationRepository employeePositionRelationRepository;
    
    @Autowired
    private EmployeeDepartmentRepository employeeDepartmentRepository;
    
    @Autowired
    private EmployeeRepository employeeRepository;
    
    @Autowired
    private DepartmentRepository departmentRepository;
    
    @Value("${sync.http.position-url}")
    private String positionServiceUrl;
    
    @Value("${sync.http.timeout:30000}")
    private int timeout;
    
    /**
     * 从API直接获取数据并保存到各关系表
     */
    @Override
    @Transactional
    public Map<String, Integer> syncAllRelations() {
        return syncRelationsInRange(null, null);
    }
    
    /**
     * 从API获取指定时间范围内的数据并保存到各关系表
     */
    @Override
    @Transactional
    public Map<String, Integer> syncRelationsInRange(Date startDate, Date endDate) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        
        if (startDate != null && endDate != null) {
            log.info("开始从API同步关系数据，URL: {}, 记录时间范围: {} 至 {}",
                    positionServiceUrl,
                    dateFormat.format(startDate),
                    dateFormat.format(endDate));
        } else {
            log.info("开始从API同步所有关系数据，URL: {}", positionServiceUrl);
        }
        
        // 用于去重的集合
        Map<String, PositionInfo> positionMap = new HashMap<>();
        Set<String> positionDeptRelationSet = new HashSet<>();
        Set<String> positionEmployeeRelationSet = new HashSet<>();
        Set<String> employeeDeptRelationSet = new HashSet<>();
        
        // 批量处理列表
        List<PositionInfo> positionsToSave = new ArrayList<>();
        List<DepartmentPosition> positionDeptsToSave = new ArrayList<>();
        List<EmployeePositionRelation> positionEmpsToSave = new ArrayList<>();
        List<EmployeeDepartment> empDeptsToSave = new ArrayList<>();
        
        // 预加载现有岗位信息
        Map<String, PositionInfo> existingPositions = new HashMap<>();
        positionInfoRepository.findAll().forEach(position -> 
            existingPositions.put(position.getPositionName(), position));
        log.info("预加载了 {} 个现有岗位", existingPositions.size());
        
        // 预加载部门映射
        Map<String, Department> departmentCodeMap = new HashMap<>();
        departmentRepository.findAll().forEach(dept -> {
            if (dept.getOrgCode() != null) {
                departmentCodeMap.put(dept.getOrgCode(), dept);
            }
        });
        log.info("预加载了 {} 个部门", departmentCodeMap.size());
        
        // 预加载员工映射
        Map<String, Employee> employeeCodeMap = new HashMap<>();
        employeeRepository.findAll().forEach(emp -> {
            if (emp.getEmployeeCode() != null) {
                employeeCodeMap.put(emp.getEmployeeCode(), emp);
            }
        });
        log.info("预加载了 {} 个员工", employeeCodeMap.size());
        
        // 记录处理数据的计数器
        int totalCount = 0;
        int positionCount = 0;
        int deptPosCount = 0;
        int empPosCount = 0;
        int empDeptCount = 0;
        
        // 参数设置
        Map<String, String> paramMap = new HashMap<>(2);
        Long limit = 1000L;
        Long offset = 0L;
        
        // 循环获取数据
        while (true) {
            paramMap.put("limit", String.valueOf(limit));
            paramMap.put("offset", String.valueOf(offset));
            
            log.info("请求关系数据: URL={}, 参数: offset={}, limit={}",
                    positionServiceUrl, offset, limit);
            
            String jsonString;
            try {
                jsonString = HttpUtil.get(positionServiceUrl, paramMap, timeout);
            } catch (Exception e) {
                log.error("发送HTTP GET请求异常: {}", e.getMessage(), e);
                if (totalCount > 0) {
                    log.warn("已同步部分数据，跳过剩余同步");
                    break;
                } else {
                    throw new RuntimeException("获取关系数据失败，未能同步任何数据", e);
                }
            }
            
            if (jsonString == null || jsonString.trim().isEmpty()) {
                log.error("响应数据为空");
                break;
            }
            
            // 解析JSON数据
            JSONObject jsonObject;
            try {
                jsonObject = JSONUtil.parseObj(jsonString);
            } catch (Exception e) {
                log.error("解析JSON数据异常: {}", e.getMessage(), e);
                log.error("原始数据: {}", jsonString);
                break;
            }
            
            JSONArray jsonArray = jsonObject.getJSONArray("data");
            if (jsonArray == null || jsonArray.size() == 0) {
                log.info("没有更多数据，结束同步");
                break;
            }
            
            totalCount += jsonArray.size();
            
            // 处理当前批次数据
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject item = jsonArray.getJSONObject(i);
                
                String post = item.getStr("post");
                String userNo = item.getStr("userNo");
                String name = item.getStr("name");
                String departmentId = item.getStr("departmentId");
                String department = item.getStr("department");
                String skillLevel = item.getStr("skillLevel");
                String workExpStr = item.getStr("workExp");
                String teamsGroups = item.getStr("teamsGroups");
                String education = item.getStr("education");
                
                Integer workExp = null;
                if (workExpStr != null && !workExpStr.isEmpty()) {
                    try {
                        workExp = Integer.parseInt(workExpStr);
                    } catch (NumberFormatException e) {
                        log.warn("解析工作年限失败: {}", workExpStr);
                    }
                }
                
                // 1. 处理岗位信息
                if (post != null && !post.trim().isEmpty()) {
                    String postName = post.trim();
                    
                    if (!positionMap.containsKey(postName)) {
                        String positionCode = "POS_" + Math.abs(postName.hashCode());
                        PositionInfo existingPosition = existingPositions.get(postName);
                        
                        if (existingPosition == null) {
                            PositionInfo position = new PositionInfo();
                            position.setId(IDUtil.generateId());
                            position.setPositionName(postName);
                            position.setPositionCode(positionCode);
                            position.setSkillLevelCode(skillLevel);
                            position.setRequiredEducation(education);
                            position.setRequiredExperience(workExp);
                            position.setStatus("ACTIVE");
                            position.setCreatedTime(new Date());
                            position.setUpdatedTime(new Date());
                            
                            positionMap.put(postName, position);
                            positionsToSave.add(position);
                            positionCount++;
                        } else {
                            positionMap.put(postName, existingPosition);
                            
                            // 检查是否需要更新岗位信息
                            boolean needUpdate = false;
                            
                            if (skillLevel != null && !skillLevel.equals(existingPosition.getSkillLevelCode())) {
                                existingPosition.setSkillLevelCode(skillLevel);
                                needUpdate = true;
                            }
                            
                            if (education != null && !education.equals(existingPosition.getRequiredEducation())) {
                                existingPosition.setRequiredEducation(education);
                                needUpdate = true;
                            }
                            
                            if (workExp != null && !workExp.equals(existingPosition.getRequiredExperience())) {
                                existingPosition.setRequiredExperience(workExp);
                                needUpdate = true;
                            }
                            
                            if (needUpdate) {
                                existingPosition.setUpdatedTime(new Date());
                                positionsToSave.add(existingPosition);
                                positionCount++;
                            }
                        }
                    }
                    
                    // 2. 处理部门-岗位关系
                    if (departmentId != null && !departmentId.trim().isEmpty()) {
                        String deptCode = departmentId.trim();
                        String relKey = postName + "_" + deptCode;
                        
                        if (!positionDeptRelationSet.contains(relKey)) {
                            positionDeptRelationSet.add(relKey);
                            
                            // 查询部门是否存在
                            Department dept = departmentCodeMap.get(deptCode);
                            PositionInfo position = positionMap.get(postName);
                            
                            if (dept != null && position != null) {
                                DepartmentPosition dp = new DepartmentPosition();
                                dp.setDepartmentId(dept.getId());
                                dp.setDepartmentCode(deptCode);
                                dp.setDepartmentName(department);
                                dp.setPositionId(position.getId());
                                dp.setPositionCode(position.getPositionCode());
                                dp.setPositionName(postName);
                                dp.setIsActive(true);
                                dp.setCreatedTime(new Date());
                                dp.setUpdatedTime(new Date());
                                
                                positionDeptsToSave.add(dp);
                                deptPosCount++;
                            }
                        }
                    }
                    
                    // 3. 处理员工-岗位关系
                    if (userNo != null && !userNo.trim().isEmpty()) {
                        String empCode = userNo.trim();
                        String relKey = postName + "_" + empCode;
                        
                        if (!positionEmployeeRelationSet.contains(relKey)) {
                            positionEmployeeRelationSet.add(relKey);
                            
                            // 查询员工是否存在
                            Employee emp = employeeCodeMap.get(empCode);
                            PositionInfo position = positionMap.get(postName);
                            
                            if (position != null) {
                                EmployeePositionRelation epr = new EmployeePositionRelation();
                                
                                if (emp != null) {
                                    epr.setEmployeeId(emp.getId());
                                }
                                
                                epr.setEmployeeCode(empCode);
                                epr.setEmployeeName(name);
                                epr.setPositionId(position.getId());
                                epr.setPositionCode(position.getPositionCode());
                                epr.setPositionName(postName);
                                
                                // 设置部门信息
                                if (departmentId != null && !departmentId.isEmpty()) {
                                    Department dept = departmentCodeMap.get(departmentId.trim());
                                    if (dept != null) {
                                        epr.setDepartmentId(dept.getId());
                                        epr.setDepartmentCode(departmentId.trim());
                                        epr.setDepartmentName(department);
                                    }
                                }
                                
                                epr.setIsPrimary(true);
                                epr.setWorkExp(workExp);
                                epr.setCreatedTime(new Date());
                                epr.setUpdatedTime(new Date());
                                
                                positionEmpsToSave.add(epr);
                                empPosCount++;
                            }
                        }
                    }
                    
                    // 4. 处理员工-部门关系
                    if (userNo != null && !userNo.trim().isEmpty() && 
                        departmentId != null && !departmentId.trim().isEmpty()) {
                        
                        String empCode = userNo.trim();
                        String deptCode = departmentId.trim();
                        String relKey = empCode + "_" + deptCode;
                        
                        if (!employeeDeptRelationSet.contains(relKey)) {
                            employeeDeptRelationSet.add(relKey);
                            
                            // 查询员工和部门是否存在
                            Employee emp = employeeCodeMap.get(empCode);
                            Department dept = departmentCodeMap.get(deptCode);
                            
                            if (dept != null) {
                                EmployeeDepartment ed = new EmployeeDepartment();
                                
//                                if (emp != null) {
//                                    ed.setEmployeeCode(empCode);
//                                }
                                
                                ed.setEmployeeCode(empCode);
                                ed.setEmployeeName(name);
                                ed.setDepartmentId(dept.getId());
                                ed.setDepartmentCode(deptCode);
                                ed.setDepartmentName(department);
                                ed.setRelationshipType("PRIMARY");
                                ed.setIsActive(true);
                                ed.setTeamsGroups(teamsGroups);
                                ed.setCreatedTime(new Date());
                                ed.setUpdatedTime(new Date());
                                
                                empDeptsToSave.add(ed);
                                empDeptCount++;
                            }
                        }
                    }
                }
            }
            
            // 每1000条保存一次数据
            if (positionsToSave.size() >= 1000) {
                positionInfoRepository.saveAll(positionsToSave);
                positionsToSave.clear();
            }
            
            if (positionDeptsToSave.size() >= 1000) {
                departmentPositionRepository.saveAll(positionDeptsToSave);
                positionDeptsToSave.clear();
            }
            
            if (positionEmpsToSave.size() >= 1000) {
                employeePositionRelationRepository.saveAll(positionEmpsToSave);
                positionEmpsToSave.clear();
            }
            
            if (empDeptsToSave.size() >= 1000) {
                employeeDepartmentRepository.saveAll(empDeptsToSave);
                empDeptsToSave.clear();
            }
            
            // 更新偏移量
            offset += limit;
            
            if (jsonArray.size() < limit) {
                log.info("返回数据条数小于请求条数，数据已全部同步");
                break;
            }
        }
        
        // 保存剩余数据
        if (!positionsToSave.isEmpty()) {
            positionInfoRepository.saveAll(positionsToSave);
            positionsToSave.clear();
        }
        
        if (!positionDeptsToSave.isEmpty()) {
            departmentPositionRepository.saveAll(positionDeptsToSave);
            positionDeptsToSave.clear();
        }
        
        if (!positionEmpsToSave.isEmpty()) {
            employeePositionRelationRepository.saveAll(positionEmpsToSave);
            positionEmpsToSave.clear();
        }
        
        if (!empDeptsToSave.isEmpty()) {
            employeeDepartmentRepository.saveAll(empDeptsToSave);
            empDeptsToSave.clear();
        }
        
        log.info("关系数据同步完成，共处理 {} 条记录", totalCount);
        log.info("岗位信息: {} 条, 部门-岗位关系: {} 条, 员工-岗位关系: {} 条, 员工-部门关系: {} 条",
                positionCount, deptPosCount, empPosCount, empDeptCount);

        // 返回处理统计
        Map<String, Integer> statistics = new HashMap<>();
        statistics.put("totalProcessed", totalCount);
        statistics.put("positionsCreated", positionCount);
        statistics.put("departmentPositionsCreated", deptPosCount);
        statistics.put("employeePositionsCreated", empPosCount);
        statistics.put("employeeDepartmentsCreated", empDeptCount);

        return statistics;
    }
}