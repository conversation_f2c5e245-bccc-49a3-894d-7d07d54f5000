package com.nercar.datasynchronization.service;

import java.util.Date;
import java.util.Map;

/**
 * 关系提取服务接口 - 直接从HR API获取数据并分别保存到关系表
 */
public interface RelationExtractService {
    
    /**
     * 从API直接获取数据并保存到各关系表
     * @return 处理统计结果
     */
    Map<String, Integer> syncAllRelations();
    
    /**
     * 从API获取指定时间范围内的数据并保存到各关系表
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 处理统计结果
     */
    Map<String, Integer> syncRelationsInRange(Date startDate, Date endDate);
}