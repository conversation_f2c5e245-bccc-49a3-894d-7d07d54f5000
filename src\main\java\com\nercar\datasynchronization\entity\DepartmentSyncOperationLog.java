package com.nercar.datasynchronization.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 部门同步操作日志实体类
 * 记录每次同步操作的详细信息
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "department_sync_operation_log")
public class DepartmentSyncOperationLog {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 同步日期
     */
    @Column(name = "sync_date")
    private LocalDate syncDate;

    /**
     * 组织编码
     */
    @Column(name = "org_code", length = 50)
    private String orgCode;

    /**
     * 操作类型（INSERT/UPDATE/DELETE/SKIP）
     */
    @Column(name = "operation_type", length = 20)
    private String operationType;

    /**
     * ISHISTORY字段值
     */
    @Column(name = "is_history_value")
    private Integer isHistoryValue;

    /**
     * USERPREDEF_14字段值
     */
    @Column(name = "user_predef_14_value", length = 10)
    private String userPredef14Value;

    /**
     * 本地是否已存在（0=否，1=是）
     */
    @Column(name = "local_exists")
    private Integer localExists;

    /**
     * 操作结果（SUCCESS/FAILED）
     */
    @Column(name = "operation_result", length = 20)
    private String operationResult;

    /**
     * 错误信息
     */
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;

    @PrePersist
    protected void onCreate() {
        if (createTime == null) {
            createTime = LocalDateTime.now();
        }
    }

    /**
     * 创建成功日志
     */
    public static DepartmentSyncOperationLog createSuccessLog(LocalDate syncDate, String orgCode, 
            String operationType, Integer isHistoryValue, String userPredef14Value, 
            boolean localExists, String message) {
        DepartmentSyncOperationLog log = new DepartmentSyncOperationLog();
        log.setSyncDate(syncDate);
        log.setOrgCode(orgCode);
        log.setOperationType(operationType);
        log.setIsHistoryValue(isHistoryValue);
        log.setUserPredef14Value(userPredef14Value);
        log.setLocalExists(localExists ? 1 : 0);
        log.setOperationResult("SUCCESS");
        log.setErrorMessage(message);
        return log;
    }

    /**
     * 创建失败日志
     */
    public static DepartmentSyncOperationLog createFailedLog(LocalDate syncDate, String orgCode, 
            String operationType, Integer isHistoryValue, String userPredef14Value, 
            boolean localExists, String errorMessage) {
        DepartmentSyncOperationLog log = new DepartmentSyncOperationLog();
        log.setSyncDate(syncDate);
        log.setOrgCode(orgCode);
        log.setOperationType(operationType);
        log.setIsHistoryValue(isHistoryValue);
        log.setUserPredef14Value(userPredef14Value);
        log.setLocalExists(localExists ? 1 : 0);
        log.setOperationResult("FAILED");
        log.setErrorMessage(errorMessage);
        return log;
    }
}
