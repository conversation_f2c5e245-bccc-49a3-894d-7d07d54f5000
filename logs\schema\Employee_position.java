package com.nercar.datasynchronization.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.util.Date;

@Data
@Entity
@Table(name = "employee_position")
@DynamicInsert
@DynamicUpdate
public class Employee_position {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "employee_mdm_id", length = 50)
    private String employee_mdm_id;

    @Column(name = "mdmryzzgw_v4_zbnm", length = 255)
    private String mdmryzzgw_v4_zbnm;

    @Column(name = "mdmryzzgw_v4_udef10", length = 255)
    private String mdmryzzgw_v4_udef10;

    @Column(name = "mdmryzzgw_v4_udef11", length = 255)
    private String mdmryzzgw_v4_udef11;

    @Column(name = "mdmryzzgw_v4_guid", length = 255)
    private String mdmryzzgw_v4_guid;

    @Column(name = "mdmryzzgw_v4_udef1", length = 255)
    private String mdmryzzgw_v4_udef1;

    @Column(name = "mdmryzzgw_v4_udef16", length = 255)
    private String mdmryzzgw_v4_udef16;

    @Column(name = "mdmryzzgw_v4_pcxmzy", length = 255)
    private String mdmryzzgw_v4_pcxmzy;

    @Column(name = "mdmryzzgw_v4_udef2", length = 255)
    private String mdmryzzgw_v4_udef2;

    @Column(name = "mdmryzzgw_v4_udef17", length = 255)
    private String mdmryzzgw_v4_udef17;

    @Column(name = "mdmryzzgw_v4_udef18", length = 255)
    private String mdmryzzgw_v4_udef18;

    @Column(name = "mdmryzzgw_v4_udef5", length = 255)
    private String mdmryzzgw_v4_udef5;

    @Column(name = "mdmryzzgw_v4_udef12", length = 255)
    private String mdmryzzgw_v4_udef12;

    @Column(name = "mdmryzzgw_v4_udef6", length = 255)
    private String mdmryzzgw_v4_udef6;

    @Column(name = "mdmryzzgw_v4_udef13", length = 255)
    private String mdmryzzgw_v4_udef13;

    @Column(name = "mdmryzzgw_v4_udef3", length = 255)
    private String mdmryzzgw_v4_udef3;

    @Column(name = "mdmryzzgw_v4_udef14", length = 255)
    private String mdmryzzgw_v4_udef14;

    @Column(name = "mdmryzzgw_v4_udef4", length = 255)
    private String mdmryzzgw_v4_udef4;

    @Column(name = "mdmryzzgw_v4_udef15", length = 255)
    private String mdmryzzgw_v4_udef15;

    @Column(name = "mdmryzzgw_v4_udef9", length = 255)
    private String mdmryzzgw_v4_udef9;

    @Column(name = "mdmryzzgw_v4_udef7", length = 255)
    private String mdmryzzgw_v4_udef7;

    @Column(name = "mdmryzzgw_v4_udef8", length = 255)
    private String mdmryzzgw_v4_udef8;

    @Column(name = "created_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdTime;

    @Column(name = "updated_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedTime;
}
