# =====================================================
# 部门数据迁移API测试脚本
# 用于测试department_sync_test2.sql的完整迁移功能
# =====================================================

Write-Host "=== 部门数据迁移API测试 ===" -ForegroundColor Green

# 配置
$baseUrl = "http://localhost:8080/api/migration"
$outputFile = "department_sync_test2_to_org_structure_complete.sql"

# 检查应用是否运行
Write-Host "1. 检查应用状态..." -ForegroundColor Yellow
try {
    $healthCheck = Invoke-WebRequest -Uri "http://localhost:8080/actuator/health" -Method GET -TimeoutSec 5
    if ($healthCheck.StatusCode -eq 200) {
        Write-Host "✅ 应用正在运行" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ 应用未运行，请先启动Spring Boot应用" -ForegroundColor Red
    Write-Host "启动命令: mvn spring-boot:run" -ForegroundColor Cyan
    exit 1
}

# 调用迁移API
Write-Host "2. 调用department_sync_test2.sql迁移API..." -ForegroundColor Yellow
try {
    $body = @{
        outputFile = $outputFile
    }
    
    $response = Invoke-RestMethod -Uri "$baseUrl/migrate-test2" -Method POST -Body $body -ContentType "application/x-www-form-urlencoded"
    
    if ($response.success) {
        Write-Host "✅ 迁移成功!" -ForegroundColor Green
        Write-Host "📄 输入文件: $($response.inputFile)" -ForegroundColor Cyan
        Write-Host "📄 输出文件: $($response.outputFile)" -ForegroundColor Cyan
        Write-Host "📊 结果: $($response.message)" -ForegroundColor Cyan
        Write-Host "📝 说明: $($response.description)" -ForegroundColor Cyan
    } else {
        Write-Host "❌ 迁移失败: $($response.error)" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ API调用失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 检查生成的文件
Write-Host "3. 检查生成的迁移文件..." -ForegroundColor Yellow
if (Test-Path $outputFile) {
    $fileInfo = Get-Item $outputFile
    $lineCount = (Get-Content $outputFile | Measure-Object -Line).Lines
    
    Write-Host "✅ 文件生成成功!" -ForegroundColor Green
    Write-Host "📁 文件路径: $($fileInfo.FullName)" -ForegroundColor Cyan
    Write-Host "📏 文件大小: $([math]::Round($fileInfo.Length / 1KB, 2)) KB" -ForegroundColor Cyan
    Write-Host "📄 总行数: $lineCount" -ForegroundColor Cyan
    
    # 显示文件前几行
    Write-Host "📖 文件内容预览:" -ForegroundColor Cyan
    Get-Content $outputFile -Head 10 | ForEach-Object { Write-Host "   $_" -ForegroundColor Gray }
    Write-Host "   ..." -ForegroundColor Gray
    
} else {
    Write-Host "❌ 迁移文件未生成" -ForegroundColor Red
    exit 1
}

# 获取迁移统计信息
Write-Host "4. 获取迁移统计信息..." -ForegroundColor Yellow
try {
    $statsResponse = Invoke-RestMethod -Uri "$baseUrl/statistics" -Method GET
    
    if ($statsResponse.success) {
        Write-Host "✅ 统计信息获取成功!" -ForegroundColor Green
        Write-Host "📊 统计信息:" -ForegroundColor Cyan
        Write-Host "$($statsResponse.statistics)" -ForegroundColor Gray
    }
} catch {
    Write-Host "⚠️ 统计信息获取失败: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "=== 测试完成 ===" -ForegroundColor Green
Write-Host "🎉 department_sync_test2.sql已成功迁移为PostgreSQL格式!" -ForegroundColor Green
Write-Host "📁 生成的文件: $outputFile" -ForegroundColor Cyan
Write-Host ""
Write-Host "下一步操作:" -ForegroundColor Yellow
Write-Host "1. 在PostgreSQL数据库中执行生成的SQL文件" -ForegroundColor White
Write-Host "2. 验证数据迁移结果" -ForegroundColor White
Write-Host "3. 检查层级关系完整性" -ForegroundColor White
