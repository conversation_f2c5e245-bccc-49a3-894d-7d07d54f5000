package com.nercar.datasynchronization.job;

import com.nercar.datasynchronization.client.SoapClient;
import com.nercar.datasynchronization.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

@Slf4j
@Component
public class DataSyncJob {

    @Autowired
    private SoapClient soapClient;
    
    @Autowired
    private DepartmentSyncService departmentSyncService;
    
    @Autowired
    private EmployeeSyncService employeeSyncService;
    
//    @Autowired
//    private HrUserDetailsService hrUserDetailsService;
    
//    @Autowired
//    private ParallelHrUserDetailsService parallelHrUserDetailsService;
    
//    @Autowired
//    private DataMigrationService dataMigrationService;
    
    /**
     * 每天凌晨3点执行同步任务
     */
    @Scheduled(cron = "${sync.cron}")
    public void syncData() {
        log.info("开始执行数据同步任务");
        try {
            // 获取昨天的开始和结束时间
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DATE, -1);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            Date startDate = calendar.getTime();

            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            Date endDate = calendar.getTime();

            // 同步部门数据
            syncDepartmentData(startDate, endDate);

            // 同步员工数据
            syncEmployeeData(startDate, endDate);

//            // 同步岗位数据 - 使用多线程版本
//            syncHrUserDetailsParallel();

            // 为确保关联关系正确，手动触发一次本次同步时间范围内的数据关联
//            log.info("开始执行增量数据关联检查");

//            // 创建员工-部门关联，传入实际数据时间范围
//            log.info("创建员工-部门关联关系，时间范围从 {} 到 {}",
//                    new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(startDate),
//                    new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(endDate));
//            dataMigrationService.createEmployeeDepartmentRelations(startDate, endDate);

            // 创建岗位相关关联 - 不需要传递时间参数，直接使用无参方法
//            log.info("创建岗位相关关联关系，使用所有岗位数据");
//            dataMigrationService.createPositionRelations();
            // 创建岗位相关关联 - 使用并行处理版本
//            log.info("并行创建岗位相关关联关系，使用所有岗位数据");
//            dataMigrationService.createPositionRelationsParallel();
//            log.info("增量数据关联检查完成");

            log.info("数据同步任务完成");
        } catch (Exception e) {
            log.error("数据同步任务执行失败", e);
        }
    }
    
    /**
     * 同步部门数据
     */
    private void syncDepartmentData(Date startDate, Date endDate) {
        try {
            log.info("开始同步部门数据，时间范围从 {} 到 {}",
                    new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(startDate),
                    new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(endDate));
            
            String soapResponse = soapClient.getOrgInfo(startDate, endDate);
            if (soapResponse != null) {
                departmentSyncService.syncDepartments(soapResponse);
            }
            log.info("部门数据同步完成");
        } catch (Exception e) {
            log.error("同步部门数据失败", e);
        }
    }
    
    /**
     * 同步员工数据
     */
    private void syncEmployeeData(Date startDate, Date endDate) {
        try {
            log.info("开始同步员工数据，时间范围从 {} 到 {}",
                    new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(startDate),
                    new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(endDate));
                    
            String soapResponse = soapClient.getUserInfo(startDate, endDate);
            if (soapResponse != null) {
                // 修改为传递时间范围参数
                employeeSyncService.syncEmployees(soapResponse, startDate, endDate);
            }
            log.info("员工数据同步完成");
        } catch (Exception e) {
            log.error("同步员工数据失败", e);
        }
    }
    
//    /**
//     * 同步岗位数据 - 使用多线程
//     */
//    private void syncHrUserDetailsParallel() {
//        try {
//            log.info("开始多线程同步岗位数据");
//            parallelHrUserDetailsService.updateAllUserDetailFromErpParallel();
//            log.info("岗位数据同步完成");
//        } catch (Exception e) {
//            log.error("同步岗位数据失败", e);
//        }
//    }
}