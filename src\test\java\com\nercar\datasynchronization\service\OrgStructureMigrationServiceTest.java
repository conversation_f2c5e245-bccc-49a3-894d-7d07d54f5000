package com.nercar.datasynchronization.service;

import com.nercar.datasynchronization.entity.Department;
import com.nercar.datasynchronization.repository.DepartmentRepository;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 组织架构迁移服务集成测试
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
class OrgStructureMigrationServiceTest {

    @Autowired
    private OrgStructureMigrationService migrationService;

    @Autowired
    private DepartmentRepository departmentRepository;

    @Autowired
    @Qualifier("postgresqlJdbcTemplate")
    private JdbcTemplate postgresqlJdbcTemplate;

    @BeforeEach
    void setUp() {
        log.info("开始测试前的准备工作...");
        
        // 清理测试数据
        try {
            migrationService.clearOrgStructureData();
            log.info("测试数据清理完成");
        } catch (Exception e) {
            log.warn("清理测试数据时发生异常: {}", e.getMessage());
        }
    }

    @Test
    void testPostgreSQLConnection() {
        log.info("测试PostgreSQL连接...");
        
        boolean connected = migrationService.checkPostgreSQLConnection();
        assertThat(connected).isTrue();
        
        log.info("PostgreSQL连接测试通过");
    }

    @Test
    void testQueryActiveDepartments() {
        log.info("测试查询正常状态部门...");
        
        List<Department> activeDepartments = departmentRepository.findActiveDepartments();
        log.info("查询到 {} 个正常状态部门", activeDepartments.size());
        
        assertThat(activeDepartments).isNotNull();
        
        // 验证查询条件
        for (Department dept : activeDepartments) {
            assertThat(dept.getIsHistory()).isEqualTo(0);
            assertThat(dept.getUserPredef14()).isNotEqualTo("D");
        }
        
        log.info("正常状态部门查询测试通过");
    }

    @Test
    void testQueryRootDepartments() {
        log.info("测试查询根节点部门...");
        
        List<Department> rootDepartments = departmentRepository.findRootDepartments();
        log.info("查询到 {} 个根节点部门", rootDepartments.size());
        
        assertThat(rootDepartments).isNotNull();
        
        // 验证根节点条件
        for (Department dept : rootDepartments) {
            assertThat(dept.getIsHistory()).isEqualTo(0);
            assertThat(dept.getUserPredef14()).isNotEqualTo("D");
            assertThat(dept.getParentId() == null || dept.getParentId().isEmpty()).isTrue();
        }
        
        log.info("根节点部门查询测试通过");
    }

    @Test
    void testFullMigrationProcess() {
        log.info("测试完整迁移流程...");
        
        // 1. 获取迁移前的统计信息
        long mysqlCount = departmentRepository.countActiveDepartments();
        log.info("MySQL中正常部门数量: {}", mysqlCount);
        
        if (mysqlCount == 0) {
            log.warn("MySQL中没有正常状态的部门数据，跳过迁移测试");
            return;
        }
        
        // 2. 执行迁移
        migrationService.migrateToOrgStructure(true);
        log.info("迁移执行完成");
        
        // 3. 验证迁移结果
        int result = migrationService.validateMigrationResult();
        assertThat(result).isGreaterThan(0);
        log.info("迁移验证通过，迁移记录数: {}", result);
        
        // 4. 检查数据数量
        String countSql = "SELECT COUNT(*) FROM t_org_structure WHERE data_source = 2";
        Long postgresqlCount = postgresqlJdbcTemplate.queryForObject(countSql, Long.class);
        assertThat(postgresqlCount).isEqualTo(mysqlCount);
        log.info("数据数量验证通过: MySQL={}, PostgreSQL={}", mysqlCount, postgresqlCount);
        
        // 5. 检查根节点
        String rootCountSql = "SELECT COUNT(*) FROM t_org_structure WHERE data_source = 2 AND pre_id IS NULL";
        Long rootCount = postgresqlJdbcTemplate.queryForObject(rootCountSql, Long.class);
        assertThat(rootCount).isGreaterThan(0);
        log.info("根节点数量: {}", rootCount);
        
        // 6. 获取统计信息
        String statistics = migrationService.getMigrationStatistics();
        assertThat(statistics).isNotNull();
        log.info("迁移统计信息:\n{}", statistics);
        
        log.info("完整迁移流程测试通过");
    }

    @Test
    void testClearMigrationData() {
        log.info("测试清理迁移数据...");
        
        // 1. 先执行一次迁移
        long mysqlCount = departmentRepository.countActiveDepartments();
        if (mysqlCount > 0) {
            migrationService.migrateToOrgStructure(false);
            
            // 2. 验证数据存在
            String countSql = "SELECT COUNT(*) FROM t_org_structure WHERE data_source = 2";
            Long beforeClear = postgresqlJdbcTemplate.queryForObject(countSql, Long.class);
            assertThat(beforeClear).isGreaterThan(0);
            log.info("清理前记录数: {}", beforeClear);
            
            // 3. 执行清理
            migrationService.clearOrgStructureData();
            
            // 4. 验证清理结果
            Long afterClear = postgresqlJdbcTemplate.queryForObject(countSql, Long.class);
            assertThat(afterClear).isEqualTo(0);
            log.info("清理后记录数: {}", afterClear);
        }
        
        log.info("清理迁移数据测试通过");
    }

    @Test
    void testMigrationStatistics() {
        log.info("测试迁移统计信息...");
        
        String statistics = migrationService.getMigrationStatistics();
        assertThat(statistics).isNotNull();
        assertThat(statistics).contains("迁移统计信息");
        
        log.info("统计信息:\n{}", statistics);
        log.info("迁移统计信息测试通过");
    }

    @Test
    void testValidationWithoutMigration() {
        log.info("测试未迁移时的验证...");
        
        // 清理数据后直接验证
        migrationService.clearOrgStructureData();
        
        int result = migrationService.validateMigrationResult();
        
        // 如果MySQL中有数据但PostgreSQL中没有，应该返回-1
        long mysqlCount = departmentRepository.countActiveDepartments();
        if (mysqlCount > 0) {
            assertThat(result).isEqualTo(-1);
            log.info("验证结果符合预期: MySQL有数据但PostgreSQL无数据");
        } else {
            assertThat(result).isEqualTo(0);
            log.info("验证结果符合预期: MySQL和PostgreSQL都无数据");
        }
        
        log.info("未迁移时的验证测试通过");
    }
}
