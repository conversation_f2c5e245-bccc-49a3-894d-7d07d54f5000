package com.nercar.datasynchronization.repository;

import com.nercar.datasynchronization.entity.PositionInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface PositionInfoRepository extends JpaRepository<PositionInfo, String> {
    
    PositionInfo findByPositionCode(String positionCode);
    
    PositionInfo findByPositionName(String positionName);
}