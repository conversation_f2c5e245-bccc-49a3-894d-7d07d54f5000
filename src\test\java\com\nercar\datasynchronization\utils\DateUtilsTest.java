package com.nercar.datasynchronization.utils;

import org.junit.jupiter.api.Test;
import java.util.Date;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 日期工具类测试
 */
public class DateUtilsTest {
    
    @Test
    public void testParseStandardDateTime() {
        // 测试标准日期时间格式
        String dateStr = "2024-01-01 00:00:00";
        Date date = DateUtils.parseDate(dateStr);
        assertNotNull(date);
        assertEquals("2024-01-01 00:00:00", DateUtils.formatDate(date));
    }
    
    @Test
    public void testParseUrlEncodedDateTime() {
        // 测试URL编码的日期时间格式
        String dateStr = "2024-01-01%2000:00:00";
        Date date = DateUtils.parseDate(dateStr);
        assertNotNull(date);
        assertEquals("2024-01-01 00:00:00", DateUtils.formatDate(date));
    }
    
    @Test
    public void testParseSlashDateTime() {
        // 测试斜杠分隔的日期时间格式
        String dateStr = "2024/01/01 00:00:00";
        Date date = DateUtils.parseDate(dateStr);
        assertNotNull(date);
    }
    
    @Test
    public void testParseDateOnly() {
        // 测试仅日期格式
        String dateStr = "2024-01-01";
        Date date = DateUtils.parseDate(dateStr);
        assertNotNull(date);
    }
    
    @Test
    public void testParseISODateTime() {
        // 测试ISO格式
        String dateStr = "2024-01-01T00:00:00";
        Date date = DateUtils.parseDate(dateStr);
        assertNotNull(date);
    }
    
    @Test
    public void testParseInvalidDate() {
        // 测试无效日期格式
        String dateStr = "invalid-date";
        assertThrows(IllegalArgumentException.class, () -> {
            DateUtils.parseDate(dateStr);
        });
    }
    
    @Test
    public void testParseNullDate() {
        // 测试空日期
        assertThrows(IllegalArgumentException.class, () -> {
            DateUtils.parseDate(null);
        });
    }
    
    @Test
    public void testParseEmptyDate() {
        // 测试空字符串
        assertThrows(IllegalArgumentException.class, () -> {
            DateUtils.parseDate("");
        });
    }
    
    @Test
    public void testIsValidDate() {
        // 测试日期验证
        assertTrue(DateUtils.isValidDate("2024-01-01 00:00:00"));
        assertTrue(DateUtils.isValidDate("2024-01-01%2000:00:00"));
        assertTrue(DateUtils.isValidDate("2024/01/01"));
        assertFalse(DateUtils.isValidDate("invalid-date"));
        assertFalse(DateUtils.isValidDate(null));
    }
    
    @Test
    public void testGetSupportedPatterns() {
        // 测试获取支持的格式
        String[] patterns = DateUtils.getSupportedPatterns();
        assertNotNull(patterns);
        assertTrue(patterns.length > 0);
    }
}
