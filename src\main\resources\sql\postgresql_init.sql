-- PostgreSQL初始化脚本
-- 用于创建t_org_structure表的序列和相关对象

-- 创建序列用于生成t_org_structure表的ID
CREATE SEQUENCE IF NOT EXISTS t_org_structure_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

-- 设置序列的所有者（如果表已存在）
-- ALTER SEQUENCE t_org_structure_id_seq OWNED BY t_org_structure.id;

-- 如果需要创建t_org_structure表（根据实际情况决定是否执行）
/*
CREATE TABLE IF NOT EXISTS t_org_structure (
    id BIGINT NOT NULL DEFAULT nextval('t_org_structure_id_seq'),
    organ_name VARCHAR(255),
    pre_id BIGINT,
    order_info INTEGER,
    is_del BOOLEAN,
    create_time TIMESTAMP(6),
    modify_time TIMESTAMP(6),
    data_source INTEGER DEFAULT 1,
    PRIMARY KEY (id)
);
*/

-- 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_t_org_structure_pre_id ON t_org_structure(pre_id);
CREATE INDEX IF NOT EXISTS idx_t_org_structure_data_source ON t_org_structure(data_source);
CREATE INDEX IF NOT EXISTS idx_t_org_structure_organ_name ON t_org_structure(organ_name);

-- 注释说明
COMMENT ON SEQUENCE t_org_structure_id_seq IS '组织架构表ID序列';
COMMENT ON TABLE t_org_structure IS '组织架构表';
COMMENT ON COLUMN t_org_structure.id IS '主键ID';
COMMENT ON COLUMN t_org_structure.organ_name IS '组织名称';
COMMENT ON COLUMN t_org_structure.pre_id IS '父级ID，根节点为空';
COMMENT ON COLUMN t_org_structure.order_info IS '顺序，从1开始';
COMMENT ON COLUMN t_org_structure.is_del IS '是否删除';
COMMENT ON COLUMN t_org_structure.create_time IS '创建时间';
COMMENT ON COLUMN t_org_structure.modify_time IS '修改时间';
COMMENT ON COLUMN t_org_structure.data_source IS '数据来源，1=页面输入，2=数据同步';
