# 🚀 部门同步测试快速启动指南

## 📋 前置条件

1. ✅ 已创建测试表：`department_sync_test` 和 `department_sync_operation_log`
2. ✅ 项目已编译并启动
3. ✅ 数据库连接正常
4. ✅ SOAP接口可用

## 🎯 快速测试步骤

### 第1步：健康检查
```bash
curl -X GET "http://localhost:8080/api/sync/test/health"
```

**期望结果**：
```json
{
  "status": "UP",
  "service": "DepartmentSyncTest",
  "timestamp": 1704067200000
}
```

### 第2步：清理测试环境
```bash
curl -X DELETE "http://localhost:8080/api/sync/test/departments/clear"
```

**期望结果**：
```json
{
  "success": true,
  "message": "测试数据清理完成"
}
```

### 第3步：单天同步测试
```bash
# 测试昨天的数据
curl -X POST "http://localhost:8080/api/sync/test/departments/daily?syncDate=2024-07-06"
```

**期望结果**：
```json
{
  "success": true,
  "message": "同步完成",
  "syncDate": "2024-07-06",
  "processedCount": 50,
  "insertCount": 45,
  "updateCount": 3,
  "deleteCount": 1,
  "skipCount": 1,
  "repairedCount": 0,
  "deletedSpecialCount": 0
}
```

### 第4步：查看数据统计
```bash
curl -X GET "http://localhost:8080/api/sync/test/departments/statistics"
```

**期望结果**：
```json
{
  "totalCount": 49,
  "normalCount": 48,
  "historyCount": 1,
  "deletedCount": 0,
  "rootDepartmentCount": 1,
  "brokenRelationCount": 0
}
```

### 第5步：验证数据完整性
```bash
curl -X GET "http://localhost:8080/api/sync/test/departments/validate"
```

**期望结果**：
```json
{
  "isValid": true,
  "issues": [],
  "brokenRelationCount": 0
}
```

### 第6步：查看同步日志
```bash
curl -X GET "http://localhost:8080/api/sync/test/departments/logs?syncDate=2024-07-06"
```

**期望结果**：
```json
{
  "syncDate": "2024-07-06",
  "operationCounts": {
    "INSERT": 45,
    "UPDATE": 3,
    "DELETE": 1,
    "SKIP": 1
  },
  "resultCounts": {
    "SUCCESS": 50,
    "FAILED": 0
  },
  "failedOperations": 0,
  "failedDetails": []
}
```

## 📊 扩展测试

### 测试一周数据
```bash
curl -X POST "http://localhost:8080/api/sync/test/departments/full?startDate=2024-07-01&endDate=2024-07-07"
```

### 测试关系修复
```bash
curl -X POST "http://localhost:8080/api/sync/test/departments/repair"
```

## 🔍 数据库验证

### 查看测试表数据
```sql
-- 查看部门数据
SELECT org_code, org_name, parent_code, is_history, sync_date 
FROM department_sync_test 
ORDER BY sync_date DESC, org_code 
LIMIT 10;

-- 查看操作日志
SELECT sync_date, operation_type, COUNT(*) as count
FROM department_sync_operation_log 
GROUP BY sync_date, operation_type 
ORDER BY sync_date DESC;
```

### 检查数据质量
```sql
-- 检查父子关系
SELECT COUNT(*) as broken_relations
FROM department_sync_test d1 
WHERE d1.parent_code IS NOT NULL 
AND d1.parent_code != '' 
AND d1.parent_code NOT IN (
    SELECT d2.org_code FROM department_sync_test d2
);

-- 检查重复数据
SELECT org_code, COUNT(*) as count
FROM department_sync_test 
GROUP BY org_code 
HAVING COUNT(*) > 1;
```

## ⚠️ 常见问题

### 问题1：同步返回0条数据
**原因**：指定日期可能没有数据更新
**解决**：尝试其他日期，或检查ERP接口返回

### 问题2：父子关系异常
**原因**：父级部门可能在后续日期才同步
**解决**：执行关系修复接口

### 问题3：同步失败
**原因**：网络问题或ERP接口异常
**解决**：查看错误日志，重试同步

## 📈 性能监控

### 监控指标
- 同步耗时
- 处理数据量
- 错误率
- 内存使用

### 日志位置
```
logs/data-synchronization.log
```

### 关键日志
```
开始同步日期: 2024-07-06
解析到 50 条ERP部门数据
特殊数据处理完成，删除了 0 个特殊部门
关系修复完成，修复了 0 个部门的父子关系
```

## 🎯 成功标准

### 数据完整性
- ✅ 所有部门都能正确同步
- ✅ 父子关系完整无异常
- ✅ 删除操作正确执行
- ✅ 无重复数据

### 性能表现
- ✅ 单天同步时间 < 30秒
- ✅ 内存使用稳定
- ✅ 无内存泄漏

### 日志质量
- ✅ 操作日志完整
- ✅ 错误信息详细
- ✅ 统计数据准确

## 📞 技术支持

如果遇到问题，请提供：
1. 错误日志
2. 同步参数
3. 数据库状态
4. 系统环境信息

---

**🎉 恭喜！如果所有测试都通过，说明新的同步逻辑工作正常！**
