package com.nercar.datasynchronization.utils;

import lombok.extern.slf4j.Slf4j;

import java.util.function.Supplier;

/**
 * 重试工具类
 * 提供通用的重试机制，用于处理网络超时、服务暂时不可用等临时性错误
 */
@Slf4j
public class RetryUtils {
    
    /**
     * 默认重试次数
     */
    public static final int DEFAULT_MAX_RETRIES = 3;
    
    /**
     * 默认重试间隔（毫秒）
     */
    public static final long DEFAULT_RETRY_DELAY = 2000;
    
    /**
     * 执行带重试的操作
     * 
     * @param operation 要执行的操作
     * @param maxRetries 最大重试次数
     * @param retryDelay 重试间隔（毫秒）
     * @param operationName 操作名称（用于日志）
     * @param <T> 返回类型
     * @return 操作结果
     * @throws Exception 如果所有重试都失败
     */
    public static <T> T executeWithRetry(Supplier<T> operation, int maxRetries, long retryDelay, String operationName) throws Exception {
        Exception lastException = null;
        
        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                log.info("🔄 [重试机制] 执行操作: {} (第 {}/{} 次尝试)", operationName, attempt, maxRetries);
                
                T result = operation.get();
                
                if (attempt > 1) {
                    log.info("✅ [重试成功] 操作 {} 在第 {} 次尝试后成功", operationName, attempt);
                }
                
                return result;
                
            } catch (Exception e) {
                lastException = e;

                // 详细的错误分类日志
                String errorType = classifyError(e);
                log.warn("❌ [重试失败] 操作 {} 第 {}/{} 次尝试失败 [{}]: {}",
                        operationName, attempt, maxRetries, errorType, e.getMessage());

                // 如果是最后一次尝试，不再等待
                if (attempt < maxRetries) {
                    // 检查是否是可重试的错误
                    if (isRetryableException(e)) {
                        log.info("⏳ [重试等待] 等待 {} 毫秒后进行第 {} 次重试... (错误类型: {})",
                                retryDelay, attempt + 1, errorType);
                        try {
                            Thread.sleep(retryDelay);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            throw new RuntimeException("重试过程被中断", ie);
                        }
                    } else {
                        log.error("💥 [不可重试] 操作 {} 遇到不可重试的错误 [{}]，停止重试: {}",
                                operationName, errorType, e.getMessage());
                        break;
                    }
                } else {
                    log.error("💥 [重试耗尽] 操作 {} 在 {} 次尝试后仍然失败，最后错误: [{}] {}",
                            operationName, maxRetries, errorType, e.getMessage());
                }
            }
        }
        
        // 所有重试都失败了，抛出最后一个异常
        throw new RuntimeException(String.format("操作 %s 在 %d 次重试后仍然失败", operationName, maxRetries), lastException);
    }
    
    /**
     * 使用默认参数执行带重试的操作
     * 
     * @param operation 要执行的操作
     * @param operationName 操作名称
     * @param <T> 返回类型
     * @return 操作结果
     * @throws Exception 如果所有重试都失败
     */
    public static <T> T executeWithRetry(Supplier<T> operation, String operationName) throws Exception {
        return executeWithRetry(operation, DEFAULT_MAX_RETRIES, DEFAULT_RETRY_DELAY, operationName);
    }
    
    /**
     * 对错误进行分类
     *
     * @param exception 异常
     * @return 错误类型描述
     */
    private static String classifyError(Exception exception) {
        String message = exception.getMessage();
        if (message == null) {
            return "未知错误";
        }

        // 网络超时
        if (message.contains("timeout") || message.contains("超时") ||
            exception instanceof java.net.SocketTimeoutException) {
            return "网络超时";
        }

        // HTTP错误
        if (message.contains("HTTP_STATUS_CODE:499")) {
            return "HTTP 499错误";
        }

        // ESB服务异常
        if (message.contains("ESB调用MDM服务") && message.contains("异常")) {
            return "ESB服务异常";
        }

        // 连接错误
        if (message.contains("Connection")) {
            if (message.contains("timed out")) {
                return "连接超时";
            } else if (message.contains("reset") || message.contains("refused")) {
                return "连接错误";
            }
        }

        // 服务不可用
        if (message.contains("Service Unavailable") || message.contains("服务不可用")) {
            return "服务不可用";
        }

        // 网关超时
        if (message.contains("504") || message.contains("Gateway Time-out")) {
            return "网关超时";
        }

        // 远程MDM错误
        if (message.contains("远程MDM服务")) {
            return "远程MDM错误";
        }

        // 其他网络异常
        if (exception instanceof java.net.ConnectException ||
            exception instanceof java.net.NoRouteToHostException ||
            exception instanceof java.net.UnknownHostException ||
            exception instanceof java.net.SocketTimeoutException) {
            return "网络连接异常";
        }

        return "其他错误";
    }

    /**
     * 判断异常是否可重试
     *
     * @param exception 异常
     * @return 是否可重试
     */
    private static boolean isRetryableException(Exception exception) {
        String message = exception.getMessage();
        if (message == null) {
            return false;
        }
        
        // 网络超时相关错误
        if (message.contains("timeout") || message.contains("超时")) {
            return true;
        }
        
        // HTTP状态码499（客户端关闭连接）
        if (message.contains("HTTP_STATUS_CODE:499")) {
            return true;
        }
        
        // ESB服务调用异常
        if (message.contains("ESB调用MDM服务") && message.contains("异常")) {
            return true;
        }
        
        // 连接相关错误
        if (message.contains("Connection") && (message.contains("reset") || message.contains("refused") || message.contains("timed out"))) {
            return true;
        }
        
        // 服务不可用
        if (message.contains("Service Unavailable") || message.contains("服务不可用")) {
            return true;
        }

        // 网关超时（504错误）
        if (message.contains("504") || message.contains("Gateway Time-out")) {
            return true;
        }
        
        // 其他临时性错误
        if (message.contains("SocketTimeoutException") ||
            message.contains("ConnectTimeoutException") ||
            message.contains("NoRouteToHostException")) {
            return true;
        }

        // 直接检查异常类型
        if (exception instanceof java.net.SocketTimeoutException ||
            exception instanceof java.net.ConnectException ||
            exception instanceof java.net.NoRouteToHostException ||
            exception instanceof java.net.UnknownHostException) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 计算指数退避延迟时间
     * 
     * @param attempt 当前尝试次数（从1开始）
     * @param baseDelay 基础延迟时间（毫秒）
     * @param maxDelay 最大延迟时间（毫秒）
     * @return 计算后的延迟时间
     */
    public static long calculateExponentialBackoffDelay(int attempt, long baseDelay, long maxDelay) {
        long delay = baseDelay * (long) Math.pow(2, attempt - 1);
        return Math.min(delay, maxDelay);
    }
    
    /**
     * 执行带指数退避的重试操作
     * 
     * @param operation 要执行的操作
     * @param maxRetries 最大重试次数
     * @param baseDelay 基础延迟时间（毫秒）
     * @param maxDelay 最大延迟时间（毫秒）
     * @param operationName 操作名称
     * @param <T> 返回类型
     * @return 操作结果
     * @throws Exception 如果所有重试都失败
     */
    public static <T> T executeWithExponentialBackoff(Supplier<T> operation, int maxRetries, 
                                                      long baseDelay, long maxDelay, String operationName) throws Exception {
        Exception lastException = null;
        
        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                log.info("🔄 [指数退避重试] 执行操作: {} (第 {}/{} 次尝试)", operationName, attempt, maxRetries);
                
                T result = operation.get();
                
                if (attempt > 1) {
                    log.info("✅ [重试成功] 操作 {} 在第 {} 次尝试后成功", operationName, attempt);
                }
                
                return result;
                
            } catch (Exception e) {
                lastException = e;
                
                log.warn("❌ [重试失败] 操作 {} 第 {}/{} 次尝试失败: {}", 
                        operationName, attempt, maxRetries, e.getMessage());
                
                if (attempt < maxRetries && isRetryableException(e)) {
                    long delay = calculateExponentialBackoffDelay(attempt, baseDelay, maxDelay);
                    log.info("⏳ [指数退避等待] 等待 {} 毫秒后进行第 {} 次重试...", delay, attempt + 1);
                    
                    try {
                        Thread.sleep(delay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("重试过程被中断", ie);
                    }
                } else {
                    break;
                }
            }
        }
        
        throw new RuntimeException(String.format("操作 %s 在 %d 次重试后仍然失败", operationName, maxRetries), lastException);
    }
}
