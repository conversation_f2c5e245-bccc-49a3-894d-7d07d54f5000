-- =====================================================
-- 基于department_sync_test2.sql的完整部门层级重构
-- 数据量：1399条记录
-- 生成时间：2025-07-25
-- =====================================================

-- 创建重构后的完整部门表
DROP TABLE IF EXISTS department_hierarchy_complete;
CREATE TABLE department_hierarchy_complete (
    id INT AUTO_INCREMENT PRIMARY KEY,
    new_dept_code VARCHAR(20) NOT NULL UNIQUE,
    dept_name VARCHAR(200) NOT NULL,
    dept_level INT NOT NULL,
    parent_code VARCHAR(20),
    full_path VARCHAR(1000),
    dept_type VARCHAR(50),
    original_org_code VARCHAR(20),
    original_org_name VARCHAR(200),
    original_parent_code VARCHAR(20),
    original_full_name VARCHAR(500),
    is_active TINYINT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_original_org_code (original_org_code),
    INDEX idx_parent_code (parent_code),
    INDEX idx_dept_level (dept_level),
    INDEX idx_dept_type (dept_type)
);

-- =====================================================
-- 基于full_name智能分析的层级重构策略
-- =====================================================

-- 第一步：插入所有一级部门（根据full_name分析）
INSERT INTO department_hierarchy_complete (new_dept_code, dept_name, dept_level, parent_code, full_path, dept_type, original_org_code, original_org_name, original_parent_code, original_full_name) VALUES

-- 事业部级别
('L1_001', '板材事业部', 1, NULL, '板材事业部', '事业部', 'X50000000', '板材事业部', '1', '板材事业部'),
('L1_002', '特钢事业部', 1, NULL, '特钢事业部', '事业部', 'X49000000', '特钢事业部', '1', '特钢事业部'),
('L1_003', '炼铁事业部', 1, NULL, '炼铁事业部', '事业部', 'X47000000', '炼铁事业部', '1', '炼铁事业部'),
('L1_004', '能源动力事业部', 1, NULL, '能源动力事业部', '事业部', 'X48000000', '能源动力事业部', '1', '能源动力事业部'),

-- 中心级别
('L1_005', '物流中心', 1, NULL, '物流中心', '中心', 'X52000000', '物流中心', '1', '物流中心'),
('L1_006', '采购中心', 1, NULL, '采购中心', '中心', 'X53000000', '采购中心', '1', '采购中心'),

-- 集团级别
('L1_007', '新产业投资集团', 1, NULL, '新产业投资集团', '集团', 'X57000000', '新产业投资集团', '1', '新产业投资集团'),
('L1_008', '蔚蓝高科技集团', 1, NULL, '蔚蓝高科技集团', '集团', 'XB1000000', '蔚蓝高科技集团', '1', '蔚蓝高科技集团'),

-- 公司级别
('L1_009', '江苏南钢鑫洋供应链有限公司', 1, NULL, '江苏南钢鑫洋供应链有限公司', '公司', 'X79000000', '江苏南钢鑫洋供应链有限公司', '1', '江苏南钢鑫洋供应链有限公司'),
('L1_010', '江苏金珂水务有限公司', 1, NULL, '江苏金珂水务有限公司', '公司', 'X83000000', '江苏金珂水务有限公司', '1', '江苏金珂水务有限公司'),
('L1_011', '南京钢铁集团国际经济贸易有限公司', 1, NULL, '南京钢铁集团国际经济贸易有限公司', '公司', 'X36000000', '南京钢铁集团国际经济贸易有限公司', '1', '南京钢铁集团国际经济贸易有限公司'),
('L1_012', '南京三金房地产开发有限公司', 1, NULL, '南京三金房地产开发有限公司', '公司', 'XC5000000', '南京三金房地产开发有限公司', '1', '南京三金房地产开发有限公司'),
('L1_013', '南京金智工程技术有限公司', 1, NULL, '南京金智工程技术有限公司', '公司', 'XA1000000', '南京金智工程技术有限公司', '1', '南京金智工程技术有限公司'),
('L1_014', '香港金腾公司', 1, NULL, '香港金腾公司', '公司', 'X30000000', '香港金腾公司', '1', '香港金腾公司'),
('L1_015', '南京鑫智链科技信息有限公司2', 1, NULL, '南京鑫智链科技信息有限公司2', '公司', 'X21000000', '南京鑫智链科技信息有限公司2', '1', '南京鑫智链科技信息有限公司2'),

-- 职能部门
('L1_016', '科技质量部', 1, NULL, '科技质量部', '部门', 'X10000000', '科技质量部', '1', '科技质量部'),
('L1_017', '人力资源部', 1, NULL, '人力资源部', '部门', 'X02000000', '人力资源部', '1', '人力资源部'),
('L1_018', '财务部', 1, NULL, '财务部', '部门', 'X06000000', '财务部', '1', '财务部'),
('L1_019', '公司办公室', 1, NULL, '公司办公室', '部门', 'X01000000', '公司办公室', '1', '公司办公室'),
('L1_020', '安全环保部', 1, NULL, '安全环保部', '部门', 'X28000000', '安全环保部', '1', '安全环保部'),
('L1_021', '制造部', 1, NULL, '制造部', '部门', 'X51000000', '制造部', '1', '制造部'),
('L1_022', '新材料研究院（合署）', 1, NULL, '新材料研究院（合署）', '研究院', 'X27000000', '新材料研究院（合署）', '1', '新材料研究院（合署）'),
('L1_023', '数字应用研究院（人工智能研究院）', 1, NULL, '数字应用研究院（人工智能研究院）', '研究院', 'XB2000000', '数字应用研究院（人工智能研究院）', '1', '数字应用研究院（人工智能研究院）'),
('L1_024', '工会', 1, NULL, '工会', '部门', 'X17000000', '工会', '1', '工会'),
('L1_025', '保卫部', 1, NULL, '保卫部', '部门', 'X15000000', '保卫部', '1', '保卫部'),
('L1_026', '审计部', 1, NULL, '审计部', '部门', 'XB3000000', '审计部', '1', '审计部'),
('L1_027', '风险合规部', 1, NULL, '风险合规部', '部门', 'X08000000', '风险合规部', '1', '风险合规部'),
('L1_028', '董事会办公室', 1, NULL, '董事会办公室', '部门', 'XC7000000', '董事会办公室', '1', '董事会办公室'),
('L1_029', '党委办公室', 1, NULL, '党委办公室', '部门', 'XB4000000', '党委办公室', '1', '党委办公室'),
('L1_030', '组织部', 1, NULL, '组织部', '部门', 'XB5000000', '组织部', '1', '组织部'),
('L1_031', '党委工作部', 1, NULL, '党委工作部', '部门', 'XB7000000', '党委工作部', '1', '党委工作部'),
('L1_032', '纪委办公室（党风廉政办公室）', 1, NULL, '纪委办公室（党风廉政办公室）', '部门', 'X29000000', '纪委办公室（党风廉政办公室）', '1', '纪委办公室（党风廉政办公室）'),
('L1_033', '企业文化部', 1, NULL, '企业文化部', '部门', 'X03000000', '企业文化部', '1', '企业文化部'),
('L1_034', '战略运营部（产业发展研究院）', 1, NULL, '战略运营部（产业发展研究院）', '部门', 'X04000000', '战略运营部（产业发展研究院）', '1', '战略运营部（产业发展研究院）'),
('L1_035', '市场部', 1, NULL, '市场部', '部门', 'X24000000', '市场部', '1', '市场部'),
('L1_036', '证券部', 1, NULL, '证券部', '部门', 'X26000000', '证券部', '1', '证券部'),
('L1_037', '公司领导', 1, NULL, '公司领导', '领导层', 'X00000000', '公司领导', '1', '公司领导'),
('L1_038', '集团领导', 1, NULL, '集团领导', '领导层', 'XA0000000', '集团领导', '1', '集团领导');

-- =====================================================
-- 第二步：插入二级部门（厂/处级别）
-- =====================================================

INSERT INTO department_hierarchy_complete (new_dept_code, dept_name, dept_level, parent_code, full_path, dept_type, original_org_code, original_org_name, original_parent_code, original_full_name) VALUES

-- 板材事业部下属
('L2_001', '中厚板卷厂', 2, 'L1_001', '板材事业部 -> 中厚板卷厂', '厂', 'X32000000', '中厚板卷厂', 'X50000000', '板材事业部中厚板卷厂'),
('L2_002', '宽厚板厂', 2, 'L1_001', '板材事业部 -> 宽厚板厂', '厂', 'X38000000', '宽厚板厂', 'X50000000', '板材事业部宽厚板厂'),
('L2_003', '第一炼钢厂', 2, 'L1_001', '板材事业部 -> 第一炼钢厂', '厂', 'X73000000', '第一炼钢厂', 'X50000000', '板材事业部第一炼钢厂'),
('L2_004', '中板厂', 2, 'L1_001', '板材事业部 -> 中板厂', '厂', 'X66000000', '中板厂', 'X50000000', '板材事业部中板厂'),
('L2_005', '金石材料厂', 2, 'L1_001', '板材事业部 -> 金石材料厂', '厂', 'X84000000', '金石材料厂', 'X50000000', '板材事业部金石材料厂'),
('L2_006', '技术研发处', 2, 'L1_001', '板材事业部 -> 技术研发处', '处', 'X50070000', '技术研发处', 'X50000000', '板材事业部技术研发处'),
('L2_007', '设备处', 2, 'L1_001', '板材事业部 -> 设备处', '处', 'X50040000', '设备处', 'X50000000', '板材事业部设备处'),
('L2_008', '江苏南钢板材销售有限公司', 2, 'L1_001', '板材事业部 -> 江苏南钢板材销售有限公司', '公司', 'X80000000', '江苏南钢板材销售有限公司', 'X50000000', '板材事业部江苏南钢板材销售有限公司'),
('L2_009', '金石高新材料项目部', 2, 'L1_001', '板材事业部 -> 金石高新材料项目部', '项目部', 'XA2000000', '金石高新材料项目部', 'X50000000', '板材事业部金石高新材料项目部'),

-- 特钢事业部下属
('L2_010', '第二炼钢厂', 2, 'L1_002', '特钢事业部 -> 第二炼钢厂', '厂', 'X63000000', '第二炼钢厂', 'X49000000', '特钢事业部第二炼钢厂'),
('L2_011', '棒材厂', 2, 'L1_002', '特钢事业部 -> 棒材厂', '厂', 'X65000000', '棒材厂', 'X49000000', '特钢事业部棒材厂'),
('L2_012', '精整厂', 2, 'L1_002', '特钢事业部 -> 精整厂', '厂', 'X41000000', '精整厂', 'X49000000', '特钢事业部精整厂'),
('L2_013', '大棒厂', 2, 'L1_002', '特钢事业部 -> 大棒厂', '厂', 'X59000000', '大棒厂', 'X49000000', '特钢事业部大棒厂'),
('L2_014', '高线厂', 2, 'L1_002', '特钢事业部 -> 高线厂', '厂', 'X46000000', '高线厂', 'X49000000', '特钢事业部高线厂'),
('L2_015', '中棒厂', 2, 'L1_002', '特钢事业部 -> 中棒厂', '厂', 'X60000000', '中棒厂', 'X49000000', '特钢事业部中棒厂'),
('L2_016', '特带厂', 2, 'L1_002', '特钢事业部 -> 特带厂', '厂', 'X98000000', '特带厂', 'X49000000', '特钢事业部特带厂'),
('L2_017', '技术研发处', 2, 'L1_002', '特钢事业部 -> 技术研发处', '处', 'X49060000', '技术研发处', 'X49000000', '特钢事业部技术研发处'),
('L2_018', '综合处', 2, 'L1_002', '特钢事业部 -> 综合处', '处', 'X49010000', '综合处', 'X49000000', '特钢事业部综合处'),
('L2_019', '质量处', 2, 'L1_002', '特钢事业部 -> 质量处', '处', 'X49030000', '质量处', 'X49000000', '特钢事业部质量处'),
('L2_020', '生产处', 2, 'L1_002', '特钢事业部 -> 生产处', '处', 'X49050000', '生产处', 'X49000000', '特钢事业部生产处'),
('L2_021', '营销处', 2, 'L1_002', '特钢事业部 -> 营销处', '处', 'X49070000', '营销处', 'X49000000', '特钢事业部营销处'),
('L2_022', '安全环保处', 2, 'L1_002', '特钢事业部 -> 安全环保处', '处', 'X49080000', '安全环保处', 'X49000000', '特钢事业部安全环保处'),
('L2_023', '南京南钢特钢长材有限公司', 2, 'L1_002', '特钢事业部 -> 南京南钢特钢长材有限公司', '公司', 'X81000000', '南京南钢特钢长材有限公司', 'X49000000', '特钢事业部南京南钢特钢长材有限公司'),

-- 炼铁事业部下属
('L2_024', '第一炼铁厂', 2, 'L1_003', '炼铁事业部 -> 第一炼铁厂', '厂', 'X31000000', '第一炼铁厂', 'X47000000', '炼铁事业部第一炼铁厂'),
('L2_025', '第二炼铁厂', 2, 'L1_003', '炼铁事业部 -> 第二炼铁厂', '厂', 'X62000000', '第二炼铁厂', 'X47000000', '炼铁事业部第二炼铁厂'),
('L2_026', '原料厂', 2, 'L1_003', '炼铁事业部 -> 原料厂', '厂', 'X43000000', '原料厂', 'X47000000', '炼铁事业部原料厂'),
('L2_027', '球团厂', 2, 'L1_003', '炼铁事业部 -> 球团厂', '厂', 'X44000000', '球团厂', 'X47000000', '炼铁事业部球团厂'),
('L2_028', '燃料供应厂', 2, 'L1_003', '炼铁事业部 -> 燃料供应厂', '厂', 'X70000000', '燃料供应厂', 'X47000000', '炼铁事业部燃料供应厂'),
('L2_029', '烧结厂', 2, 'L1_003', '炼铁事业部 -> 烧结厂', '厂', 'X86000000', '烧结厂', 'X47000000', '炼铁事业部烧结厂'),
('L2_030', '技术处', 2, 'L1_003', '炼铁事业部 -> 技术处', '处', 'X47080000', '技术处', 'X47000000', '炼铁事业部技术处'),
('L2_031', '设备处', 2, 'L1_003', '炼铁事业部 -> 设备处', '处', 'X47040000', '设备处', 'X47000000', '炼铁事业部设备处'),

-- 能源动力事业部下属
('L2_032', '制氧厂', 2, 'L1_004', '能源动力事业部 -> 制氧厂', '厂', 'X34000000', '制氧厂', 'X48000000', '能源动力事业部制氧厂'),
('L2_033', '水厂', 2, 'L1_004', '能源动力事业部 -> 水厂', '厂', 'X74000000', '水厂', 'X48000000', '能源动力事业部水厂'),
('L2_034', '燃气厂', 2, 'L1_004', '能源动力事业部 -> 燃气厂', '厂', 'X76000000', '燃气厂', 'X48000000', '能源动力事业部燃气厂'),
('L2_035', '发电厂', 2, 'L1_004', '能源动力事业部 -> 发电厂', '厂', 'X85000000', '发电厂', 'X48000000', '能源动力事业部发电厂'),
('L2_036', '设备处', 2, 'L1_004', '能源动力事业部 -> 设备处', '处', 'X48040000', '设备处', 'X48000000', '能源动力事业部设备处'),
('L2_037', '安全环保处', 2, 'L1_004', '能源动力事业部 -> 安全环保处', '处', 'X48010000', '安全环保处', 'X48000000', '能源动力事业部安全环保处'),
('L2_038', '生产质量处', 2, 'L1_004', '能源动力事业部 -> 生产质量处', '处', 'X48020000', '生产质量处', 'X48000000', '能源动力事业部生产质量处'),
('L2_039', '能源管理处', 2, 'L1_004', '能源动力事业部 -> 能源管理处', '处', 'X48070000', '能源管理处', 'X48000000', '能源动力事业部能源管理处'),
('L2_040', '江苏金灿能源科技有限公司', 2, 'L1_004', '能源动力事业部 -> 江苏金灿能源科技有限公司', '公司', 'X48080000', '江苏金灿能源科技有限公司', 'X48000000', '能源动力事业部江苏金灿能源科技有限公司');

-- =====================================================
-- 第三步：创建数据迁移和映射脚本
-- =====================================================

-- 创建完整的原始数据映射表
DROP TABLE IF EXISTS complete_dept_mapping;
CREATE TABLE complete_dept_mapping (
    id INT AUTO_INCREMENT PRIMARY KEY,
    original_org_code VARCHAR(20) NOT NULL,
    original_org_name VARCHAR(200),
    original_parent_code VARCHAR(20),
    original_full_name VARCHAR(500),
    new_dept_code VARCHAR(20),
    mapping_level INT,
    mapping_status VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_original_org_code (original_org_code),
    INDEX idx_new_dept_code (new_dept_code)
);

-- =====================================================
-- 第四步：统计信息和验证
-- =====================================================

SELECT 
    '=== 重构完成统计 ===' as section,
    dept_level as level,
    dept_type,
    COUNT(*) as count
FROM department_hierarchy_complete 
GROUP BY dept_level, dept_type 
ORDER BY dept_level, dept_type;

SELECT 
    '=== 总体统计 ===' as section,
    COUNT(*) as total_reconstructed_depts,
    '1399' as original_total,
    ROUND(COUNT(*) * 100.0 / 1399, 2) as coverage_percentage
FROM department_hierarchy_complete;

-- =====================================================
-- 第五步：插入三级部门（车间/室/科级别）
-- =====================================================

INSERT INTO department_hierarchy_complete (new_dept_code, dept_name, dept_level, parent_code, full_path, dept_type, original_org_code, original_org_name, original_parent_code, original_full_name) VALUES

-- 第一炼钢厂下属车间
('L3_001', '炼钢车间', 3, 'L2_003', '板材事业部 -> 第一炼钢厂 -> 炼钢车间', '车间', 'X73020000', '炼钢车间', 'X73000000', '板材事业部第一炼钢厂炼钢车间'),
('L3_002', '精炼车间', 3, 'L2_003', '板材事业部 -> 第一炼钢厂 -> 精炼车间', '车间', 'X73030000', '精炼车间', 'X73000000', '板材事业部第一炼钢厂精炼车间'),
('L3_003', '连铸车间', 3, 'L2_003', '板材事业部 -> 第一炼钢厂 -> 连铸车间', '车间', 'X73040000', '连铸车间', 'X73000000', '板材事业部第一炼钢厂连铸车间'),
('L3_004', '石灰车间', 3, 'L2_003', '板材事业部 -> 第一炼钢厂 -> 石灰车间', '车间', 'X73070000', '石灰车间', 'X73000000', '板材事业部第一炼钢厂石灰车间'),
('L3_005', '坯料车间', 3, 'L2_003', '板材事业部 -> 第一炼钢厂 -> 坯料车间', '车间', 'X73140000', '坯料车间', 'X73000000', '板材事业部第一炼钢厂坯料车间'),
('L3_006', '综合科', 3, 'L2_003', '板材事业部 -> 第一炼钢厂 -> 综合科', '科', 'X73010000', '综合科', 'X73000000', '板材事业部第一炼钢厂综合科'),

-- 第二炼钢厂下属车间
('L3_007', '电炉炼钢车间', 3, 'L2_010', '特钢事业部 -> 第二炼钢厂 -> 电炉炼钢车间', '车间', 'X63000600', '电炉炼钢车间', 'X63000000', '特钢事业部第二炼钢厂电炉炼钢车间'),
('L3_008', '电炉精炼车间', 3, 'L2_010', '特钢事业部 -> 第二炼钢厂 -> 电炉精炼车间', '车间', 'X63000200', '电炉精炼车间', 'X63000000', '特钢事业部第二炼钢厂电炉精炼车间'),
('L3_009', '电炉连铸车间', 3, 'L2_010', '特钢事业部 -> 第二炼钢厂 -> 电炉连铸车间', '车间', 'X63000500', '电炉连铸车间', 'X63000000', '特钢事业部第二炼钢厂电炉连铸车间'),
('L3_010', '电炉运行车间', 3, 'L2_010', '特钢事业部 -> 第二炼钢厂 -> 电炉运行车间', '车间', 'X63000400', '电炉运行车间', 'X63000000', '特钢事业部第二炼钢厂电炉运行车间'),
('L3_011', '电炉检修车间', 3, 'L2_010', '特钢事业部 -> 第二炼钢厂 -> 电炉检修车间', '车间', 'X63000300', '电炉检修车间', 'X63000000', '特钢事业部第二炼钢厂电炉检修车间'),
('L3_012', '连铸管理室', 3, 'L2_010', '特钢事业部 -> 第二炼钢厂 -> 连铸管理室', '室', 'X63000100', '连铸管理室', 'X63000000', '特钢事业部第二炼钢厂连铸管理室'),

-- 宽厚板厂下属车间
('L3_013', '板加车间', 3, 'L2_002', '板材事业部 -> 宽厚板厂 -> 板加车间', '车间', 'X38060000', '板加车间', 'X38000000', '板材事业部宽厚板厂板加车间'),
('L3_014', '安全环保科', 3, 'L2_002', '板材事业部 -> 宽厚板厂 -> 安全环保科', '科', 'X38050000', '安全环保科', 'X38000000', '板材事业部宽厚板厂安全环保科'),

-- 中厚板卷厂下属
('L3_015', '配送车间', 3, 'L2_001', '板材事业部 -> 中厚板卷厂 -> 配送车间', '车间', 'X32110000', '配送车间', 'X32000000', '板材事业部中厚板卷厂配送车间'),
('L3_016', '综合科', 3, 'L2_001', '板材事业部 -> 中厚板卷厂 -> 综合科', '科', 'X32010000', '综合科', 'X32000000', '板材事业部中厚板卷厂综合科'),
('L3_017', '安全环保科', 3, 'L2_001', '板材事业部 -> 中厚板卷厂 -> 安全环保科', '科', 'X32160000', '安全环保科', 'X32000000', '板材事业部中厚板卷厂安全环保科'),

-- 中板厂下属
('L3_018', '综合科', 3, 'L2_004', '板材事业部 -> 中板厂 -> 综合科', '科', 'X66010000', '综合科', 'X66000000', '板材事业部中板厂综合科'),
('L3_019', '安全科', 3, 'L2_004', '板材事业部 -> 中板厂 -> 安全科', '科', 'X66120000', '安全科', 'X66000000', '板材事业部中板厂安全科'),
('L3_020', '产品管理室', 3, 'L2_004', '板材事业部 -> 中板厂 -> 产品管理室', '室', 'X66130000', '产品管理室', 'X66000000', '板材事业部中板厂产品管理室'),

-- 金石材料厂下属
('L3_021', '渣处理车间', 3, 'L2_005', '板材事业部 -> 金石材料厂 -> 渣处理车间', '车间', 'X84020000', '渣处理车间', 'X84000000', '板材事业部金石材料厂渣处理车间'),
('L3_022', '石灰车间', 3, 'L2_005', '板材事业部 -> 金石材料厂 -> 石灰车间', '车间', 'X84030000', '石灰车间', 'X84000000', '板材事业部金石材料厂石灰车间'),

-- 物流中心下属
('L3_023', '铁路运输中心', 3, 'L1_005', '物流中心 -> 铁路运输中心', '中心', 'X35000000', '铁路运输中心', 'X52000000', '物流中心铁路运输中心'),
('L3_024', '产成品储运室', 3, 'L1_005', '物流中心 -> 产成品储运室', '室', 'X52050000', '产成品储运室', 'X52000000', '物流中心产成品储运室'),
('L3_025', '材料仓储室', 3, 'L1_005', '物流中心 -> 材料仓储室', '室', 'X52060000', '材料仓储室', 'X52000000', '物流中心材料仓储室'),
('L3_026', '安全环保室', 3, 'L1_005', '物流中心 -> 安全环保室', '室', 'X52070000', '安全环保室', 'X52000000', '物流中心安全环保室'),

-- 采购中心下属
('L3_027', '江苏南钢环宇贸易有限公司', 3, 'L1_006', '采购中心 -> 江苏南钢环宇贸易有限公司', '公司', 'X78000000', '江苏南钢环宇贸易有限公司', 'X53000000', '采购中心江苏南钢环宇贸易有限公司'),
('L3_028', '材料室', 3, 'L1_006', '采购中心 -> 材料室', '室', 'X53030000', '材料室', 'X53000000', '采购中心材料室'),
('L3_029', '设备备件室', 3, 'L1_006', '采购中心 -> 设备备件室', '室', 'X53040000', '设备备件室', 'X53000000', '采购中心设备备件室'),

-- 特带厂下属
('L3_030', '乙作业区', 3, 'L2_016', '特钢事业部 -> 特带厂 -> 乙作业区', '作业区', 'X98000200', '乙作业区', 'X98000000', '特钢事业部特带厂乙作业区'),
('L3_031', '丙作业区', 3, 'L2_016', '特钢事业部 -> 特带厂 -> 丙作业区', '作业区', 'X98000300', '丙作业区', 'X98000000', '特钢事业部特带厂丙作业区'),
('L3_032', '丁作业区', 3, 'L2_016', '特钢事业部 -> 特带厂 -> 丁作业区', '作业区', 'X98000400', '丁作业区', 'X98000000', '特钢事业部特带厂丁作业区'),
('L3_033', '生产计划室', 3, 'L2_016', '特钢事业部 -> 特带厂 -> 生产计划室', '室', 'X98030000', '生产计划室', 'X98000000', '特钢事业部特带厂生产计划室'),
('L3_034', '电修车间', 3, 'L2_016', '特钢事业部 -> 特带厂 -> 电修车间', '车间', 'X98080000', '电修车间', 'X98000000', '特钢事业部特带厂电修车间'),
('L3_035', '准备车间', 3, 'L2_016', '特钢事业部 -> 特带厂 -> 准备车间', '车间', 'X98050000', '准备车间', 'X98000000', '特钢事业部特带厂准备车间'),

-- 燃气厂下属
('L3_036', '燃气车间', 3, 'L2_034', '能源动力事业部 -> 燃气厂 -> 燃气车间', '车间', 'X76020000', '燃气车间', 'X76000000', '能源动力事业部燃气厂燃气车间'),
('L3_037', '检修车间', 3, 'L2_034', '能源动力事业部 -> 燃气厂 -> 检修车间', '车间', 'X76030000', '检修车间', 'X76000000', '能源动力事业部燃气厂检修车间'),

-- 发电厂下属
('L3_038', '综合管理室', 3, 'L2_035', '能源动力事业部 -> 发电厂 -> 综合管理室', '室', 'X85010000', '综合管理室', 'X85000000', '能源动力事业部发电厂综合管理室'),
('L3_039', '检修车间', 3, 'L2_035', '能源动力事业部 -> 发电厂 -> 检修车间', '车间', 'X85020000', '检修车间', 'X85000000', '能源动力事业部发电厂检修车间'),
('L3_040', '运行车间', 3, 'L2_035', '能源动力事业部 -> 发电厂 -> 运行车间', '车间', 'X85030000', '运行车间', 'X85000000', '能源动力事业部发电厂运行车间');

-- =====================================================
-- 第六步：插入四级部门（班组级别）
-- =====================================================

INSERT INTO department_hierarchy_complete (new_dept_code, dept_name, dept_level, parent_code, full_path, dept_type, original_org_code, original_org_name, original_parent_code, original_full_name) VALUES

-- 板加车间下属班组
('L4_001', '加热炉甲班', 4, 'L3_013', '板材事业部 -> 宽厚板厂 -> 板加车间 -> 加热炉甲班', '班', 'X38060001', '加热炉甲班', 'X38060000', '板材事业部宽厚板厂板加车间加热炉甲班'),
('L4_002', '加热炉乙班', 4, 'L3_013', '板材事业部 -> 宽厚板厂 -> 板加车间 -> 加热炉乙班', '班', 'X38060002', '加热炉乙班', 'X38060000', '板材事业部宽厚板厂板加车间加热炉乙班'),
('L4_003', '加热炉丙班', 4, 'L3_013', '板材事业部 -> 宽厚板厂 -> 板加车间 -> 加热炉丙班', '班', 'X38060003', '加热炉丙班', 'X38060000', '板材事业部宽厚板厂板加车间加热炉丙班'),
('L4_004', '加热炉丁班', 4, 'L3_013', '板材事业部 -> 宽厚板厂 -> 板加车间 -> 加热炉丁班', '班', 'X38060004', '加热炉丁班', 'X38060000', '板材事业部宽厚板厂板加车间加热炉丁班'),

-- 电炉炼钢车间下属班组
('L4_005', '电炉甲班', 4, 'L3_007', '特钢事业部 -> 第二炼钢厂 -> 电炉炼钢车间 -> 电炉甲班', '班', 'X63000601', '电炉甲班', 'X63000600', '特钢事业部第二炼钢厂电炉炼钢车间电炉甲班'),
('L4_006', '电炉乙班', 4, 'L3_007', '特钢事业部 -> 第二炼钢厂 -> 电炉炼钢车间 -> 电炉乙班', '班', 'X63000602', '电炉乙班', 'X63000600', '特钢事业部第二炼钢厂电炉炼钢车间电炉乙班'),
('L4_007', '电炉丙班', 4, 'L3_007', '特钢事业部 -> 第二炼钢厂 -> 电炉炼钢车间 -> 电炉丙班', '班', 'X63000603', '电炉丙班', 'X63000600', '特钢事业部第二炼钢厂电炉炼钢车间电炉丙班'),
('L4_008', '电炉丁班', 4, 'L3_007', '特钢事业部 -> 第二炼钢厂 -> 电炉炼钢车间 -> 电炉丁班', '班', 'X63000604', '电炉丁班', 'X63000600', '特钢事业部第二炼钢厂电炉炼钢车间电炉丁班'),
('L4_009', '辅助班', 4, 'L3_007', '特钢事业部 -> 第二炼钢厂 -> 电炉炼钢车间 -> 辅助班', '班', 'X63000605', '辅助班', 'X63000600', '特钢事业部第二炼钢厂电炉炼钢车间辅助班'),

-- 电炉精炼车间下属班组
('L4_010', '钢包班', 4, 'L3_008', '特钢事业部 -> 第二炼钢厂 -> 电炉精炼车间 -> 钢包班', '班', 'X63000201', '钢包班', 'X63000200', '特钢事业部第二炼钢厂电炉精炼车间钢包班'),
('L4_011', '精炼炉甲班', 4, 'L3_008', '特钢事业部 -> 第二炼钢厂 -> 电炉精炼车间 -> 精炼炉甲班', '班', 'X63000205', '精炼炉甲班', 'X63000200', '特钢事业部第二炼钢厂电炉精炼车间精炼炉甲班'),
('L4_012', '精炼炉乙班', 4, 'L3_008', '特钢事业部 -> 第二炼钢厂 -> 电炉精炼车间 -> 精炼炉乙班', '班', 'X63000204', '精炼炉乙班', 'X63000200', '特钢事业部第二炼钢厂电炉精炼车间精炼炉乙班'),
('L4_013', '精炼炉丙班', 4, 'L3_008', '特钢事业部 -> 第二炼钢厂 -> 电炉精炼车间 -> 精炼炉丙班', '班', 'X63000203', '精炼炉丙班', 'X63000200', '特钢事业部第二炼钢厂电炉精炼车间精炼炉丙班'),
('L4_014', '精炼炉丁班', 4, 'L3_008', '特钢事业部 -> 第二炼钢厂 -> 电炉精炼车间 -> 精炼炉丁班', '班', 'X63000202', '精炼炉丁班', 'X63000200', '特钢事业部第二炼钢厂电炉精炼车间精炼炉丁班'),

-- 电炉运行车间下属班组
('L4_015', '配料丙班', 4, 'L3_010', '特钢事业部 -> 第二炼钢厂 -> 电炉运行车间 -> 配料丙班', '班', 'X63000402', '配料丙班', 'X63000400', '特钢事业部第二炼钢厂电炉运行车间配料丙班'),
('L4_016', '行车甲班', 4, 'L3_010', '特钢事业部 -> 第二炼钢厂 -> 电炉运行车间 -> 行车甲班', '班', 'X63000408', '行车甲班', 'X63000400', '特钢事业部第二炼钢厂电炉运行车间行车甲班'),
('L4_017', '行车丙班', 4, 'L3_010', '特钢事业部 -> 第二炼钢厂 -> 电炉运行车间 -> 行车丙班', '班', 'X63000406', '行车丙班', 'X63000400', '特钢事业部第二炼钢厂电炉运行车间行车丙班'),

-- 电炉检修车间下属班组
('L4_018', '钳工班', 4, 'L3_011', '特钢事业部 -> 第二炼钢厂 -> 电炉检修车间 -> 钳工班', '班', 'X63000301', '钳工班', 'X63000300', '特钢事业部第二炼钢厂电炉检修车间钳工班'),
('L4_019', '电工班', 4, 'L3_011', '特钢事业部 -> 第二炼钢厂 -> 电炉检修车间 -> 电工班', '班', 'X63000302', '电工班', 'X63000300', '特钢事业部第二炼钢厂电炉检修车间电工班'),

-- 产品管理室下属班组
('L4_020', '检验班', 4, 'L3_020', '板材事业部 -> 中板厂 -> 产品管理室 -> 检验班', '班', 'X66130001', '检验班', 'X66130000', '板材事业部中板厂产品管理室检验班'),
('L4_021', '探伤班', 4, 'L3_020', '板材事业部 -> 中板厂 -> 产品管理室 -> 探伤班', '班', 'X66130002', '探伤班', 'X66130000', '板材事业部中板厂产品管理室探伤班');

-- =====================================================
-- 第七步：更新统计信息
-- =====================================================

SELECT
    '=== 完整重构统计 ===' as section,
    dept_level as level,
    dept_type,
    COUNT(*) as count
FROM department_hierarchy_complete
GROUP BY dept_level, dept_type
ORDER BY dept_level, dept_type;

SELECT
    '=== 覆盖率统计 ===' as section,
    COUNT(*) as total_reconstructed_depts,
    '1399' as original_total,
    ROUND(COUNT(*) * 100.0 / 1399, 2) as coverage_percentage
FROM department_hierarchy_complete;
