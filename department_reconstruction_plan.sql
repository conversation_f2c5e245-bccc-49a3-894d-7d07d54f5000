-- =====================================================
-- 基于full_name生成标准化部门重构方案
-- =====================================================

-- 第一步：生成标准化的一级部门列表
SELECT 
    '=== 标准化一级部门 ===' as section,
    ROW_NUMBER() OVER (ORDER BY level1_name) as new_dept_id,
    level1_name as standard_name,
    COUNT(*) as total_sub_depts,
    CASE 
        WHEN level1_name LIKE '%事业部%' THEN '事业部'
        WHEN level1_name LIKE '%集团%' THEN '集团'
        WHEN level1_name LIKE '%有限公司%' THEN '公司'
        WHEN level1_name LIKE '%中心%' THEN '中心'
        WHEN level1_name LIKE '%部' THEN '部门'
        ELSE '其他'
    END as dept_type
FROM (
    SELECT 
        CASE 
            WHEN full_name LIKE '%事业部%' THEN 
                CONCAT(TRIM(SUBSTRING_INDEX(full_name, '事业部', 1)), '事业部')
            WHEN full_name LIKE '%集团%' AND full_name NOT LIKE '%事业部%' THEN 
                CONCAT(TRIM(SUBSTRING_INDEX(full_name, '集团', 1)), '集团')
            WHEN full_name LIKE '%有限公司%' AND full_name NOT LIKE '%事业部%' AND full_name NOT LIKE '%集团%' THEN 
                CONCAT(TRIM(SUBSTRING_INDEX(full_name, '有限公司', 1)), '有限公司')
            WHEN full_name LIKE '%中心%' AND full_name NOT LIKE '%事业部%' AND full_name NOT LIKE '%集团%' AND full_name NOT LIKE '%有限公司%' THEN 
                CONCAT(TRIM(SUBSTRING_INDEX(full_name, '中心', 1)), '中心')
            WHEN full_name LIKE '%部' AND full_name NOT LIKE '%事业部%' THEN 
                full_name
            ELSE 
                TRIM(SUBSTRING_INDEX(full_name, '（', 1))
        END as level1_name
    FROM department_sync_test 
    WHERE is_history = 0 AND user_predef_14 != 'D'
    AND full_name IS NOT NULL AND full_name != ''
) t
GROUP BY level1_name
ORDER BY total_sub_depts DESC;

-- 第二步：生成标准化的二级部门列表
SELECT 
    '=== 标准化二级部门 ===' as section,
    level1_name as parent_dept,
    level2_name as standard_name,
    COUNT(*) as total_sub_depts,
    CASE 
        WHEN level2_name LIKE '%厂%' THEN '厂'
        WHEN level2_name LIKE '%处%' THEN '处'
        WHEN level2_name LIKE '%部%' THEN '部'
        WHEN level2_name LIKE '%室%' THEN '室'
        ELSE '其他'
    END as dept_type
FROM (
    SELECT 
        CASE 
            WHEN full_name LIKE '%事业部%' THEN 
                CONCAT(TRIM(SUBSTRING_INDEX(full_name, '事业部', 1)), '事业部')
            WHEN full_name LIKE '%集团%' AND full_name NOT LIKE '%事业部%' THEN 
                CONCAT(TRIM(SUBSTRING_INDEX(full_name, '集团', 1)), '集团')
            WHEN full_name LIKE '%有限公司%' AND full_name NOT LIKE '%事业部%' AND full_name NOT LIKE '%集团%' THEN 
                CONCAT(TRIM(SUBSTRING_INDEX(full_name, '有限公司', 1)), '有限公司')
            ELSE 
                TRIM(SUBSTRING_INDEX(full_name, '（', 1))
        END as level1_name,
        
        CASE 
            WHEN full_name LIKE '%事业部%厂%' THEN 
                CONCAT(TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '厂', 1), '事业部', -1)), '厂')
            WHEN full_name LIKE '%事业部%处%' THEN 
                CONCAT(TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '处', 1), '事业部', -1)), '处')
            WHEN full_name LIKE '%集团%部%' THEN 
                CONCAT(TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '部', 1), '集团', -1)), '部')
            WHEN full_name LIKE '%有限公司%部%' THEN 
                CONCAT(TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '部', 1), '有限公司', -1)), '部')
            WHEN full_name LIKE '%中心%厂%' THEN 
                CONCAT(TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '厂', 1), '中心', -1)), '厂')
            ELSE ''
        END as level2_name
    FROM department_sync_test 
    WHERE is_history = 0 AND user_predef_14 != 'D'
    AND full_name IS NOT NULL AND full_name != ''
) t
WHERE level2_name != '' AND level2_name IS NOT NULL
GROUP BY level1_name, level2_name
ORDER BY level1_name, total_sub_depts DESC;

-- 第三步：生成标准化的三级部门列表
SELECT 
    '=== 标准化三级部门 ===' as section,
    level1_name as top_parent,
    level2_name as parent_dept,
    level3_name as standard_name,
    COUNT(*) as total_sub_depts,
    CASE 
        WHEN level3_name LIKE '%车间%' THEN '车间'
        WHEN level3_name LIKE '%室%' THEN '室'
        WHEN level3_name LIKE '%科%' THEN '科'
        WHEN level3_name LIKE '%站%' THEN '站'
        ELSE '其他'
    END as dept_type
FROM (
    SELECT 
        CASE 
            WHEN full_name LIKE '%事业部%' THEN 
                CONCAT(TRIM(SUBSTRING_INDEX(full_name, '事业部', 1)), '事业部')
            WHEN full_name LIKE '%集团%' AND full_name NOT LIKE '%事业部%' THEN 
                CONCAT(TRIM(SUBSTRING_INDEX(full_name, '集团', 1)), '集团')
            ELSE 
                TRIM(SUBSTRING_INDEX(full_name, '（', 1))
        END as level1_name,
        
        CASE 
            WHEN full_name LIKE '%事业部%厂%' THEN 
                CONCAT(TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '厂', 1), '事业部', -1)), '厂')
            WHEN full_name LIKE '%事业部%处%' THEN 
                CONCAT(TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '处', 1), '事业部', -1)), '处')
            ELSE ''
        END as level2_name,
        
        CASE 
            WHEN full_name LIKE '%厂%车间%' THEN 
                CONCAT(TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '车间', 1), '厂', -1)), '车间')
            WHEN full_name LIKE '%厂%室%' THEN 
                CONCAT(TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '室', 1), '厂', -1)), '室')
            WHEN full_name LIKE '%处%室%' THEN 
                CONCAT(TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '室', 1), '处', -1)), '室')
            WHEN full_name LIKE '%厂%科%' THEN 
                CONCAT(TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '科', 1), '厂', -1)), '科')
            ELSE ''
        END as level3_name
    FROM department_sync_test 
    WHERE is_history = 0 AND user_predef_14 != 'D'
    AND full_name IS NOT NULL AND full_name != ''
) t
WHERE level3_name != '' AND level3_name IS NOT NULL
GROUP BY level1_name, level2_name, level3_name
ORDER BY level1_name, level2_name, total_sub_depts DESC;

-- 第四步：生成标准化的四级部门列表（班组级别）
SELECT 
    '=== 标准化四级部门（班组） ===' as section,
    level1_name as top_parent,
    level2_name as level2_parent,
    level3_name as parent_dept,
    level4_name as standard_name,
    COUNT(*) as total_count
FROM (
    SELECT 
        CASE 
            WHEN full_name LIKE '%事业部%' THEN 
                CONCAT(TRIM(SUBSTRING_INDEX(full_name, '事业部', 1)), '事业部')
            ELSE 
                TRIM(SUBSTRING_INDEX(full_name, '（', 1))
        END as level1_name,
        
        CASE 
            WHEN full_name LIKE '%事业部%厂%' THEN 
                CONCAT(TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '厂', 1), '事业部', -1)), '厂')
            ELSE ''
        END as level2_name,
        
        CASE 
            WHEN full_name LIKE '%厂%车间%' THEN 
                CONCAT(TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '车间', 1), '厂', -1)), '车间')
            WHEN full_name LIKE '%厂%室%' THEN 
                CONCAT(TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '室', 1), '厂', -1)), '室')
            WHEN full_name LIKE '%厂%科%' THEN 
                CONCAT(TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '科', 1), '厂', -1)), '科')
            ELSE ''
        END as level3_name,
        
        CASE 
            WHEN full_name LIKE '%车间%班%' THEN 
                CONCAT(TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '班', 1), '车间', -1)), '班')
            WHEN full_name LIKE '%室%班%' THEN 
                CONCAT(TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '班', 1), '室', -1)), '班')
            WHEN full_name LIKE '%科%班%' THEN 
                CONCAT(TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(full_name, '班', 1), '科', -1)), '班')
            ELSE ''
        END as level4_name
    FROM department_sync_test 
    WHERE is_history = 0 AND user_predef_14 != 'D'
    AND full_name IS NOT NULL AND full_name != ''
) t
WHERE level4_name != '' AND level4_name IS NOT NULL
GROUP BY level1_name, level2_name, level3_name, level4_name
ORDER BY level1_name, level2_name, level3_name, total_count DESC;

-- 第五步：生成重构后的部门编码建议
SELECT 
    '=== 重构后的部门编码建议 ===' as section,
    CONCAT('L1_', LPAD(ROW_NUMBER() OVER (ORDER BY level1_name), 3, '0')) as new_code,
    level1_name as dept_name,
    '1级部门' as level_info,
    COUNT(*) as sub_count
FROM (
    SELECT DISTINCT
        CASE 
            WHEN full_name LIKE '%事业部%' THEN 
                CONCAT(TRIM(SUBSTRING_INDEX(full_name, '事业部', 1)), '事业部')
            WHEN full_name LIKE '%集团%' AND full_name NOT LIKE '%事业部%' THEN 
                CONCAT(TRIM(SUBSTRING_INDEX(full_name, '集团', 1)), '集团')
            WHEN full_name LIKE '%有限公司%' AND full_name NOT LIKE '%事业部%' AND full_name NOT LIKE '%集团%' THEN 
                CONCAT(TRIM(SUBSTRING_INDEX(full_name, '有限公司', 1)), '有限公司')
            WHEN full_name LIKE '%中心%' AND full_name NOT LIKE '%事业部%' AND full_name NOT LIKE '%集团%' AND full_name NOT LIKE '%有限公司%' THEN 
                CONCAT(TRIM(SUBSTRING_INDEX(full_name, '中心', 1)), '中心')
            WHEN full_name LIKE '%部' AND full_name NOT LIKE '%事业部%' THEN 
                full_name
            ELSE 
                TRIM(SUBSTRING_INDEX(full_name, '（', 1))
        END as level1_name
    FROM department_sync_test 
    WHERE is_history = 0 AND user_predef_14 != 'D'
    AND full_name IS NOT NULL AND full_name != ''
) t1
JOIN (
    SELECT 
        CASE 
            WHEN full_name LIKE '%事业部%' THEN 
                CONCAT(TRIM(SUBSTRING_INDEX(full_name, '事业部', 1)), '事业部')
            WHEN full_name LIKE '%集团%' AND full_name NOT LIKE '%事业部%' THEN 
                CONCAT(TRIM(SUBSTRING_INDEX(full_name, '集团', 1)), '集团')
            WHEN full_name LIKE '%有限公司%' AND full_name NOT LIKE '%事业部%' AND full_name NOT LIKE '%集团%' THEN 
                CONCAT(TRIM(SUBSTRING_INDEX(full_name, '有限公司', 1)), '有限公司')
            WHEN full_name LIKE '%中心%' AND full_name NOT LIKE '%事业部%' AND full_name NOT LIKE '%集团%' AND full_name NOT LIKE '%有限公司%' THEN 
                CONCAT(TRIM(SUBSTRING_INDEX(full_name, '中心', 1)), '中心')
            WHEN full_name LIKE '%部' AND full_name NOT LIKE '%事业部%' THEN 
                full_name
            ELSE 
                TRIM(SUBSTRING_INDEX(full_name, '（', 1))
        END as level1_name
    FROM department_sync_test 
    WHERE is_history = 0 AND user_predef_14 != 'D'
    AND full_name IS NOT NULL AND full_name != ''
) t2 ON t1.level1_name = t2.level1_name
GROUP BY level1_name
ORDER BY sub_count DESC;
