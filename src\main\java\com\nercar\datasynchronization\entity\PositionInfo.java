package com.nercar.datasynchronization.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.util.Date;

/**
 * 岗位信息实体 - 存储岗位基本信息
 */
@Data
@Entity
@Table(name = "position_info")
@DynamicInsert
@DynamicUpdate
public class PositionInfo {
    
    @Id
    private String id;
    
    @Column(name = "position_code", nullable = false, unique = true)
    private String positionCode;
    
    @Column(name = "position_name", nullable = false, unique = true)
    private String positionName;
    
    @Column(name = "post_no")
    private String postNo;
    
    @Column(name = "skill_level_code")
    private String skillLevelCode;
    
    @Column(name = "required_education")
    private String requiredEducation;
    
    @Column(name = "required_experience")
    private Integer requiredExperience;
    
    @Column(name = "position_description")
    private String positionDescription;
    
    @Column(name = "status")
    private String status;
    
    @Column(name = "created_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdTime;
    
    @Column(name = "updated_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedTime;
}