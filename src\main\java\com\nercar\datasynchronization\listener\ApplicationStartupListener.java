package com.nercar.datasynchronization.listener;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * 应用启动监听器
 * 在应用启动完成后输出应用相关信息
 */
@Slf4j
@Component
public class ApplicationStartupListener implements ApplicationListener<ApplicationStartedEvent> {

    @Value("${server.port:8080}")
    private int serverPort;

    @Value("${springdoc.swagger-ui.path:/swagger-ui.html}")
    private String swaggerPath;

    @Value("${sync.cron:0 0 3 * * ?}")
    private String syncCron;

    @Value("${sync.soap.org-url}")
    private String orgServiceUrl;

    @Value("${sync.soap.user-url}")
    private String userServiceUrl;

    @Value("${sync.http.position-url}")
    private String positionServiceUrl;

    @Override
    public void onApplicationEvent(ApplicationStartedEvent event) {
        String contextPath = "";
        String hostAddress = "localhost";

        try {
            hostAddress = InetAddress.getLocalHost().getHostAddress();
        } catch (UnknownHostException e) {
            log.warn("无法获取主机地址，将使用localhost作为默认值", e);
        }

        if (event.getApplicationContext() instanceof ServletWebServerApplicationContext) {
            ServletWebServerApplicationContext webContext = (ServletWebServerApplicationContext) event.getApplicationContext();
            contextPath = webContext.getServletContext().getContextPath();
        }

        // 打印应用访问地址
        log.info("=====================================================");
        log.info("数据同步应用启动成功！");
        log.info("应用访问地址: http://{}:{}{}", hostAddress, serverPort, contextPath);
        log.info("Swagger文档地址: http://{}:{}{}{}", hostAddress, serverPort, contextPath, swaggerPath);
        log.info("=====================================================");

        // 打印服务调用信息
        log.info("SOAP服务调用信息：");
        log.info("组织数据服务地址: {}", orgServiceUrl);
        log.info("接口方法: GetOrgInfoFromMDM");
        log.info("人员数据服务地址: {}", userServiceUrl);
        log.info("接口方法: GetUserInfoFromMDM");
        log.info("=====================================================");

        // 打印HTTP接口信息
        log.info("HTTP接口调用信息：");
        log.info("岗位数据服务地址: {}", positionServiceUrl);
        log.info("接口方法: getQualityPersonnelInfo");
        log.info("=====================================================");

        // 打印定时任务配置信息
        log.info("定时任务配置信息：");
        log.info("数据同步任务执行时间: {} (凌晨3点执行)", syncCron);
        log.info("数据同步范围: 昨天00:00:00至23:59:59的数据");
        log.info("=====================================================");
    }
}