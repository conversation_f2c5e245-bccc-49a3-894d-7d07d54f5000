package com.nercar.datasynchronization.dto;

import lombok.Data;
import java.util.Date;

/**
 * 员工岗位数据传输对象
 * 对应employee_position表结构
 */
@Data
public class EmployeePositionDTO {

    /**
     * 全局唯一标识符
     */
    private String guid;

    /**
     * 关联的员工MDM ID
     */
    private String employeeMdmId;

    /**
     * 岗位代码
     */
    private String positionCode;

    /**
     * 组织代码
     */
    private String orgCode;

    /**
     * 部门代码
     */
    private String departmentCode;

    /**
     * 是否主岗位：1=是，0=否
     */
    private String isPrimary;

    /**
     * 状态：D=在职，U=默认
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新时间
     */
    private Date updatedTime;

    /**
     * 是否激活
     */
    private String isActive;

    /**
     * 岗位详细代码
     */
    private String positionDetailCode;
}
