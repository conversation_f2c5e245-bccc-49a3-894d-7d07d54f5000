package com.nercar.datasynchronization.service;

import com.nercar.datasynchronization.entity.DepartmentSyncTest;
import com.nercar.datasynchronization.repository.DepartmentSyncTestRepository;
import com.nercar.datasynchronization.repository.DepartmentSyncOperationLogRepository;
import com.nercar.datasynchronization.service.impl.DepartmentSyncTestServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 部门同步测试服务单元测试
 */
@ExtendWith(MockitoExtension.class)
class DepartmentSyncTestServiceTest {

    @Mock
    private DepartmentSyncTestRepository departmentSyncTestRepository;

    @Mock
    private DepartmentSyncOperationLogRepository operationLogRepository;

    @Mock
    private com.nercar.datasynchronization.client.SoapClient soapClient;

    @InjectMocks
    private DepartmentSyncTestServiceImpl departmentSyncTestService;

    private DepartmentSyncTest testDepartment;

    @BeforeEach
    void setUp() {
        testDepartment = new DepartmentSyncTest();
        testDepartment.setId(1L);
        testDepartment.setOrgCode("TEST001");
        testDepartment.setOrgName("测试部门");
        testDepartment.setParentCode("ROOT");
        testDepartment.setIsHistory(0);
    }

    @Test
    void testHandleSpecialDepartments() {
        // 准备测试数据
        DepartmentSyncTest specialDept = new DepartmentSyncTest();
        specialDept.setOrgCode("X");
        specialDept.setOrgName("特殊根节点");
        
        when(departmentSyncTestRepository.findByOrgCodeIn(Arrays.asList("X")))
                .thenReturn(Arrays.asList(specialDept));

        // 执行测试
        List<String> deletedOrgCodes = departmentSyncTestService.handleSpecialDepartments();

        // 验证结果
        assertEquals(1, deletedOrgCodes.size());
        assertEquals("X", deletedOrgCodes.get(0));
        verify(departmentSyncTestRepository).delete(specialDept);
    }

    @Test
    void testRepairDepartmentRelations() {
        // 准备测试数据
        DepartmentSyncTest brokenDept = new DepartmentSyncTest();
        brokenDept.setOrgCode("BROKEN001");
        brokenDept.setParentCode("DELETED_PARENT");
        
        List<String> deletedOrgCodes = Arrays.asList("DELETED_PARENT");
        
        when(departmentSyncTestRepository.findDepartmentsWithBrokenRelations())
                .thenReturn(Arrays.asList(brokenDept));

        // 执行测试
        int repairedCount = departmentSyncTestService.repairDepartmentRelations(deletedOrgCodes);

        // 验证结果
        assertEquals(1, repairedCount);
        assertEquals("1", brokenDept.getParentCode());
        verify(departmentSyncTestRepository).save(brokenDept);
    }

    @Test
    void testGetDataStatistics() {
        // 准备测试数据
        when(departmentSyncTestRepository.count()).thenReturn(100L);
        when(departmentSyncTestRepository.countByIsHistory(0)).thenReturn(90L);
        when(departmentSyncTestRepository.countByIsHistory(1)).thenReturn(10L);
        when(departmentSyncTestRepository.countByUserPredef14("D")).thenReturn(5L);
        when(departmentSyncTestRepository.findRootDepartments()).thenReturn(Arrays.asList(testDepartment));
        when(departmentSyncTestRepository.findDepartmentsWithBrokenRelations()).thenReturn(Arrays.asList());

        // 执行测试
        Map<String, Object> stats = departmentSyncTestService.getDataStatistics();

        // 验证结果
        assertNotNull(stats);
        assertEquals(100L, stats.get("totalCount"));
        assertEquals(90L, stats.get("normalCount"));
        assertEquals(10L, stats.get("historyCount"));
        assertEquals(5L, stats.get("deletedCount"));
        assertEquals(1, stats.get("rootDepartmentCount"));
        assertEquals(0, stats.get("brokenRelationCount"));
    }

    @Test
    void testValidateDataIntegrity() {
        // 准备测试数据
        when(departmentSyncTestRepository.findDepartmentsWithBrokenRelations())
                .thenReturn(Arrays.asList());

        // 执行测试
        Map<String, Object> validation = departmentSyncTestService.validateDataIntegrity();

        // 验证结果
        assertNotNull(validation);
        assertTrue((Boolean) validation.get("isValid"));
        assertEquals(0, ((List<?>) validation.get("issues")).size());
        assertEquals(0, validation.get("brokenRelationCount"));
    }

    @Test
    void testValidateDataIntegrityWithIssues() {
        // 准备测试数据
        DepartmentSyncTest brokenDept = new DepartmentSyncTest();
        brokenDept.setOrgCode("BROKEN001");
        
        when(departmentSyncTestRepository.findDepartmentsWithBrokenRelations())
                .thenReturn(Arrays.asList(brokenDept));

        // 执行测试
        Map<String, Object> validation = departmentSyncTestService.validateDataIntegrity();

        // 验证结果
        assertNotNull(validation);
        assertFalse((Boolean) validation.get("isValid"));
        assertEquals(1, ((List<?>) validation.get("issues")).size());
        assertEquals(1, validation.get("brokenRelationCount"));
    }

    @Test
    void testClearTestData() {
        // 执行测试
        assertDoesNotThrow(() -> departmentSyncTestService.clearTestData());

        // 验证结果
        verify(operationLogRepository).deleteAll();
        verify(departmentSyncTestRepository).deleteAll();
    }
}
