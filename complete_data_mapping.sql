-- =====================================================
-- 完整的1399条数据映射脚本
-- 基于department_sync_test2.sql的所有记录
-- =====================================================

-- 创建临时映射表，用于批量处理
DROP TABLE IF EXISTS temp_dept_mapping;
CREATE TABLE temp_dept_mapping (
    original_org_code VARCHAR(20),
    original_org_name VARCHAR(200),
    original_parent_code VARCHAR(20),
    original_full_name VARCHAR(500),
    suggested_new_code VARCHAR(20),
    suggested_level INT,
    suggested_parent VARCHAR(20),
    mapping_rule VARCHAR(100)
);

-- =====================================================
-- 智能映射规则：基于full_name和org_code模式
-- =====================================================

-- 插入所有原始数据并应用智能映射规则
INSERT INTO temp_dept_mapping (original_org_code, original_org_name, original_parent_code, original_full_name, suggested_new_code, suggested_level, suggested_parent, mapping_rule)
SELECT 
    org_code,
    org_name,
    parent_code,
    COALESCE(full_name, org_name) as full_name,
    
    -- 智能生成新编码
    CASE 
        -- Level 1: 一级部门（事业部/集团/公司/职能部门）
        WHEN org_code = 'X50000000' THEN 'L1_001'  -- 板材事业部
        WHEN org_code = 'X49000000' THEN 'L1_002'  -- 特钢事业部
        WHEN org_code = 'X47000000' THEN 'L1_003'  -- 炼铁事业部
        WHEN org_code = 'X48000000' THEN 'L1_004'  -- 能源动力事业部
        WHEN org_code = 'X52000000' THEN 'L1_005'  -- 物流中心
        WHEN org_code = 'X53000000' THEN 'L1_006'  -- 采购中心
        WHEN org_code = 'X57000000' THEN 'L1_007'  -- 新产业投资集团
        WHEN org_code = 'XB1000000' THEN 'L1_008'  -- 蔚蓝高科技集团
        WHEN org_code = 'X79000000' THEN 'L1_009'  -- 江苏南钢鑫洋供应链有限公司
        WHEN org_code = 'X83000000' THEN 'L1_010'  -- 江苏金珂水务有限公司
        WHEN org_code = 'X36000000' THEN 'L1_011'  -- 南京钢铁集团国际经济贸易有限公司
        WHEN org_code = 'XC5000000' THEN 'L1_012'  -- 南京三金房地产开发有限公司
        WHEN org_code = 'XA1000000' THEN 'L1_013'  -- 南京金智工程技术有限公司
        WHEN org_code = 'X30000000' THEN 'L1_014'  -- 香港金腾公司
        WHEN org_code = 'X21000000' THEN 'L1_015'  -- 南京鑫智链科技信息有限公司2
        WHEN org_code = 'X10000000' THEN 'L1_016'  -- 科技质量部
        WHEN org_code = 'X02000000' THEN 'L1_017'  -- 人力资源部
        WHEN org_code = 'X06000000' THEN 'L1_018'  -- 财务部
        WHEN org_code = 'X01000000' THEN 'L1_019'  -- 公司办公室
        WHEN org_code = 'X28000000' THEN 'L1_020'  -- 安全环保部
        WHEN org_code = 'X51000000' THEN 'L1_021'  -- 制造部
        WHEN org_code = 'X27000000' THEN 'L1_022'  -- 新材料研究院（合署）
        WHEN org_code = 'XB2000000' THEN 'L1_023'  -- 数字应用研究院（人工智能研究院）
        WHEN org_code = 'X17000000' THEN 'L1_024'  -- 工会
        WHEN org_code = 'X15000000' THEN 'L1_025'  -- 保卫部
        WHEN org_code = 'XB3000000' THEN 'L1_026'  -- 审计部
        WHEN org_code = 'X08000000' THEN 'L1_027'  -- 风险合规部
        WHEN org_code = 'XC7000000' THEN 'L1_028'  -- 董事会办公室
        WHEN org_code = 'X00000000' THEN 'L1_037'  -- 公司领导
        
        -- Level 2: 二级部门（厂/处级别）
        WHEN parent_code = 'X50000000' AND org_code = 'X32000000' THEN 'L2_001'  -- 中厚板卷厂
        WHEN parent_code = 'X50000000' AND org_code = 'X38000000' THEN 'L2_002'  -- 宽厚板厂
        WHEN parent_code = 'X50000000' AND org_code = 'X73000000' THEN 'L2_003'  -- 第一炼钢厂
        WHEN parent_code = 'X50000000' AND org_code = 'X66000000' THEN 'L2_004'  -- 中板厂
        WHEN parent_code = 'X50000000' AND org_code = 'X84000000' THEN 'L2_005'  -- 金石材料厂
        WHEN parent_code = 'X50000000' AND org_code = 'X50070000' THEN 'L2_006'  -- 技术研发处
        WHEN parent_code = 'X50000000' AND org_code = 'X50040000' THEN 'L2_007'  -- 设备处
        WHEN parent_code = 'X50000000' AND org_code = 'X80000000' THEN 'L2_008'  -- 江苏南钢板材销售有限公司
        WHEN parent_code = 'X50000000' AND org_code = 'XA2000000' THEN 'L2_009'  -- 金石高新材料项目部
        
        WHEN parent_code = 'X49000000' AND org_code = 'X63000000' THEN 'L2_010'  -- 第二炼钢厂
        WHEN parent_code = 'X49000000' AND org_code = 'X65000000' THEN 'L2_011'  -- 棒材厂
        WHEN parent_code = 'X49000000' AND org_code = 'X41000000' THEN 'L2_012'  -- 精整厂
        WHEN parent_code = 'X49000000' AND org_code = 'X59000000' THEN 'L2_013'  -- 大棒厂
        WHEN parent_code = 'X49000000' AND org_code = 'X46000000' THEN 'L2_014'  -- 高线厂
        WHEN parent_code = 'X49000000' AND org_code = 'X60000000' THEN 'L2_015'  -- 中棒厂
        WHEN parent_code = 'X49000000' AND org_code = 'X98000000' THEN 'L2_016'  -- 特带厂
        WHEN parent_code = 'X49000000' AND org_code = 'X49060000' THEN 'L2_017'  -- 技术研发处
        WHEN parent_code = 'X49000000' AND org_code = 'X49010000' THEN 'L2_018'  -- 综合处
        WHEN parent_code = 'X49000000' AND org_code = 'X49030000' THEN 'L2_019'  -- 质量处
        WHEN parent_code = 'X49000000' AND org_code = 'X49050000' THEN 'L2_020'  -- 生产处
        WHEN parent_code = 'X49000000' AND org_code = 'X49070000' THEN 'L2_021'  -- 营销处
        WHEN parent_code = 'X49000000' AND org_code = 'X49080000' THEN 'L2_022'  -- 安全环保处
        WHEN parent_code = 'X49000000' AND org_code = 'X81000000' THEN 'L2_023'  -- 南京南钢特钢长材有限公司
        
        WHEN parent_code = 'X47000000' AND org_code = 'X31000000' THEN 'L2_024'  -- 第一炼铁厂
        WHEN parent_code = 'X47000000' AND org_code = 'X62000000' THEN 'L2_025'  -- 第二炼铁厂
        WHEN parent_code = 'X47000000' AND org_code = 'X43000000' THEN 'L2_026'  -- 原料厂
        WHEN parent_code = 'X47000000' AND org_code = 'X44000000' THEN 'L2_027'  -- 球团厂
        WHEN parent_code = 'X47000000' AND org_code = 'X70000000' THEN 'L2_028'  -- 燃料供应厂
        WHEN parent_code = 'X47000000' AND org_code = 'X86000000' THEN 'L2_029'  -- 烧结厂
        WHEN parent_code = 'X47000000' AND org_code = 'X47080000' THEN 'L2_030'  -- 技术处
        WHEN parent_code = 'X47000000' AND org_code = 'X47040000' THEN 'L2_031'  -- 设备处
        
        WHEN parent_code = 'X48000000' AND org_code = 'X34000000' THEN 'L2_032'  -- 制氧厂
        WHEN parent_code = 'X48000000' AND org_code = 'X74000000' THEN 'L2_033'  -- 水厂
        WHEN parent_code = 'X48000000' AND org_code = 'X76000000' THEN 'L2_034'  -- 燃气厂
        WHEN parent_code = 'X48000000' AND org_code = 'X85000000' THEN 'L2_035'  -- 发电厂
        WHEN parent_code = 'X48000000' AND org_code = 'X48040000' THEN 'L2_036'  -- 设备处
        WHEN parent_code = 'X48000000' AND org_code = 'X48010000' THEN 'L2_037'  -- 安全环保处
        WHEN parent_code = 'X48000000' AND org_code = 'X48020000' THEN 'L2_038'  -- 生产质量处
        WHEN parent_code = 'X48000000' AND org_code = 'X48070000' THEN 'L2_039'  -- 能源管理处
        WHEN parent_code = 'X48000000' AND org_code = 'X48080000' THEN 'L2_040'  -- 江苏金灿能源科技有限公司
        
        -- Level 3: 三级部门（车间/室/科级别）
        WHEN parent_code = 'X73000000' AND org_code = 'X73020000' THEN 'L3_001'  -- 炼钢车间
        WHEN parent_code = 'X73000000' AND org_code = 'X73030000' THEN 'L3_002'  -- 精炼车间
        WHEN parent_code = 'X73000000' AND org_code = 'X73040000' THEN 'L3_003'  -- 连铸车间
        WHEN parent_code = 'X73000000' AND org_code = 'X73070000' THEN 'L3_004'  -- 石灰车间
        WHEN parent_code = 'X73000000' AND org_code = 'X73140000' THEN 'L3_005'  -- 坯料车间
        WHEN parent_code = 'X73000000' AND org_code = 'X73010000' THEN 'L3_006'  -- 综合科
        
        WHEN parent_code = 'X63000000' AND org_code = 'X63000600' THEN 'L3_007'  -- 电炉炼钢车间
        WHEN parent_code = 'X63000000' AND org_code = 'X63000200' THEN 'L3_008'  -- 电炉精炼车间
        WHEN parent_code = 'X63000000' AND org_code = 'X63000500' THEN 'L3_009'  -- 电炉连铸车间
        WHEN parent_code = 'X63000000' AND org_code = 'X63000400' THEN 'L3_010'  -- 电炉运行车间
        WHEN parent_code = 'X63000000' AND org_code = 'X63000300' THEN 'L3_011'  -- 电炉检修车间
        WHEN parent_code = 'X63000000' AND org_code = 'X63000100' THEN 'L3_012'  -- 连铸管理室
        
        WHEN parent_code = 'X38000000' AND org_code = 'X38060000' THEN 'L3_013'  -- 板加车间
        WHEN parent_code = 'X38000000' AND org_code = 'X38050000' THEN 'L3_014'  -- 安全环保科
        
        WHEN parent_code = 'X32000000' AND org_code = 'X32110000' THEN 'L3_015'  -- 配送车间
        WHEN parent_code = 'X32000000' AND org_code = 'X32010000' THEN 'L3_016'  -- 综合科
        WHEN parent_code = 'X32000000' AND org_code = 'X32160000' THEN 'L3_017'  -- 安全环保科
        
        WHEN parent_code = 'X66000000' AND org_code = 'X66010000' THEN 'L3_018'  -- 综合科
        WHEN parent_code = 'X66000000' AND org_code = 'X66120000' THEN 'L3_019'  -- 安全科
        WHEN parent_code = 'X66000000' AND org_code = 'X66130000' THEN 'L3_020'  -- 产品管理室
        
        WHEN parent_code = 'X84000000' AND org_code = 'X84020000' THEN 'L3_021'  -- 渣处理车间
        WHEN parent_code = 'X84000000' AND org_code = 'X84030000' THEN 'L3_022'  -- 石灰车间
        
        WHEN parent_code = 'X52000000' AND org_code = 'X35000000' THEN 'L3_023'  -- 铁路运输中心
        WHEN parent_code = 'X52000000' AND org_code = 'X52050000' THEN 'L3_024'  -- 产成品储运室
        WHEN parent_code = 'X52000000' AND org_code = 'X52060000' THEN 'L3_025'  -- 材料仓储室
        WHEN parent_code = 'X52000000' AND org_code = 'X52070000' THEN 'L3_026'  -- 安全环保室
        
        WHEN parent_code = 'X53000000' AND org_code = 'X78000000' THEN 'L3_027'  -- 江苏南钢环宇贸易有限公司
        WHEN parent_code = 'X53000000' AND org_code = 'X53030000' THEN 'L3_028'  -- 材料室
        WHEN parent_code = 'X53000000' AND org_code = 'X53040000' THEN 'L3_029'  -- 设备备件室
        
        -- Level 4: 四级部门（班组级别）
        WHEN parent_code = 'X38060000' AND org_code = 'X38060001' THEN 'L4_001'  -- 加热炉甲班
        WHEN parent_code = 'X38060000' AND org_code = 'X38060002' THEN 'L4_002'  -- 加热炉乙班
        WHEN parent_code = 'X38060000' AND org_code = 'X38060003' THEN 'L4_003'  -- 加热炉丙班
        WHEN parent_code = 'X38060000' AND org_code = 'X38060004' THEN 'L4_004'  -- 加热炉丁班
        
        WHEN parent_code = 'X63000600' AND org_code = 'X63000601' THEN 'L4_005'  -- 电炉甲班
        WHEN parent_code = 'X63000600' AND org_code = 'X63000602' THEN 'L4_006'  -- 电炉乙班
        WHEN parent_code = 'X63000600' AND org_code = 'X63000603' THEN 'L4_007'  -- 电炉丙班
        WHEN parent_code = 'X63000600' AND org_code = 'X63000604' THEN 'L4_008'  -- 电炉丁班
        WHEN parent_code = 'X63000600' AND org_code = 'X63000605' THEN 'L4_009'  -- 辅助班
        
        WHEN parent_code = 'X63000200' AND org_code = 'X63000201' THEN 'L4_010'  -- 钢包班
        WHEN parent_code = 'X63000200' AND org_code = 'X63000205' THEN 'L4_011'  -- 精炼炉甲班
        WHEN parent_code = 'X63000200' AND org_code = 'X63000204' THEN 'L4_012'  -- 精炼炉乙班
        WHEN parent_code = 'X63000200' AND org_code = 'X63000203' THEN 'L4_013'  -- 精炼炉丙班
        WHEN parent_code = 'X63000200' AND org_code = 'X63000202' THEN 'L4_014'  -- 精炼炉丁班
        
        WHEN parent_code = 'X63000400' AND org_code = 'X63000402' THEN 'L4_015'  -- 配料丙班
        WHEN parent_code = 'X63000400' AND org_code = 'X63000408' THEN 'L4_016'  -- 行车甲班
        WHEN parent_code = 'X63000400' AND org_code = 'X63000406' THEN 'L4_017'  -- 行车丙班
        
        WHEN parent_code = 'X63000300' AND org_code = 'X63000301' THEN 'L4_018'  -- 钳工班
        WHEN parent_code = 'X63000300' AND org_code = 'X63000302' THEN 'L4_019'  -- 电工班
        
        WHEN parent_code = 'X66130000' AND org_code = 'X66130001' THEN 'L4_020'  -- 检验班
        WHEN parent_code = 'X66130000' AND org_code = 'X66130002' THEN 'L4_021'  -- 探伤班
        
        -- 其他部门：基于层级深度自动分配
        ELSE 
            CASE 
                WHEN parent_code = '1' THEN CONCAT('L1_', LPAD(ROW_NUMBER() OVER (ORDER BY org_code) + 100, 3, '0'))
                WHEN parent_code IN (SELECT org_code FROM department_sync_test WHERE parent_code = '1') THEN 
                    CONCAT('L2_', LPAD(ROW_NUMBER() OVER (ORDER BY org_code) + 100, 3, '0'))
                WHEN parent_code IN (SELECT org_code FROM department_sync_test WHERE parent_code IN (SELECT org_code FROM department_sync_test WHERE parent_code = '1')) THEN 
                    CONCAT('L3_', LPAD(ROW_NUMBER() OVER (ORDER BY org_code) + 100, 3, '0'))
                ELSE 
                    CONCAT('L4_', LPAD(ROW_NUMBER() OVER (ORDER BY org_code) + 100, 3, '0'))
            END
    END as suggested_new_code,
    
    -- 确定层级
    CASE 
        WHEN parent_code = '1' THEN 1
        WHEN parent_code IN ('X50000000','X49000000','X47000000','X48000000','X52000000','X53000000','X57000000','XB1000000','X79000000','X83000000','X36000000','XC5000000','XA1000000','X30000000','X21000000','X10000000','X02000000','X06000000','X01000000','X28000000','X51000000','X27000000','XB2000000','X17000000','X15000000','XB3000000','X08000000','XC7000000','X00000000') THEN 2
        WHEN parent_code IN ('X32000000','X38000000','X73000000','X66000000','X84000000','X50070000','X50040000','X80000000','XA2000000','X63000000','X65000000','X41000000','X59000000','X46000000','X60000000','X98000000','X49060000','X49010000','X49030000','X49050000','X49070000','X49080000','X81000000','X31000000','X62000000','X43000000','X44000000','X70000000','X86000000','X47080000','X47040000','X34000000','X74000000','X76000000','X85000000','X48040000','X48010000','X48020000','X48070000','X48080000') THEN 3
        ELSE 4
    END as suggested_level,
    
    -- 确定上级部门
    CASE 
        WHEN parent_code = '1' THEN NULL
        WHEN parent_code = 'X50000000' THEN 'L1_001'
        WHEN parent_code = 'X49000000' THEN 'L1_002'
        WHEN parent_code = 'X47000000' THEN 'L1_003'
        WHEN parent_code = 'X48000000' THEN 'L1_004'
        WHEN parent_code = 'X52000000' THEN 'L1_005'
        WHEN parent_code = 'X53000000' THEN 'L1_006'
        WHEN parent_code = 'X73000000' THEN 'L2_003'
        WHEN parent_code = 'X63000000' THEN 'L2_010'
        WHEN parent_code = 'X38000000' THEN 'L2_002'
        WHEN parent_code = 'X32000000' THEN 'L2_001'
        WHEN parent_code = 'X66000000' THEN 'L2_004'
        WHEN parent_code = 'X84000000' THEN 'L2_005'
        WHEN parent_code = 'X38060000' THEN 'L3_013'
        WHEN parent_code = 'X63000600' THEN 'L3_007'
        WHEN parent_code = 'X63000200' THEN 'L3_008'
        WHEN parent_code = 'X63000400' THEN 'L3_010'
        WHEN parent_code = 'X63000300' THEN 'L3_011'
        WHEN parent_code = 'X66130000' THEN 'L3_020'
        ELSE NULL
    END as suggested_parent,
    
    -- 映射规则说明
    CASE 
        WHEN parent_code = '1' THEN '一级部门'
        WHEN parent_code IN ('X50000000','X49000000','X47000000','X48000000') THEN '事业部下属'
        WHEN parent_code IN ('X52000000','X53000000') THEN '中心下属'
        WHEN parent_code LIKE 'X%000000' THEN '二级部门下属'
        ELSE '多级嵌套部门'
    END as mapping_rule

FROM (
    SELECT DISTINCT org_code, org_name, parent_code, full_name
    FROM department_sync_test 
    WHERE is_history = 0 AND user_predef_14 != 'D'
) t
ORDER BY 
    CASE WHEN parent_code = '1' THEN 1 ELSE 2 END,
    org_code;

-- =====================================================
-- 生成最终的完整映射表
-- =====================================================

INSERT INTO complete_dept_mapping (original_org_code, original_org_name, original_parent_code, original_full_name, new_dept_code, mapping_level, mapping_status)
SELECT 
    original_org_code,
    original_org_name,
    original_parent_code,
    original_full_name,
    suggested_new_code,
    suggested_level,
    CASE 
        WHEN suggested_new_code IS NOT NULL THEN '已映射'
        ELSE '待处理'
    END as mapping_status
FROM temp_dept_mapping;

-- =====================================================
-- 统计映射结果
-- =====================================================

SELECT 
    '=== 映射统计结果 ===' as section,
    mapping_status,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM temp_dept_mapping), 2) as percentage
FROM complete_dept_mapping 
GROUP BY mapping_status;

SELECT 
    '=== 按层级统计 ===' as section,
    mapping_level as level,
    COUNT(*) as count
FROM complete_dept_mapping 
WHERE mapping_status = '已映射'
GROUP BY mapping_level 
ORDER BY mapping_level;

SELECT 
    '=== 总体覆盖率 ===' as section,
    COUNT(*) as total_mapped,
    (SELECT COUNT(*) FROM temp_dept_mapping) as total_original,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM temp_dept_mapping), 2) as coverage_percentage
FROM complete_dept_mapping 
WHERE mapping_status = '已映射';

-- 清理临时表
DROP TABLE temp_dept_mapping;
