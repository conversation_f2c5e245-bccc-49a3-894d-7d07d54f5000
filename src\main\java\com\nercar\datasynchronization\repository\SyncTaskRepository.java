package com.nercar.datasynchronization.repository;

import com.nercar.datasynchronization.entity.SyncTask;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface SyncTaskRepository extends JpaRepository<SyncTask, Long> {
    
    Optional<SyncTask> findByTaskId(String taskId);
    
    List<SyncTask> findByStatus(String status);
    
    List<SyncTask> findByTaskType(String taskType);
    /**
     * 根据任务类型和状态查找同步任务，按结束时间降序排序
     * @param taskType 任务类型
     * @param status 任务状态
     * @return 同步任务列表
     */
    List<SyncTask> findByTaskTypeAndStatusOrderByEndDateDesc(String taskType, String status);
}