package com.nercar.datasynchronization.service.impl;

import com.nercar.datasynchronization.entity.Employee;
import com.nercar.datasynchronization.entity.EmployeePosition;
import com.nercar.datasynchronization.entity.EmployeeSystem;
import com.nercar.datasynchronization.entity.EmployeeTitle;
import com.nercar.datasynchronization.repository.EmployeePositionRepository;
import com.nercar.datasynchronization.repository.EmployeeRepository;
import com.nercar.datasynchronization.repository.EmployeeSystemRepository;
import com.nercar.datasynchronization.repository.EmployeeTitleRepository;
//import com.nercar.datasynchronization.service.DataMigrationService;
import com.nercar.datasynchronization.service.EmployeeSyncService;
import com.nercar.datasynchronization.utils.XmlUtils;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.Element;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.datasource.DataSourceUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.sql.DataSource;
import java.sql.Connection;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Service
public class EmployeeSyncServiceImpl implements EmployeeSyncService {

    @Autowired
    private EmployeeRepository employeeRepository;

    @Autowired
    private EmployeePositionRepository positionRepository;

    @Autowired
    private EmployeeTitleRepository titleRepository;

    @Autowired
    private EmployeeSystemRepository systemRepository;

    @Autowired
    private PlatformTransactionManager transactionManager;

    @Autowired
    private DataSource dataSource;

//    @Autowired
//    private DataMigrationService dataMigrationService;

    private static final ThreadLocal<SimpleDateFormat> DATE_FORMAT =
            ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd"));

    private static final ThreadLocal<SimpleDateFormat> DATE_FORMAT_ALT =
            ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy/MM/dd HH:mm:ss"));


    private static final int BATCH_SIZE = 50; // 进一步减小批处理大小

    private Map<String, Long> employeeIdCache = new ConcurrentHashMap<>();
    private Map<String, Long> positionIdCache = new ConcurrentHashMap<>();
    private Map<String, Long> titleIdCache = new ConcurrentHashMap<>();
    private Map<String, Long> systemIdCache = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        preloadEmployeeCache();
    }

    private void preloadEmployeeCache() {
        log.info("开始预加载员工缓存...");

        try {
            // 预加载员工ID缓存
            employeeRepository.findAll().forEach(emp -> {
                if (emp.getMdmId() != null && emp.getId() != null) {
                    employeeIdCache.put(emp.getMdmId(), emp.getId());
                }
            });
            log.info("预加载员工ID缓存完成，共 {} 条记录", employeeIdCache.size());

            // 预加载岗位ID缓存
            positionRepository.findAll().forEach(pos -> {
                if (pos.getGuid() != null && pos.getId() != null) {
                    positionIdCache.put(pos.getGuid(), pos.getId());
                }
            });
            log.info("预加载岗位ID缓存完成，共 {} 条记录", positionIdCache.size());

            // 预加载职称ID缓存
            titleRepository.findAll().forEach(title -> {
                if (title.getGuid() != null && title.getId() != null) {
                    titleIdCache.put(title.getGuid(), title.getId());
                }
            });
            log.info("预加载职称ID缓存完成，共 {} 条记录", titleIdCache.size());

            // 预加载系统标识ID缓存
            systemRepository.findAll().forEach(sys -> {
                if (sys.getGuid() != null && sys.getId() != null) {
                    systemIdCache.put(sys.getGuid(), sys.getId());
                }
            });
            log.info("预加载系统标识ID缓存完成，共 {} 条记录", systemIdCache.size());

        } catch (Exception e) {
            log.error("预加载缓存失败: {}", e.getMessage(), e);
            // 清空可能部分加载的缓存
            employeeIdCache.clear();
            positionIdCache.clear();
            titleIdCache.clear();
            systemIdCache.clear();
        }
    }

    @Override
    public void syncEmployees(String xmlData) {
        // 为了兼容现有代码，当不指定时间范围时，使用当前时间
        syncEmployees(xmlData, new Date(), new Date());
    }

    @Override
    public void syncEmployees(String xmlData, Date startDate, Date endDate) {
        long startTime = System.currentTimeMillis();
        log.info("开始同步员工数据，数据长度: {}, 时间范围: {} 至 {}",
                xmlData.length(),
                new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(startDate),
                new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(endDate));

        // 如果缓存为空，预加载员工ID缓存
        if (employeeIdCache.isEmpty()) {
            preloadEmployeeCache();
        }

        // 创建本地批处理列表
        List<Employee> employeeBatch = new ArrayList<>(BATCH_SIZE);
        List<EmployeePosition> positionBatch = new ArrayList<>(BATCH_SIZE);
        List<EmployeeTitle> titleBatch = new ArrayList<>(BATCH_SIZE);
        List<EmployeeSystem> systemBatch = new ArrayList<>(BATCH_SIZE);

        // 记录处理的实体数量
        final AtomicInteger processingCount = new AtomicInteger(0);

        try {
            // 先解析XML，提取所有数据到内存中
            final List<Element> allElements = new ArrayList<>();

            // 使用SAX解析器流式获取所有元素，但不立即处理
            XmlUtils.parseXmlWithSAX(xmlData, "O_DATA", allElements::add);

            log.info("XML数据解析完成，共发现{}条记录", allElements.size());

            // 然后在独立事务中批量处理解析到的元素
            for (Element dataElement : allElements) {
                try {
                    processEmployee(dataElement, employeeBatch, positionBatch, titleBatch, systemBatch, startDate, endDate);
                    int count = processingCount.incrementAndGet();

                    // 处理批次，当达到批处理大小时保存
                    if (employeeBatch.size() >= BATCH_SIZE) {
                        saveEmployeesBatch(new ArrayList<>(employeeBatch));
                        employeeBatch.clear();
                    }

                    if (positionBatch.size() >= BATCH_SIZE) {
                        savePositionsBatch(new ArrayList<>(positionBatch));
                        positionBatch.clear();
                    }

                    if (titleBatch.size() >= BATCH_SIZE) {
                        saveTitlesBatch(new ArrayList<>(titleBatch));
                        titleBatch.clear();
                    }

                    if (systemBatch.size() >= BATCH_SIZE) {
                        saveSystemsBatch(new ArrayList<>(systemBatch));
                        systemBatch.clear();
                    }
                } catch (Exception e) {
                    log.error("处理单个员工记录失败: {}", e.getMessage());
                }
            }

            // 保存剩余批次
            if (!employeeBatch.isEmpty()) {
                saveEmployeesBatch(employeeBatch);
                employeeBatch.clear();
            }

            if (!positionBatch.isEmpty()) {
                savePositionsBatch(positionBatch);
                positionBatch.clear();
            }

            if (!titleBatch.isEmpty()) {
                saveTitlesBatch(titleBatch);
                titleBatch.clear();
            }

            if (!systemBatch.isEmpty()) {
                saveSystemsBatch(systemBatch);
                systemBatch.clear();
            }

//            // 在员工数据同步完成后，使用传入的时间范围创建关联关系
//            log.info("开始创建员工-部门关联关系，时间范围从 {} 到 {}",
//                    new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(startDate),
//                    new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(endDate));
//
//            dataMigrationService.createEmployeeDepartmentRelations(startDate, endDate);
//            log.info("员工-部门关联关系创建完成");

            long endTime = System.currentTimeMillis();
            log.info("员工数据同步完成，共处理 {} 条记录，耗时: {}ms", processingCount.get(), (endTime - startTime));
        } catch (Exception e) {
            log.error("同步员工数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("同步员工数据失败", e);
        } finally {
            // 确保释放所有资源
            employeeBatch.clear();
            positionBatch.clear();
            titleBatch.clear();
            systemBatch.clear();
        }
    }

    private void saveEmployeesBatch(List<Employee> employees) {
        if (employees.isEmpty()) return;

        TransactionStatus status = null;
        try {
            DefaultTransactionDefinition def = new DefaultTransactionDefinition();
            def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
            def.setTimeout(30); // 30秒超时

            status = transactionManager.getTransaction(def);
            employeeRepository.saveAll(employees);
            transactionManager.commit(status);
            status = null;

            // 更新缓存
            for (Employee emp : employees) {
                if (emp.getId() != null && emp.getMdmId() != null) {
                    employeeIdCache.put(emp.getMdmId(), emp.getId());
                }
            }

            log.debug("保存员工批次成功，数量: {}", employees.size());
        } catch (Exception e) {
            if (status != null) {
                transactionManager.rollback(status);
            }
            log.error("保存员工数据批次失败: {}", e.getMessage());
        }
    }

    private void savePositionsBatch(List<EmployeePosition> positions) {
        if (positions.isEmpty()) return;

        TransactionStatus status = null;
        try {
            DefaultTransactionDefinition def = new DefaultTransactionDefinition();
            def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
            def.setTimeout(30);

            status = transactionManager.getTransaction(def);
            positionRepository.saveAll(positions);
            transactionManager.commit(status);
            status = null;

            // 更新缓存
            for (EmployeePosition pos : positions) {
                if (pos.getId() != null && pos.getGuid() != null) {
                    positionIdCache.put(pos.getGuid(), pos.getId());
                }
            }

            log.debug("保存岗位批次成功，数量: {}", positions.size());
        } catch (Exception e) {
            if (status != null) {
                transactionManager.rollback(status);
            }
            log.error("保存岗位数据批次失败: {}", e.getMessage());
        }
    }

    private void saveTitlesBatch(List<EmployeeTitle> titles) {
        if (titles.isEmpty()) return;

        TransactionStatus status = null;
        try {
            DefaultTransactionDefinition def = new DefaultTransactionDefinition();
            def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
            def.setTimeout(30);

            status = transactionManager.getTransaction(def);
            titleRepository.saveAll(titles);
            transactionManager.commit(status);
            status = null;

            // 更新缓存
            for (EmployeeTitle title : titles) {
                if (title.getId() != null && title.getGuid() != null) {
                    titleIdCache.put(title.getGuid(), title.getId());
                }
            }

            log.debug("保存职称批次成功，数量: {}", titles.size());
        } catch (Exception e) {
            if (status != null) {
                transactionManager.rollback(status);
            }
            log.error("保存职称数据批次失败: {}", e.getMessage());
        }
    }

    private void saveSystemsBatch(List<EmployeeSystem> systems) {
        if (systems.isEmpty()) return;

        TransactionStatus status = null;
        try {
            DefaultTransactionDefinition def = new DefaultTransactionDefinition();
            def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
            def.setTimeout(30);

            status = transactionManager.getTransaction(def);
            systemRepository.saveAll(systems);
            transactionManager.commit(status);
            status = null;

            // 更新缓存
            for (EmployeeSystem sys : systems) {
                if (sys.getId() != null && sys.getGuid() != null) {
                    systemIdCache.put(sys.getGuid(), sys.getId());
                }
            }

            log.debug("保存系统标识批次成功，数量: {}", systems.size());
        } catch (Exception e) {
            if (status != null) {
                transactionManager.rollback(status);
            }
            log.error("保存系统标识数据批次失败: {}", e.getMessage());
        }
    }

    private void processEmployee(Element dataElement,
                               List<Employee> employeeBatch,
                               List<EmployeePosition> positionBatch,
                               List<EmployeeTitle> titleBatch,
                               List<EmployeeSystem> systemBatch,
                               Date startDate,
                               Date endDate) {
        try {
            // 解析员工基本信息
            Employee employee = new Employee();
            String mdmId = XmlUtils.getElementText(dataElement, "MDMZGZD_NM");
            if (mdmId == null || mdmId.isEmpty()) {
                log.warn("员工MDM ID为空，跳过处理");
                return;
            }

            employee.setMdmId(mdmId);

            // 使用缓存查找员工ID
            Long employeeId = employeeIdCache.get(mdmId);
            if (employeeId != null) {
                employee.setId(employeeId);
                // 如果是更新现有记录，设置更新时间
                employee.setUpdatedTime(endDate);
            } else {
                // 如果是新记录，设置创建和更新时间
                employee.setCreatedTime(startDate);
                employee.setUpdatedTime(endDate);
            }

            // 根据文档格式提取并设置字段，确保与文档描述的出参匹配
            employee.setMdmHrdwnm(XmlUtils.getElementText(dataElement, "MDMZGZD_HRDWNM")); // 所属组织
            employee.setEmployeeCode(XmlUtils.getElementText(dataElement, "MDMZGZD_ZGBH")); // 人员编号
            employee.setEmployeeName(XmlUtils.getElementText(dataElement, "MDMZGZD_ZGXM")); // 人员姓名
            employee.setGender(XmlUtils.getElementText(dataElement, "MDMZGZD_ZGXB")); // 性别：1=男，2=女
            employee.setMobile(XmlUtils.getElementText(dataElement, "MDMZGZD_MOBILE")); // 手机号
            employee.setStatus(XmlUtils.getElementText(dataElement, "USERPREDEF_16")); // 证件类型
            employee.setIdCard(XmlUtils.getElementText(dataElement, "USERPREDEF_21")); // 身份证号码
            employee.setAccount(XmlUtils.getElementText(dataElement, "MDMZGZD_ZH")); // 账号

            // 处理出生日期
            // 然后在代码中尝试多种格式：
            String birthDateStr = XmlUtils.getElementText(dataElement, "MDMZGZD_CSRQ");
            if (birthDateStr != null && !birthDateStr.isEmpty()) {
                try {
                    // 先尝试标准格式
                    employee.setBirthDate(DATE_FORMAT.get().parse(birthDateStr));
                } catch (ParseException e) {
                    try {
                        // 再尝试备用格式
                        employee.setBirthDate(DATE_FORMAT_ALT.get().parse(birthDateStr));
                    } catch (ParseException ex) {
                        log.warn("解析出生日期失败，尝试了多种格式: {}", birthDateStr);
                    }
                }
            }

            // 设置其他基本信息字段
            employee.setEmail(XmlUtils.getElementText(dataElement, "MDMZGZD_EMAIL")); // 员工邮箱
            employee.setOrgType(XmlUtils.getElementText(dataElement, "USERPREDEF_17")); // 国籍
            employee.setOrgLevel1(XmlUtils.getElementText(dataElement, "USERPREDEF_18")); // 民族
            employee.setOrgLevel2(XmlUtils.getElementText(dataElement, "USERPREDEF_19")); // 政治面貌
            employee.setOrgLevel3(XmlUtils.getElementText(dataElement, "USERPREDEF_20")); // 文化程度
            employee.setWechat(XmlUtils.getElementText(dataElement, "MDMZGZD_WECHAT")); // 微信号
            employee.setTel(XmlUtils.getElementText(dataElement, "MDMZGZD_TEL")); // 固定电话
            employee.setNote(XmlUtils.getElementText(dataElement, "MDMZGZD_NOTE")); // 备注
            employee.setIsDisabled(XmlUtils.getElementText(dataElement, "MDMZGZD_TYBZ")); // 是否停用：0=启用，1=禁用
            employee.setUserType(XmlUtils.getElementText(dataElement, "USERPREDEF_3")); // 操作标识：N=新增，U=修改，D=删除
            employee.setOrgCode(XmlUtils.getElementText(dataElement, "USERPREDEF_4")); // 工作状态
            employee.setIdName(XmlUtils.getElementText(dataElement, "USERPREDEF_5")); // 判重项目值域
            employee.setUserCategory(XmlUtils.getElementText(dataElement, "USERPREDEF_7")); // 用户分类
            employee.setUserLevel(XmlUtils.getElementText(dataElement, "USERPREDEF_8")); // 用户级别
            employee.setUserStatus(XmlUtils.getElementText(dataElement, "USERPREDEF_9")); // 用户状态

            // 添加到批处理列表
            employeeBatch.add(employee);

            // 处理岗位信息（与文档的从表1对应）
            Element positions = dataElement.element("O_CHILDS1");
            if (positions != null) {
                List<Element> positionElements = positions.elements("O_CHILD");
                for (Element posElement : positionElements) {
                    processPosition(posElement, employee.getMdmId(), positionBatch, startDate, endDate);
                }
            }

            // 处理职称信息（与文档的从表2对应）
            Element titles = dataElement.element("O_CHILDS2");
            if (titles != null) {
                List<Element> titleElements = titles.elements("O_CHILD");
                for (Element titleElement : titleElements) {
                    processTitle(titleElement, employee.getMdmId(), titleBatch, startDate, endDate);
                }
            }

            // 处理系统标识（与文档的从表3对应）
            Element systems = dataElement.element("O_CHILDS3");
            if (systems != null) {
                List<Element> systemElements = systems.elements("O_CHILD");
                for (Element sysElement : systemElements) {
                    processSystem(sysElement, employee.getMdmId(), systemBatch, startDate, endDate);
                }
            }
        } catch (Exception e) {
            log.error("解析员工数据失败: {}", e.getMessage());
        }
    }

    private void processPosition(Element element, String employeeMdmId, List<EmployeePosition> positionBatch, Date startDate, Date endDate) {
        try {
            String guid = XmlUtils.getElementText(element, "MDMRYZZGW_V4_GUID");
            if (guid == null || guid.isEmpty()) {
                return;
            }

            EmployeePosition position = new EmployeePosition();
            position.setGuid(guid);

            // 使用缓存查找ID
            Long positionId = positionIdCache.get(guid);
            if (positionId != null) {
                position.setId(positionId);
                position.setUpdatedTime(endDate);
            } else {
                position.setCreatedTime(startDate);
                position.setUpdatedTime(endDate);
            }

            // 设置员工关联ID（从XML中读取，而不是使用参数）
            String xmlEmployeeMdmId = XmlUtils.getElementText(element, "MDMRYZZGW_V4_ZBNM");
            position.setEmployeeMdmId(xmlEmployeeMdmId != null ? xmlEmployeeMdmId : employeeMdmId);

            // 对应文档中的字段
            position.setPositionCode(XmlUtils.getElementText(element, "MDMRYZZGW_V4_PCXMZY")); // 判重项目值域
            position.setOrgCode(XmlUtils.getElementText(element, "MDMRYZZGW_V4_UDEF1")); // 所属组织编码
            position.setDepartmentCode(XmlUtils.getElementText(element, "MDMRYZZGW_V4_UDEF3")); // 所属职务工种编码
            position.setIsPrimary(XmlUtils.getElementText(element, "MDMRYZZGW_V4_UDEF7")); // 是否停用状态
            position.setStatus(XmlUtils.getElementText(element, "MDMRYZZGW_V4_UDEF8")); // 操作标识
            position.setIsActive(XmlUtils.getElementText(element, "MDMRYZZGW_V4_UDEF5")); // 是否主岗
            position.setPositionDetailCode(XmlUtils.getElementText(element, "MDMRYZZGW_V4_UDEF6")); // 主岗编码

            // 添加到批处理列表
            positionBatch.add(position);
        } catch (Exception e) {
            log.error("处理岗位数据失败: {}", e.getMessage());
        }
    }

    private void processTitle(Element element, String employeeMdmId, List<EmployeeTitle> titleBatch, Date startDate, Date endDate) {
        try {
            String guid = XmlUtils.getElementText(element, "MDMRYGJ_V4_GUID");
            if (guid == null || guid.isEmpty()) {
                return;
            }

            EmployeeTitle title = new EmployeeTitle();
            title.setGuid(guid);

            // 使用缓存查找ID
            Long titleId = titleIdCache.get(guid);
            if (titleId != null) {
                title.setId(titleId);
                title.setUpdatedTime(endDate);
            } else {
                title.setCreatedTime(startDate);
                title.setUpdatedTime(endDate);
            }

            // 设置员工关联ID（从XML中读取，而不是使用参数）
            String xmlEmployeeMdmId = XmlUtils.getElementText(element, "MDMRYGJ_V4_ZBNM");
            title.setEmployeeMdmId(xmlEmployeeMdmId != null ? xmlEmployeeMdmId : employeeMdmId);

            // 对应文档中的字段
            title.setTitleCode(XmlUtils.getElementText(element, "MDMRYGJ_V4_PCXMZY")); // 判重项目值域
            title.setTitleType(XmlUtils.getElementText(element, "MDMRYGJ_V4_UDEF1")); // 员工所属组织
            title.setTitleLevel(XmlUtils.getElementText(element, "MDMRYGJ_V4_UDEF3")); // 职级编码
            title.setStatus(XmlUtils.getElementText(element, "MDMRYGJ_V4_UDEF5")); // 操作标识
            title.setTitleName(XmlUtils.getElementText(element, "MDMRYGJ_V4_UDEF8")); // 职称
            title.setTitleCategory(XmlUtils.getElementText(element, "MDMRYGJ_V4_UDEF9")); // 层级O、E、C、S
            title.setDefaultOrgCode(XmlUtils.getElementText(element, "MDMRYGJ_V4_UDEF2")); // 默认组织编码
            title.setIsDisabled(XmlUtils.getElementText(element, "MDMRYGJ_V4_UDEF6")); // 是否停用状态

            // 添加到批处理列表
            titleBatch.add(title);
        } catch (Exception e) {
            log.error("处理职称数据失败: {}", e.getMessage());
        }
    }

    private void processSystem(Element element, String employeeMdmId, List<EmployeeSystem> systemBatch, Date startDate, Date endDate) {
        try {
            String guid = XmlUtils.getElementText(element, "NGTYYSB_GUID");
            if (guid == null || guid.isEmpty()) {
                return;
            }

            EmployeeSystem system = new EmployeeSystem();
            system.setGuid(guid);

            // 使用缓存查找ID
            Long systemId = systemIdCache.get(guid);
            if (systemId != null) {
                system.setId(systemId);
                system.setUpdatedTime(endDate);
            } else {
                system.setCreatedTime(startDate);
                system.setUpdatedTime(endDate);
            }

            // 设置员工关联ID（从XML中读取，而不是使用参数）
            String xmlEmployeeMdmId = XmlUtils.getElementText(element, "NGTYYSB_ZBNM");
            system.setEmployeeMdmId(xmlEmployeeMdmId != null ? xmlEmployeeMdmId : employeeMdmId);

            // 对应文档中的字段
            system.setSystemCode(XmlUtils.getElementText(element, "NGTYYSB_LYXTBH")); // 来源系统编码
            system.setSystemDataId(XmlUtils.getElementText(element, "NGTYYSB_LYXTDATANM")); // 来源系统人员内码
            system.setEmployeeCode(XmlUtils.getElementText(element, "NGTYYSB_UDEF1")); // 来源系统人员编号
            system.setOrgCode(XmlUtils.getElementText(element, "NGTYYSB_UDEF3")); // 来源系统组织编号
            system.setDepartmentCode(XmlUtils.getElementText(element, "NGTYYSB_UDEF4")); // 来源系统职务工种编码

            // 添加新字段处理
            system.setLoginAccount(XmlUtils.getElementText(element, "NGTYYSB_UDEF5")); // 登录账号
            system.setCompanyCode(XmlUtils.getElementText(element, "NGTYYSB_UDEF2")); // 公司别标识

            // 添加到批处理列表
            systemBatch.add(system);
        } catch (Exception e) {
            log.error("处理系统标识数据失败: {}", e.getMessage());
        }
    }

    @PreDestroy
    public void cleanup() {
        DATE_FORMAT.remove();
        employeeIdCache.clear();
        positionIdCache.clear();
        titleIdCache.clear();
        systemIdCache.clear();
    }
}