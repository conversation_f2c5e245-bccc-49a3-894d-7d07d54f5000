CREATE TABLE `employee_title` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `employee_mdm_id` varchar(50) DEFAULT NULL,
  `mdmrygj_v4_zbnm` varchar(255) DEFAULT NULL,
  `mdmrygj_v4_udef1` varchar(255) DEFAULT NULL,
  `mdmrygj_v4_guid` varchar(255) DEFAULT NULL,
  `mdmrygj_v4_udef13` varchar(255) DEFAULT NULL,
  `mdmrygj_v4_udef14` varchar(255) DEFAULT NULL,
  `mdmrygj_v4_udef15` varchar(255) DEFAULT NULL,
  `mdmrygj_v4_udef16` varchar(255) DEFAULT NULL,
  `mdmrygj_v4_udef10` varchar(255) DEFAULT NULL,
  `mdmrygj_v4_udef11` varchar(255) DEFAULT NULL,
  `mdmrygj_v4_udef12` varchar(255) DEFAULT NULL,
  `mdmrygj_v4_udef2` varchar(255) DEFAULT NULL,
  `mdmrygj_v4_udef3` varchar(255) DEFAULT NULL,
  `mdmrygj_v4_udef4` varchar(255) DEFAULT NULL,
  `mdmrygj_v4_udef5` varchar(255) DEFAULT NULL,
  `mdmrygj_v4_udef6` varchar(255) DEFAULT NULL,
  `mdmrygj_v4_udef7` varchar(255) DEFAULT NULL,
  `mdmrygj_v4_udef8` varchar(255) DEFAULT NULL,
  `mdmrygj_v4_pcxmzy` varchar(255) DEFAULT NULL,
  `mdmrygj_v4_udef9` varchar(255) DEFAULT NULL,
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  INDEX `idx_employee_mdm_id` (`employee_mdm_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
