server:
  port: 8080

spring:
  application:
    name: data-synchronization

  # 禁用开发工具自动重启
  devtools:
    restart:
      enabled: false
    livereload:
      enabled: false

  # 数据库配置 - 主数据源
  datasource:
    url: ******************************************************************************************************************************************************************
    username: root
    password: Pass_123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    # 连接池配置
    hikari:
      maximum-pool-size: 30               # 最大连接数
      minimum-idle: 10                    # 最小空闲连接数
      connection-timeout: 30000           # 连接超时时间
      max-lifetime: 1200000               # 连接最大生命周期
      idle-timeout: 300000                # 空闲连接超时时间
      leak-detection-threshold: 180000    # 连接泄漏检测阈值（3分钟）
      auto-commit: true                   # 自动提交
      connection-test-query: SELECT 1     # 连接测试查询
      validation-timeout: 5000            # 验证超时时间



  jpa:
    database-platform: org.hibernate.dialect.MySQL8Dialect
    hibernate:
      ddl-auto: update
    show-sql: false  # 将 true 改为 false
    properties:
      hibernate:
        jdbc:
          batch_size: 100            # 批处理大小
        order_inserts: true
        order_updates: true
        connection:
          provider_disables_autocommit: false # 禁用自动提交

  # 定时任务配置
  quartz:
    job-store-type: memory

# 日志配置
logging:
  level:
    root: info
    com.nercar: debug
    org.hibernate.SQL: WARN  # 添加此行
    org.hibernate.type.descriptor.sql: WARN  # 添加此行
    org.springframework.jdbc.core: WARN  # 添加此行
  file:
    name: logs/data-sync.log

# 同步配置
sync:
  # 定时任务配置
  cron: "0 0 3 * * ?" # 每天凌晨3点执行

  # SOAP服务配置
  soap:
    user-url: https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery
    org-url: https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery
    connect-timeout: 30000    # 连接超时30秒
    read-timeout: 120000      # 读取超时2分钟（MDM系统响应较慢）

  # HTTP接口配置
  http:
    position-url: http://************:9000/ds/dgc/getQualityPersonnelInfo
    timeout: 30000
    retry-count: 3


# Swagger配置
springdoc:
  api-docs:
    enabled: true
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    tagsSorter: alpha
    operationsSorter: method
  packages-to-scan: com.nercar.datasynchronization.controller

# MyBatis配置
mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.nercar.datasynchronization.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl  # 将 Slf4jImpl 改为 NoLoggingImpl

# 应用配置
app:
  # 重试机制配置
  retry:
    enabled: true          # 是否启用重试机制
    max-retries: 3         # 最大重试次数
    retry-delay: 5000      # 重试间隔（毫秒）
    exponential-backoff: false  # 是否启用指数退避
    base-delay: 2000       # 指数退避基础延迟（毫秒）
    max-delay: 30000       # 指数退避最大延迟（毫秒）