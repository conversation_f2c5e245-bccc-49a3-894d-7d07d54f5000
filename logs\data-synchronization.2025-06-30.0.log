2025-06-30 09:33:49.822 [restartedMain] INFO  c.n.d.DataSynchronizationApplication - Starting DataSynchronizationApplication using Java ******** with PID 19424 (E:\fushun\data-synchronization\target\classes started by Dell in E:\fushun\data-synchronization)
2025-06-30 09:33:49.828 [restartedMain] DEBUG c.n.d.DataSynchronizationApplication - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-30 09:33:49.830 [restartedMain] INFO  c.n.d.DataSynchronizationApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-30 09:33:53.259 [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-30 09:33:54.552 [restartedMain] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5548e73e
2025-06-30 09:33:55.677 [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-30 09:33:55.906 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQL8Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-30 09:33:55.907 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000026: MySQL8Dialect has been deprecated; use org.hibernate.dialect.MySQLDialect instead
2025-06-30 09:34:05.791 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-30 09:34:06.675 [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2025-06-30 09:34:06.693 [restartedMain] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-06-30 09:34:06.694 [restartedMain] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
2025-06-30 09:34:06.694 [restartedMain] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-06-30 09:34:06.695 [restartedMain] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-06-30 09:34:06.695 [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-06-30 09:34:06.695 [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
2025-06-30 09:34:06.695 [restartedMain] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@484305f
2025-06-30 09:34:07.507 [restartedMain] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-06-30 09:34:07.524 [restartedMain] INFO  c.n.d.DataSynchronizationApplication - Started DataSynchronizationApplication in 19.184 seconds (process running for 21.603)
2025-06-30 09:34:07.525 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - =====================================================
2025-06-30 09:34:07.526 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 数据同步应用启动成功！
2025-06-30 09:34:07.526 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 应用访问地址: http://192.168.101.1:8080
2025-06-30 09:34:07.526 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - Swagger文档地址: http://192.168.101.1:8080/swagger-ui.html
2025-06-30 09:34:07.526 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - =====================================================
2025-06-30 09:34:07.763 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - SOAP服务调用信息：
2025-06-30 09:34:07.764 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 组织数据服务地址: https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery
2025-06-30 09:34:07.764 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 接口方法: GetOrgInfoFromMDM
2025-06-30 09:34:07.764 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 人员数据服务地址: https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery
2025-06-30 09:34:07.765 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 接口方法: GetUserInfoFromMDM
2025-06-30 09:34:07.765 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - =====================================================
2025-06-30 09:34:07.765 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - HTTP接口调用信息：
2025-06-30 09:34:07.765 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 岗位数据服务地址: http://172.28.98.74:9000/ds/dgc/getQualityPersonnelInfo
2025-06-30 09:34:07.765 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 接口方法: getQualityPersonnelInfo
2025-06-30 09:34:07.765 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - =====================================================
2025-06-30 09:34:07.765 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 定时任务配置信息：
2025-06-30 09:34:07.765 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 数据同步任务执行时间: 0 0 3 * * ? (凌晨3点执行)
2025-06-30 09:34:07.765 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 数据同步范围: 昨天00:00:00至23:59:59的数据
2025-06-30 09:34:07.765 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - =====================================================
2025-06-30 09:34:34.949 [Thread-5] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-30 09:34:34.985 [Thread-5] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-06-30 09:34:34.985 [Thread-5] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-30 09:34:34.985 [Thread-5] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-06-30 09:34:34.993 [Thread-5] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-30 09:34:35.008 [Thread-5] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-30 09:34:35.253 [restartedMain] INFO  c.n.d.DataSynchronizationApplication - Starting DataSynchronizationApplication using Java ******** with PID 19424 (E:\fushun\data-synchronization\target\classes started by Dell in E:\fushun\data-synchronization)
2025-06-30 09:34:35.254 [restartedMain] DEBUG c.n.d.DataSynchronizationApplication - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-30 09:34:35.254 [restartedMain] INFO  c.n.d.DataSynchronizationApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-30 09:34:36.580 [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Starting...
2025-06-30 09:34:37.842 [restartedMain] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-2 - Added connection com.mysql.cj.jdbc.ConnectionImpl@aaa7e07
2025-06-30 09:34:37.842 [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Start completed.
2025-06-30 09:34:37.844 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQL8Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-30 09:34:37.844 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000026: MySQL8Dialect has been deprecated; use org.hibernate.dialect.MySQLDialect instead
2025-06-30 09:34:42.802 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-30 09:34:43.955 [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2025-06-30 09:34:43.956 [restartedMain] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-06-30 09:34:43.956 [restartedMain] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
2025-06-30 09:34:43.956 [restartedMain] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-06-30 09:34:43.956 [restartedMain] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-06-30 09:34:43.957 [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-06-30 09:34:43.957 [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
2025-06-30 09:34:43.957 [restartedMain] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7fe22f2b
2025-06-30 09:34:44.487 [restartedMain] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-06-30 09:34:44.498 [restartedMain] INFO  c.n.d.DataSynchronizationApplication - Started DataSynchronizationApplication in 9.33 seconds (process running for 58.578)
2025-06-30 09:34:44.500 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - =====================================================
2025-06-30 09:34:44.500 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 数据同步应用启动成功！
2025-06-30 09:34:44.500 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 应用访问地址: http://192.168.101.1:8080
2025-06-30 09:34:44.500 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - Swagger文档地址: http://192.168.101.1:8080/swagger-ui.html
2025-06-30 09:34:44.500 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - =====================================================
2025-06-30 09:34:44.500 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - SOAP服务调用信息：
2025-06-30 09:34:44.500 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 组织数据服务地址: https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery
2025-06-30 09:34:44.500 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 接口方法: GetOrgInfoFromMDM
2025-06-30 09:34:44.500 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 人员数据服务地址: https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery
2025-06-30 09:34:44.500 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 接口方法: GetUserInfoFromMDM
2025-06-30 09:34:44.500 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - =====================================================
2025-06-30 09:34:44.500 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - HTTP接口调用信息：
2025-06-30 09:34:44.500 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 岗位数据服务地址: http://172.28.98.74:9000/ds/dgc/getQualityPersonnelInfo
2025-06-30 09:34:44.500 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 接口方法: getQualityPersonnelInfo
2025-06-30 09:34:44.500 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - =====================================================
2025-06-30 09:34:44.500 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 定时任务配置信息：
2025-06-30 09:34:44.501 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 数据同步任务执行时间: 0 0 3 * * ? (凌晨3点执行)
2025-06-30 09:34:44.501 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 数据同步范围: 昨天00:00:00至23:59:59的数据
2025-06-30 09:34:44.501 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - =====================================================
2025-06-30 09:34:48.852 [http-nio-8080-exec-3] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 340 ms
2025-06-30 09:36:36.505 [http-nio-8080-exec-8] INFO  c.n.d.controller.SyncController - 开始同步员工数据，开始时间: Thu May 01 00:00:00 CST 2025, 结束时间: Sat May 31 23:59:59 CST 2025
2025-06-30 09:36:36.506 [http-nio-8080-exec-8] DEBUG c.n.d.client.impl.SoapClientImpl - 请求人员数据，参数：开始时间=2025/05/01 00:00:00，结束时间=2025/05/31 23:59:59
2025-06-30 09:36:36.799 [http-nio-8080-exec-8] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求超时设置: 连接超时=30000ms, 读取超时=120000ms
2025-06-30 09:36:36.799 [http-nio-8080-exec-8] DEBUG c.n.d.client.impl.SoapClientImpl - 发送SOAP请求到 https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery, SOAPAction: http://tempuri.org/GetUserInfoFromMDM
2025-06-30 09:37:37.606 [http-nio-8080-exec-8] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应码: 504
2025-06-30 09:37:37.607 [http-nio-8080-exec-8] ERROR c.n.d.client.impl.SoapClientImpl - SOAP请求失败，响应码: 504
2025-06-30 09:37:37.626 [http-nio-8080-exec-8] ERROR c.n.d.client.impl.SoapClientImpl - 错误响应内容: <html><head><title>504 Gateway Time-out</title></head><body><center><h1>504 Gateway Time-out</h1></center><hr><center>nginx</center></body></html>
2025-06-30 09:37:37.626 [http-nio-8080-exec-8] ERROR c.n.d.client.impl.SoapClientImpl - 发送SOAP请求时发生异常: SOAP请求失败，响应码: 504, 错误内容: <html><head><title>504 Gateway Time-out</title></head><body><center><h1>504 Gateway Time-out</h1></center><hr><center>nginx</center></body></html>
java.lang.RuntimeException: SOAP请求失败，响应码: 504, 错误内容: <html><head><title>504 Gateway Time-out</title></head><body><center><h1>504 Gateway Time-out</h1></center><hr><center>nginx</center></body></html>
	at com.nercar.datasynchronization.client.impl.SoapClientImpl.sendSoapRequest(SoapClientImpl.java:202)
	at com.nercar.datasynchronization.client.impl.SoapClientImpl.getUserInfo(SoapClientImpl.java:58)
	at com.nercar.datasynchronization.controller.SyncController.syncEmployees(SyncController.java:92)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:254)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:182)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:833)
2025-06-30 09:37:37.629 [http-nio-8080-exec-8] ERROR c.n.d.controller.SyncController - 同步员工数据失败
java.lang.RuntimeException: SOAP请求失败: SOAP请求失败，响应码: 504, 错误内容: <html><head><title>504 Gateway Time-out</title></head><body><center><h1>504 Gateway Time-out</h1></center><hr><center>nginx</center></body></html>
	at com.nercar.datasynchronization.client.impl.SoapClientImpl.sendSoapRequest(SoapClientImpl.java:211)
	at com.nercar.datasynchronization.client.impl.SoapClientImpl.getUserInfo(SoapClientImpl.java:58)
	at com.nercar.datasynchronization.controller.SyncController.syncEmployees(SyncController.java:92)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:254)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:182)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: java.lang.RuntimeException: SOAP请求失败，响应码: 504, 错误内容: <html><head><title>504 Gateway Time-out</title></head><body><center><h1>504 Gateway Time-out</h1></center><hr><center>nginx</center></body></html>
	at com.nercar.datasynchronization.client.impl.SoapClientImpl.sendSoapRequest(SoapClientImpl.java:202)
	... 56 common frames omitted
2025-06-30 09:38:22.797 [http-nio-8080-exec-9] INFO  c.n.d.controller.SyncController - 开始异步同步员工数据，开始时间: Thu May 01 00:00:00 CST 2025, 结束时间: Sat May 31 23:59:59 CST 2025
2025-06-30 09:38:22.807 [AsyncTask-1] DEBUG c.n.d.client.impl.SoapClientImpl - 请求人员数据，参数：开始时间=2025/05/01 00:00:00，结束时间=2025/05/31 23:59:59
2025-06-30 09:38:22.807 [AsyncTask-1] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求超时设置: 连接超时=30000ms, 读取超时=120000ms
2025-06-30 09:38:22.807 [AsyncTask-1] DEBUG c.n.d.client.impl.SoapClientImpl - 发送SOAP请求到 https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery, SOAPAction: http://tempuri.org/GetUserInfoFromMDM
2025-06-30 09:39:23.082 [AsyncTask-1] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应码: 504
2025-06-30 09:39:23.082 [AsyncTask-1] ERROR c.n.d.client.impl.SoapClientImpl - SOAP请求失败，响应码: 504
2025-06-30 09:39:23.083 [AsyncTask-1] ERROR c.n.d.client.impl.SoapClientImpl - 错误响应内容: <html><head><title>504 Gateway Time-out</title></head><body><center><h1>504 Gateway Time-out</h1></center><hr><center>nginx</center></body></html>
2025-06-30 09:39:23.083 [AsyncTask-1] ERROR c.n.d.client.impl.SoapClientImpl - 发送SOAP请求时发生异常: SOAP请求失败，响应码: 504, 错误内容: <html><head><title>504 Gateway Time-out</title></head><body><center><h1>504 Gateway Time-out</h1></center><hr><center>nginx</center></body></html>
java.lang.RuntimeException: SOAP请求失败，响应码: 504, 错误内容: <html><head><title>504 Gateway Time-out</title></head><body><center><h1>504 Gateway Time-out</h1></center><hr><center>nginx</center></body></html>
	at com.nercar.datasynchronization.client.impl.SoapClientImpl.sendSoapRequest(SoapClientImpl.java:202)
	at com.nercar.datasynchronization.client.impl.SoapClientImpl.getUserInfo(SoapClientImpl.java:58)
	at com.nercar.datasynchronization.service.impl.AsyncSyncService.syncEmployeesAsync(AsyncSyncService.java:66)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.nercar.datasynchronization.config.PerformanceMonitoringAspect.logServiceMethodExecution(PerformanceMonitoringAspect.java:21)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:637)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:627)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at org.springframework.util.concurrent.FutureUtils.lambda$toSupplier$0(FutureUtils.java:74)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
2025-06-30 09:39:23.084 [AsyncTask-1] INFO  c.n.d.c.PerformanceMonitoringAspect - com.nercar.datasynchronization.service.impl.AsyncSyncService#syncEmployeesAsync 执行完成，耗时: 60278ms
2025-06-30 09:40:45.151 [http-nio-8080-exec-1] INFO  c.n.d.controller.SyncController - 开始分片同步员工数据，开始时间: Thu May 01 00:00:00 CST 2025, 结束时间: Sat May 31 23:59:59 CST 2025, 分片天数: 1, 重试次数: 3
2025-06-30 09:40:45.151 [http-nio-8080-exec-1] INFO  c.n.d.controller.SyncController - 总天数: 30, 总分片数: 30
2025-06-30 09:40:45.152 [http-nio-8080-exec-1] INFO  c.n.d.controller.SyncController - 处理分片 1/30: 开始时间=Thu May 01 00:00:00 CST 2025, 结束时间=Fri May 02 00:00:00 CST 2025
2025-06-30 09:40:45.152 [http-nio-8080-exec-1] DEBUG c.n.d.client.impl.SoapClientImpl - 请求人员数据，参数：开始时间=2025/05/01 00:00:00，结束时间=2025/05/02 00:00:00
2025-06-30 09:40:45.153 [http-nio-8080-exec-1] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求超时设置: 连接超时=30000ms, 读取超时=120000ms
2025-06-30 09:40:45.153 [http-nio-8080-exec-1] DEBUG c.n.d.client.impl.SoapClientImpl - 发送SOAP请求到 https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery, SOAPAction: http://tempuri.org/GetUserInfoFromMDM
2025-06-30 09:40:55.338 [http-nio-8080-exec-1] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应码: 200
2025-06-30 09:40:59.008 [http-nio-8080-exec-1] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/env...
2025-06-30 09:40:59.011 [http-nio-8080-exec-1] DEBUG c.n.d.utils.XmlUtils - 开始提取XML数据，SOAP响应(前200字符): <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLS...
2025-06-30 09:40:59.095 [http-nio-8080-exec-1] DEBUG c.n.d.utils.XmlUtils - SOAP命名空间: http://schemas.xmlsoap.org/soap/envelope/
2025-06-30 09:40:59.096 [http-nio-8080-exec-1] DEBUG c.n.d.utils.XmlUtils - 成功获取SOAP Body元素
2025-06-30 09:40:59.098 [http-nio-8080-exec-1] DEBUG c.n.d.utils.XmlUtils - 响应元素名称: GetUserInfoFromMDMResponse
2025-06-30 09:40:59.133 [http-nio-8080-exec-1] DEBUG c.n.d.utils.XmlUtils - 成功提取XML数据(前100字符): <?xml version="1.0" encoding="utf-16"?><O_DATAS>  <O_DATA>    <MDMZGZD_NM>7dd1234d-7618-40d6-9228-c4...
2025-06-30 09:41:00.998 [http-nio-8080-exec-1] INFO  c.n.d.utils.XmlUtils - XML数据解析完成，共处理217条记录
2025-06-30 09:42:29.791 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-30 09:42:34.817 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-06-30 09:42:34.817 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-30 09:42:34.817 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-06-30 09:42:34.819 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Shutdown initiated...
2025-06-30 09:42:34.821 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Shutdown completed.
2025-06-30 09:42:34.824 [http-nio-8080-exec-1] WARN  c.zaxxer.hikari.pool.ProxyConnection - HikariPool-2 - Connection com.mysql.cj.jdbc.ConnectionImpl@aaa7e07 marked as broken because of SQLSTATE(08S01), ErrorCode(0)
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet successfully received from the server was 23 milliseconds ago. The last packet sent successfully to the server was 102 milliseconds ago.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1009)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeLargeUpdate(ClientPreparedStatement.java:1320)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdate(ClientPreparedStatement.java:994)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:280)
	at org.hibernate.id.insert.GetGeneratedKeysDelegate.performInsert(GetGeneratedKeysDelegate.java:107)
	at org.hibernate.engine.jdbc.mutation.internal.MutationExecutorPostInsertSingleTable.execute(MutationExecutorPostInsertSingleTable.java:100)
	at org.hibernate.persister.entity.mutation.InsertCoordinator.doDynamicInserts(InsertCoordinator.java:289)
	at org.hibernate.persister.entity.mutation.InsertCoordinator.coordinateInsert(InsertCoordinator.java:109)
	at org.hibernate.persister.entity.AbstractEntityPersister.insert(AbstractEntityPersister.java:2860)
	at org.hibernate.action.internal.EntityIdentityInsertAction.execute(EntityIdentityInsertAction.java:81)
	at org.hibernate.engine.spi.ActionQueue.execute(ActionQueue.java:667)
	at org.hibernate.engine.spi.ActionQueue.addResolvedEntityInsertAction(ActionQueue.java:290)
	at org.hibernate.engine.spi.ActionQueue.addInsertAction(ActionQueue.java:271)
	at org.hibernate.engine.spi.ActionQueue.addAction(ActionQueue.java:321)
	at org.hibernate.event.internal.AbstractSaveEventListener.addInsertAction(AbstractSaveEventListener.java:386)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSaveOrReplicate(AbstractSaveEventListener.java:300)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSave(AbstractSaveEventListener.java:219)
	at org.hibernate.event.internal.AbstractSaveEventListener.saveWithGeneratedId(AbstractSaveEventListener.java:134)
	at org.hibernate.event.internal.DefaultPersistEventListener.entityIsTransient(DefaultPersistEventListener.java:175)
	at org.hibernate.event.internal.DefaultPersistEventListener.persist(DefaultPersistEventListener.java:93)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:77)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:54)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.firePersist(SessionImpl.java:766)
	at org.hibernate.internal.SessionImpl.persist(SessionImpl.java:750)
	at jdk.internal.reflect.GeneratedMethodAccessor147.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.orm.jpa.ExtendedEntityManagerCreator$ExtendedEntityManagerInvocationHandler.invoke(ExtendedEntityManagerCreator.java:360)
	at jdk.proxy5/jdk.proxy5.$Proxy194.persist(Unknown Source)
	at jdk.internal.reflect.GeneratedMethodAccessor147.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler.invoke(SharedEntityManagerCreator.java:311)
	at jdk.proxy5/jdk.proxy5.$Proxy194.persist(Unknown Source)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.save(SimpleJpaRepository.java:618)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.saveAll(SimpleJpaRepository.java:644)
	at jdk.internal.reflect.GeneratedMethodAccessor150.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:516)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:285)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:628)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:168)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:143)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:164)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:249)
	at jdk.proxy5/jdk.proxy5.$Proxy198.saveAll(Unknown Source)
	at com.nercar.datasynchronization.service.impl.EmployeeSyncServiceImpl.saveTitlesBatch(EmployeeSyncServiceImpl.java:310)
	at com.nercar.datasynchronization.service.impl.EmployeeSyncServiceImpl.syncEmployees(EmployeeSyncServiceImpl.java:184)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.nercar.datasynchronization.config.PerformanceMonitoringAspect.logServiceMethodExecution(PerformanceMonitoringAspect.java:21)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:637)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:627)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.nercar.datasynchronization.service.impl.EmployeeSyncServiceImpl$$SpringCGLIB$$0.syncEmployees(<generated>)
	at com.nercar.datasynchronization.controller.SyncController.syncEmployeesChunked(SyncController.java:221)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:254)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:182)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet successfully received from the server was 23 milliseconds ago. The last packet sent successfully to the server was 102 milliseconds ago.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:582)
	at com.mysql.cj.protocol.a.NativeProtocol.checkErrorMessage(NativeProtocol.java:762)
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:701)
	at com.mysql.cj.protocol.a.NativeProtocol.sendQueryPacket(NativeProtocol.java:1052)
	at com.mysql.cj.NativeSession.execSQL(NativeSession.java:657)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:893)
	... 149 common frames omitted
Caused by: java.net.SocketException: Socket closed
	at java.base/sun.nio.ch.NioSocketImpl.endRead(NioSocketImpl.java:248)
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:327)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:350)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:803)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:966)
	at com.mysql.cj.protocol.ReadAheadInputStream.fill(ReadAheadInputStream.java:107)
	at com.mysql.cj.protocol.ReadAheadInputStream.readFromUnderlyingStreamIfNecessary(ReadAheadInputStream.java:150)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:180)
	at java.base/java.io.FilterInputStream.read(FilterInputStream.java:132)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:576)
	... 154 common frames omitted
2025-06-30 09:42:34.831 [http-nio-8080-exec-1] WARN  o.h.e.jdbc.spi.SqlExceptionHelper - SQL Error: 0, SQLState: 08S01
2025-06-30 09:42:34.831 [http-nio-8080-exec-1] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - Communications link failure

The last packet successfully received from the server was 23 milliseconds ago. The last packet sent successfully to the server was 102 milliseconds ago.
2025-06-30 09:42:34.854 [http-nio-8080-exec-1] INFO  c.n.d.c.PerformanceMonitoringAspect - com.nercar.datasynchronization.service.impl.EmployeeSyncServiceImpl#syncEmployees 执行完成，耗时: 95721ms
2025-06-30 09:42:34.854 [http-nio-8080-exec-1] INFO  c.n.d.controller.SyncController - 分片 1/30 同步成功
2025-06-30 09:42:34.854 [http-nio-8080-exec-1] INFO  c.n.d.controller.SyncController - 处理分片 2/30: 开始时间=Fri May 02 00:00:00 CST 2025, 结束时间=Sat May 03 00:00:00 CST 2025
2025-06-30 09:42:34.854 [http-nio-8080-exec-1] DEBUG c.n.d.client.impl.SoapClientImpl - 请求人员数据，参数：开始时间=2025/05/02 00:00:00，结束时间=2025/05/03 00:00:00
2025-06-30 09:42:34.854 [http-nio-8080-exec-1] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求超时设置: 连接超时=30000ms, 读取超时=120000ms
2025-06-30 09:42:34.854 [http-nio-8080-exec-1] DEBUG c.n.d.client.impl.SoapClientImpl - 发送SOAP请求到 https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery, SOAPAction: http://tempuri.org/GetUserInfoFromMDM
2025-06-30 09:42:36.658 [http-nio-8080-exec-1] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应码: 200
2025-06-30 09:42:45.969 [restartedMain] INFO  c.n.d.DataSynchronizationApplication - Starting DataSynchronizationApplication using Java ******** with PID 6032 (E:\fushun\data-synchronization\target\classes started by Dell in E:\fushun\data-synchronization)
2025-06-30 09:42:45.971 [restartedMain] DEBUG c.n.d.DataSynchronizationApplication - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-30 09:42:45.971 [restartedMain] INFO  c.n.d.DataSynchronizationApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-30 09:42:49.375 [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-30 09:42:50.409 [restartedMain] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@146a3d47
2025-06-30 09:42:50.412 [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-30 09:42:50.539 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQL8Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-30 09:42:50.540 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000026: MySQL8Dialect has been deprecated; use org.hibernate.dialect.MySQLDialect instead
2025-06-30 09:42:56.376 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-30 09:42:57.244 [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2025-06-30 09:42:57.256 [restartedMain] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-06-30 09:42:57.256 [restartedMain] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
2025-06-30 09:42:57.257 [restartedMain] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-06-30 09:42:57.258 [restartedMain] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-06-30 09:42:57.258 [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-06-30 09:42:57.259 [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
2025-06-30 09:42:57.259 [restartedMain] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@959a486
2025-06-30 09:42:57.911 [restartedMain] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-06-30 09:42:57.928 [restartedMain] INFO  c.n.d.DataSynchronizationApplication - Started DataSynchronizationApplication in 12.64 seconds (process running for 13.72)
2025-06-30 09:42:57.930 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - =====================================================
2025-06-30 09:42:57.930 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 数据同步应用启动成功！
2025-06-30 09:42:57.930 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 应用访问地址: http://192.168.101.1:8080
2025-06-30 09:42:57.930 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - Swagger文档地址: http://192.168.101.1:8080/swagger-ui.html
2025-06-30 09:42:57.930 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - =====================================================
2025-06-30 09:42:57.930 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - SOAP服务调用信息：
2025-06-30 09:42:57.930 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 组织数据服务地址: https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery
2025-06-30 09:42:57.930 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 接口方法: GetOrgInfoFromMDM
2025-06-30 09:42:57.930 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 人员数据服务地址: https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery
2025-06-30 09:42:57.930 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 接口方法: GetUserInfoFromMDM
2025-06-30 09:42:57.930 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - =====================================================
2025-06-30 09:42:57.931 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - HTTP接口调用信息：
2025-06-30 09:42:57.931 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 岗位数据服务地址: http://172.28.98.74:9000/ds/dgc/getQualityPersonnelInfo
2025-06-30 09:42:57.931 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 接口方法: getQualityPersonnelInfo
2025-06-30 09:42:57.931 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - =====================================================
2025-06-30 09:42:57.931 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 定时任务配置信息：
2025-06-30 09:42:57.931 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 数据同步任务执行时间: 0 0 3 * * ? (凌晨3点执行)
2025-06-30 09:42:57.931 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 数据同步范围: 昨天00:00:00至23:59:59的数据
2025-06-30 09:42:57.931 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - =====================================================
2025-06-30 09:43:29.193 [http-nio-8080-exec-1] INFO  c.n.d.controller.SyncController - 开始分片同步员工数据，开始时间: Thu May 01 00:00:00 CST 2025, 结束时间: Sat May 31 23:59:59 CST 2025, 分片天数: 1, 重试次数: 3
2025-06-30 09:43:29.194 [http-nio-8080-exec-1] INFO  c.n.d.controller.SyncController - 总天数: 30, 总分片数: 30
2025-06-30 09:43:29.195 [http-nio-8080-exec-1] INFO  c.n.d.controller.SyncController - 处理分片 1/30: 开始时间=Thu May 01 00:00:00 CST 2025, 结束时间=Fri May 02 00:00:00 CST 2025
2025-06-30 09:43:29.195 [http-nio-8080-exec-1] DEBUG c.n.d.client.impl.SoapClientImpl - 请求人员数据，参数：开始时间=2025/05/01 00:00:00，结束时间=2025/05/02 00:00:00
2025-06-30 09:43:29.282 [http-nio-8080-exec-1] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求超时设置: 连接超时=30000ms, 读取超时=120000ms
2025-06-30 09:43:29.282 [http-nio-8080-exec-1] DEBUG c.n.d.client.impl.SoapClientImpl - 发送SOAP请求到 https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery, SOAPAction: http://tempuri.org/GetUserInfoFromMDM
2025-06-30 09:43:39.687 [http-nio-8080-exec-1] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应码: 200
2025-06-30 09:43:42.912 [http-nio-8080-exec-1] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/env...
2025-06-30 09:43:42.914 [http-nio-8080-exec-1] DEBUG c.n.d.utils.XmlUtils - 开始提取XML数据，SOAP响应(前200字符): <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLS...
2025-06-30 09:43:43.000 [http-nio-8080-exec-1] DEBUG c.n.d.utils.XmlUtils - SOAP命名空间: http://schemas.xmlsoap.org/soap/envelope/
2025-06-30 09:43:43.000 [http-nio-8080-exec-1] DEBUG c.n.d.utils.XmlUtils - 成功获取SOAP Body元素
2025-06-30 09:43:43.002 [http-nio-8080-exec-1] DEBUG c.n.d.utils.XmlUtils - 响应元素名称: GetUserInfoFromMDMResponse
2025-06-30 09:43:43.019 [http-nio-8080-exec-1] DEBUG c.n.d.utils.XmlUtils - 成功提取XML数据(前100字符): <?xml version="1.0" encoding="utf-16"?><O_DATAS>  <O_DATA>    <MDMZGZD_NM>7dd1234d-7618-40d6-9228-c4...
2025-06-30 09:43:43.064 [http-nio-8080-exec-1] INFO  c.n.d.utils.XmlUtils - XML数据解析完成，共处理217条记录
2025-06-30 09:44:29.575 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-30 09:44:40.214 [restartedMain] INFO  c.n.d.DataSynchronizationApplication - Starting DataSynchronizationApplication using Java ******** with PID 43748 (E:\fushun\data-synchronization\target\classes started by Dell in E:\fushun\data-synchronization)
2025-06-30 09:44:40.216 [restartedMain] DEBUG c.n.d.DataSynchronizationApplication - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-30 09:44:40.217 [restartedMain] INFO  c.n.d.DataSynchronizationApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-30 09:44:43.143 [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-30 09:44:44.039 [restartedMain] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7277fafd
2025-06-30 09:44:44.047 [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-30 09:44:44.163 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQL8Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-30 09:44:44.164 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000026: MySQL8Dialect has been deprecated; use org.hibernate.dialect.MySQLDialect instead
2025-06-30 09:44:49.588 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-30 09:44:50.459 [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2025-06-30 09:44:50.470 [restartedMain] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-06-30 09:44:50.470 [restartedMain] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
2025-06-30 09:44:50.471 [restartedMain] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-06-30 09:44:50.471 [restartedMain] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-06-30 09:44:50.471 [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-06-30 09:44:50.471 [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
2025-06-30 09:44:50.471 [restartedMain] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@3727ae23
2025-06-30 09:44:51.061 [restartedMain] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-06-30 09:44:51.079 [restartedMain] INFO  c.n.d.DataSynchronizationApplication - Started DataSynchronizationApplication in 11.573 seconds (process running for 12.604)
2025-06-30 09:44:51.080 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - =====================================================
2025-06-30 09:44:51.080 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 数据同步应用启动成功！
2025-06-30 09:44:51.080 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 应用访问地址: http://192.168.101.1:8080
2025-06-30 09:44:51.081 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - Swagger文档地址: http://192.168.101.1:8080/swagger-ui.html
2025-06-30 09:44:51.081 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - =====================================================
2025-06-30 09:44:51.081 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - SOAP服务调用信息：
2025-06-30 09:44:51.081 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 组织数据服务地址: https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery
2025-06-30 09:44:51.081 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 接口方法: GetOrgInfoFromMDM
2025-06-30 09:44:51.081 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 人员数据服务地址: https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery
2025-06-30 09:44:51.081 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 接口方法: GetUserInfoFromMDM
2025-06-30 09:44:51.081 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - =====================================================
2025-06-30 09:44:51.081 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - HTTP接口调用信息：
2025-06-30 09:44:51.081 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 岗位数据服务地址: http://172.28.98.74:9000/ds/dgc/getQualityPersonnelInfo
2025-06-30 09:44:51.082 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 接口方法: getQualityPersonnelInfo
2025-06-30 09:44:51.082 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - =====================================================
2025-06-30 09:44:51.082 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 定时任务配置信息：
2025-06-30 09:44:51.082 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 数据同步任务执行时间: 0 0 3 * * ? (凌晨3点执行)
2025-06-30 09:44:51.082 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 数据同步范围: 昨天00:00:00至23:59:59的数据
2025-06-30 09:44:51.082 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - =====================================================
2025-06-30 09:45:01.967 [http-nio-8080-exec-1] INFO  c.n.d.controller.SyncController - 开始同步员工数据，开始时间: Thu May 01 00:00:00 CST 2025, 结束时间: Sat May 31 23:59:59 CST 2025
2025-06-30 09:45:01.968 [http-nio-8080-exec-1] DEBUG c.n.d.client.impl.SoapClientImpl - 请求人员数据，参数：开始时间=2025/05/01 00:00:00，结束时间=2025/05/31 23:59:59
2025-06-30 09:45:02.053 [http-nio-8080-exec-1] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求超时设置: 连接超时=30000ms, 读取超时=120000ms
2025-06-30 09:45:02.054 [http-nio-8080-exec-1] DEBUG c.n.d.client.impl.SoapClientImpl - 发送SOAP请求到 https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery, SOAPAction: http://tempuri.org/GetUserInfoFromMDM
2025-06-30 09:46:02.536 [http-nio-8080-exec-1] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应码: 504
2025-06-30 09:46:02.536 [http-nio-8080-exec-1] ERROR c.n.d.client.impl.SoapClientImpl - SOAP请求失败，响应码: 504
2025-06-30 09:46:02.538 [http-nio-8080-exec-1] ERROR c.n.d.client.impl.SoapClientImpl - 错误响应内容: <html><head><title>504 Gateway Time-out</title></head><body><center><h1>504 Gateway Time-out</h1></center><hr><center>nginx</center></body></html>
2025-06-30 09:46:02.539 [http-nio-8080-exec-1] ERROR c.n.d.client.impl.SoapClientImpl - 发送SOAP请求时发生异常: SOAP请求失败，响应码: 504, 错误内容: <html><head><title>504 Gateway Time-out</title></head><body><center><h1>504 Gateway Time-out</h1></center><hr><center>nginx</center></body></html>
java.lang.RuntimeException: SOAP请求失败，响应码: 504, 错误内容: <html><head><title>504 Gateway Time-out</title></head><body><center><h1>504 Gateway Time-out</h1></center><hr><center>nginx</center></body></html>
	at com.nercar.datasynchronization.client.impl.SoapClientImpl.sendSoapRequest(SoapClientImpl.java:202)
	at com.nercar.datasynchronization.client.impl.SoapClientImpl.getUserInfo(SoapClientImpl.java:58)
	at com.nercar.datasynchronization.controller.SyncController.syncEmployees(SyncController.java:92)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:254)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:182)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:833)
2025-06-30 09:46:02.551 [http-nio-8080-exec-1] ERROR c.n.d.controller.SyncController - 同步员工数据失败
java.lang.RuntimeException: SOAP请求失败: SOAP请求失败，响应码: 504, 错误内容: <html><head><title>504 Gateway Time-out</title></head><body><center><h1>504 Gateway Time-out</h1></center><hr><center>nginx</center></body></html>
	at com.nercar.datasynchronization.client.impl.SoapClientImpl.sendSoapRequest(SoapClientImpl.java:211)
	at com.nercar.datasynchronization.client.impl.SoapClientImpl.getUserInfo(SoapClientImpl.java:58)
	at com.nercar.datasynchronization.controller.SyncController.syncEmployees(SyncController.java:92)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:254)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:182)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: java.lang.RuntimeException: SOAP请求失败，响应码: 504, 错误内容: <html><head><title>504 Gateway Time-out</title></head><body><center><h1>504 Gateway Time-out</h1></center><hr><center>nginx</center></body></html>
	at com.nercar.datasynchronization.client.impl.SoapClientImpl.sendSoapRequest(SoapClientImpl.java:202)
	... 56 common frames omitted
2025-06-30 09:46:06.375 [Thread-5] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-30 09:46:06.436 [Thread-5] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-06-30 09:46:06.437 [Thread-5] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-30 09:46:06.437 [Thread-5] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-06-30 09:46:06.449 [Thread-5] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-30 09:46:06.468 [Thread-5] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-30 09:46:20.980 [restartedMain] INFO  c.n.d.DataSynchronizationApplication - Starting DataSynchronizationApplication using Java ******** with PID 43748 (E:\fushun\data-synchronization\target\classes started by Dell in E:\fushun\data-synchronization)
2025-06-30 09:46:20.981 [restartedMain] DEBUG c.n.d.DataSynchronizationApplication - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-30 09:46:20.981 [restartedMain] INFO  c.n.d.DataSynchronizationApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-30 09:46:28.082 [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Starting...
2025-06-30 09:46:28.934 [restartedMain] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-2 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6c0119ff
2025-06-30 09:46:28.935 [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Start completed.
2025-06-30 09:46:28.936 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQL8Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-30 09:46:28.936 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000026: MySQL8Dialect has been deprecated; use org.hibernate.dialect.MySQLDialect instead
2025-06-30 09:46:35.738 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-30 09:46:36.966 [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2025-06-30 09:46:36.968 [restartedMain] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-06-30 09:46:36.968 [restartedMain] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
2025-06-30 09:46:36.968 [restartedMain] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-06-30 09:46:36.968 [restartedMain] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-06-30 09:46:36.968 [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-06-30 09:46:36.968 [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
2025-06-30 09:46:36.968 [restartedMain] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@56358628
2025-06-30 09:46:37.850 [restartedMain] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-06-30 09:46:37.866 [restartedMain] INFO  c.n.d.DataSynchronizationApplication - Started DataSynchronizationApplication in 17.232 seconds (process running for 119.392)
2025-06-30 09:46:37.867 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - =====================================================
2025-06-30 09:46:37.868 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 数据同步应用启动成功！
2025-06-30 09:46:37.868 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 应用访问地址: http://192.168.101.1:8080
2025-06-30 09:46:37.868 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - Swagger文档地址: http://192.168.101.1:8080/swagger-ui.html
2025-06-30 09:46:37.868 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - =====================================================
2025-06-30 09:46:37.869 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - SOAP服务调用信息：
2025-06-30 09:46:37.869 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 组织数据服务地址: https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery
2025-06-30 09:46:37.869 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 接口方法: GetOrgInfoFromMDM
2025-06-30 09:46:37.870 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 人员数据服务地址: https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery
2025-06-30 09:46:37.870 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 接口方法: GetUserInfoFromMDM
2025-06-30 09:46:37.870 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - =====================================================
2025-06-30 09:46:37.870 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - HTTP接口调用信息：
2025-06-30 09:46:37.870 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 岗位数据服务地址: http://172.28.98.74:9000/ds/dgc/getQualityPersonnelInfo
2025-06-30 09:46:37.870 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 接口方法: getQualityPersonnelInfo
2025-06-30 09:46:37.870 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - =====================================================
2025-06-30 09:46:37.870 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 定时任务配置信息：
2025-06-30 09:46:37.870 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 数据同步任务执行时间: 0 0 3 * * ? (凌晨3点执行)
2025-06-30 09:46:37.870 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - 数据同步范围: 昨天00:00:00至23:59:59的数据
2025-06-30 09:46:37.871 [restartedMain] INFO  c.n.d.l.ApplicationStartupListener - =====================================================
2025-06-30 09:46:57.484 [http-nio-8080-exec-1] INFO  c.n.d.controller.SyncController - 开始同步员工数据，开始时间: Thu May 01 00:00:00 CST 2025, 结束时间: Sat May 31 23:59:59 CST 2025
2025-06-30 09:46:57.486 [http-nio-8080-exec-1] DEBUG c.n.d.client.impl.SoapClientImpl - 请求人员数据，参数：开始时间=2025/05/01 00:00:00，结束时间=2025/05/31 23:59:59
2025-06-30 09:46:57.487 [http-nio-8080-exec-1] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求超时设置: 连接超时=30000ms, 读取超时=120000ms
2025-06-30 09:46:57.487 [http-nio-8080-exec-1] DEBUG c.n.d.client.impl.SoapClientImpl - 发送SOAP请求到 https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery, SOAPAction: http://tempuri.org/GetUserInfoFromMDM
2025-06-30 09:47:57.917 [http-nio-8080-exec-1] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应码: 504
2025-06-30 09:47:57.918 [http-nio-8080-exec-1] ERROR c.n.d.client.impl.SoapClientImpl - SOAP请求失败，响应码: 504
2025-06-30 09:47:57.918 [http-nio-8080-exec-1] ERROR c.n.d.client.impl.SoapClientImpl - 错误响应内容: <html><head><title>504 Gateway Time-out</title></head><body><center><h1>504 Gateway Time-out</h1></center><hr><center>nginx</center></body></html>
2025-06-30 09:47:57.919 [http-nio-8080-exec-1] ERROR c.n.d.client.impl.SoapClientImpl - 发送SOAP请求时发生异常: SOAP请求失败，响应码: 504, 错误内容: <html><head><title>504 Gateway Time-out</title></head><body><center><h1>504 Gateway Time-out</h1></center><hr><center>nginx</center></body></html>
java.lang.RuntimeException: SOAP请求失败，响应码: 504, 错误内容: <html><head><title>504 Gateway Time-out</title></head><body><center><h1>504 Gateway Time-out</h1></center><hr><center>nginx</center></body></html>
	at com.nercar.datasynchronization.client.impl.SoapClientImpl.sendSoapRequest(SoapClientImpl.java:202)
	at com.nercar.datasynchronization.client.impl.SoapClientImpl.getUserInfo(SoapClientImpl.java:58)
	at com.nercar.datasynchronization.controller.SyncController.syncEmployees(SyncController.java:92)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:254)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:182)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:833)
2025-06-30 09:47:57.920 [http-nio-8080-exec-1] ERROR c.n.d.controller.SyncController - 同步员工数据失败
java.lang.RuntimeException: SOAP请求失败: SOAP请求失败，响应码: 504, 错误内容: <html><head><title>504 Gateway Time-out</title></head><body><center><h1>504 Gateway Time-out</h1></center><hr><center>nginx</center></body></html>
	at com.nercar.datasynchronization.client.impl.SoapClientImpl.sendSoapRequest(SoapClientImpl.java:211)
	at com.nercar.datasynchronization.client.impl.SoapClientImpl.getUserInfo(SoapClientImpl.java:58)
	at com.nercar.datasynchronization.controller.SyncController.syncEmployees(SyncController.java:92)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:254)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:182)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: java.lang.RuntimeException: SOAP请求失败，响应码: 504, 错误内容: <html><head><title>504 Gateway Time-out</title></head><body><center><h1>504 Gateway Time-out</h1></center><hr><center>nginx</center></body></html>
	at com.nercar.datasynchronization.client.impl.SoapClientImpl.sendSoapRequest(SoapClientImpl.java:202)
	... 56 common frames omitted
2025-06-30 09:49:56.631 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 开始分片同步员工数据，开始时间: Thu May 01 00:00:00 CST 2025, 结束时间: Sat May 31 23:59:59 CST 2025, 分片天数: 1, 重试次数: 3
2025-06-30 09:49:56.632 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 总天数: 30, 总分片数: 30
2025-06-30 09:49:56.632 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 处理分片 1/30: 开始时间=Thu May 01 00:00:00 CST 2025, 结束时间=Fri May 02 00:00:00 CST 2025
2025-06-30 09:49:56.633 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 请求人员数据，参数：开始时间=2025/05/01 00:00:00，结束时间=2025/05/02 00:00:00
2025-06-30 09:49:56.633 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求超时设置: 连接超时=30000ms, 读取超时=120000ms
2025-06-30 09:49:56.633 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 发送SOAP请求到 https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery, SOAPAction: http://tempuri.org/GetUserInfoFromMDM
2025-06-30 09:50:07.052 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应码: 200
2025-06-30 09:50:11.290 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/env...
2025-06-30 09:50:11.292 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 开始提取XML数据，SOAP响应(前200字符): <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLS...
2025-06-30 09:50:11.388 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - SOAP命名空间: http://schemas.xmlsoap.org/soap/envelope/
2025-06-30 09:50:11.389 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功获取SOAP Body元素
2025-06-30 09:50:11.390 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 响应元素名称: GetUserInfoFromMDMResponse
2025-06-30 09:50:11.407 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功提取XML数据(前100字符): <?xml version="1.0" encoding="utf-16"?><O_DATAS>  <O_DATA>    <MDMZGZD_NM>7dd1234d-7618-40d6-9228-c4...
2025-06-30 09:50:13.629 [http-nio-8080-exec-5] INFO  c.n.d.utils.XmlUtils - XML数据解析完成，共处理217条记录
2025-06-30 09:52:11.225 [http-nio-8080-exec-5] INFO  c.n.d.c.PerformanceMonitoringAspect - com.nercar.datasynchronization.service.impl.EmployeeSyncServiceImpl#syncEmployees 执行完成，耗时: 119815ms
2025-06-30 09:52:11.225 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 分片 1/30 同步成功
2025-06-30 09:52:11.225 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 处理分片 2/30: 开始时间=Fri May 02 00:00:00 CST 2025, 结束时间=Sat May 03 00:00:00 CST 2025
2025-06-30 09:52:11.225 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 请求人员数据，参数：开始时间=2025/05/02 00:00:00，结束时间=2025/05/03 00:00:00
2025-06-30 09:52:11.225 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求超时设置: 连接超时=30000ms, 读取超时=120000ms
2025-06-30 09:52:11.225 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 发送SOAP请求到 https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery, SOAPAction: http://tempuri.org/GetUserInfoFromMDM
2025-06-30 09:52:12.941 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应码: 200
2025-06-30 09:52:13.637 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/env...
2025-06-30 09:52:13.638 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 开始提取XML数据，SOAP响应(前200字符): <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLS...
2025-06-30 09:52:13.646 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - SOAP命名空间: http://schemas.xmlsoap.org/soap/envelope/
2025-06-30 09:52:13.646 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功获取SOAP Body元素
2025-06-30 09:52:13.646 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 响应元素名称: GetUserInfoFromMDMResponse
2025-06-30 09:52:13.648 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功提取XML数据(前100字符): <?xml version="1.0" encoding="utf-16"?><O_DATAS>  <O_DATA>    <MDMZGZD_NM>8e9408d4-56c7-4bc0-9090-5c...
2025-06-30 09:52:13.655 [http-nio-8080-exec-5] INFO  c.n.d.utils.XmlUtils - XML数据解析完成，共处理26条记录
2025-06-30 09:52:31.173 [http-nio-8080-exec-5] INFO  c.n.d.c.PerformanceMonitoringAspect - com.nercar.datasynchronization.service.impl.EmployeeSyncServiceImpl#syncEmployees 执行完成，耗时: 17525ms
2025-06-30 09:52:31.173 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 分片 2/30 同步成功
2025-06-30 09:52:31.173 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 处理分片 3/30: 开始时间=Sat May 03 00:00:00 CST 2025, 结束时间=Sun May 04 00:00:00 CST 2025
2025-06-30 09:52:31.173 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 请求人员数据，参数：开始时间=2025/05/03 00:00:00，结束时间=2025/05/04 00:00:00
2025-06-30 09:52:31.173 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求超时设置: 连接超时=30000ms, 读取超时=120000ms
2025-06-30 09:52:31.173 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 发送SOAP请求到 https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery, SOAPAction: http://tempuri.org/GetUserInfoFromMDM
2025-06-30 09:52:31.764 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应码: 200
2025-06-30 09:52:31.887 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/env...
2025-06-30 09:52:31.887 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 开始提取XML数据，SOAP响应(前200字符): <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLS...
2025-06-30 09:52:31.889 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - SOAP命名空间: http://schemas.xmlsoap.org/soap/envelope/
2025-06-30 09:52:31.889 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功获取SOAP Body元素
2025-06-30 09:52:31.889 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 响应元素名称: GetUserInfoFromMDMResponse
2025-06-30 09:52:31.889 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功提取XML数据(前100字符): <?xml version="1.0" encoding="utf-16"?><O_DATAS>  <O_DATA>    <MDMZGZD_NM>471e025a-7e50-453a-b1ff-7b...
2025-06-30 09:52:31.891 [http-nio-8080-exec-5] INFO  c.n.d.utils.XmlUtils - XML数据解析完成，共处理4条记录
2025-06-30 09:52:35.278 [http-nio-8080-exec-5] INFO  c.n.d.c.PerformanceMonitoringAspect - com.nercar.datasynchronization.service.impl.EmployeeSyncServiceImpl#syncEmployees 执行完成，耗时: 3389ms
2025-06-30 09:52:35.278 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 分片 3/30 同步成功
2025-06-30 09:52:35.278 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 处理分片 4/30: 开始时间=Sun May 04 00:00:00 CST 2025, 结束时间=Mon May 05 00:00:00 CST 2025
2025-06-30 09:52:35.278 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 请求人员数据，参数：开始时间=2025/05/04 00:00:00，结束时间=2025/05/05 00:00:00
2025-06-30 09:52:35.278 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求超时设置: 连接超时=30000ms, 读取超时=120000ms
2025-06-30 09:52:35.278 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 发送SOAP请求到 https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery, SOAPAction: http://tempuri.org/GetUserInfoFromMDM
2025-06-30 09:52:35.625 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应码: 200
2025-06-30 09:52:35.757 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/env...
2025-06-30 09:52:35.757 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 开始提取XML数据，SOAP响应(前200字符): <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLS...
2025-06-30 09:52:35.759 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - SOAP命名空间: http://schemas.xmlsoap.org/soap/envelope/
2025-06-30 09:52:35.759 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功获取SOAP Body元素
2025-06-30 09:52:35.759 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 响应元素名称: GetUserInfoFromMDMResponse
2025-06-30 09:52:35.760 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功提取XML数据(前100字符): <?xml version="1.0" encoding="utf-16"?><O_DATAS>  <O_DATA>    <MDMZGZD_NM>9bc78ee4-36c7-4279-bfb5-e1...
2025-06-30 09:52:35.761 [http-nio-8080-exec-5] INFO  c.n.d.utils.XmlUtils - XML数据解析完成，共处理2条记录
2025-06-30 09:52:38.063 [http-nio-8080-exec-5] INFO  c.n.d.c.PerformanceMonitoringAspect - com.nercar.datasynchronization.service.impl.EmployeeSyncServiceImpl#syncEmployees 执行完成，耗时: 2303ms
2025-06-30 09:52:38.063 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 分片 4/30 同步成功
2025-06-30 09:52:38.063 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 处理分片 5/30: 开始时间=Mon May 05 00:00:00 CST 2025, 结束时间=Tue May 06 00:00:00 CST 2025
2025-06-30 09:52:38.063 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 请求人员数据，参数：开始时间=2025/05/05 00:00:00，结束时间=2025/05/06 00:00:00
2025-06-30 09:52:38.063 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求超时设置: 连接超时=30000ms, 读取超时=120000ms
2025-06-30 09:52:38.063 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 发送SOAP请求到 https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery, SOAPAction: http://tempuri.org/GetUserInfoFromMDM
2025-06-30 09:52:38.625 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应码: 200
2025-06-30 09:52:38.896 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/env...
2025-06-30 09:52:38.897 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 开始提取XML数据，SOAP响应(前200字符): <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLS...
2025-06-30 09:52:38.900 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - SOAP命名空间: http://schemas.xmlsoap.org/soap/envelope/
2025-06-30 09:52:38.900 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功获取SOAP Body元素
2025-06-30 09:52:38.900 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 响应元素名称: GetUserInfoFromMDMResponse
2025-06-30 09:52:38.900 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功提取XML数据(前100字符): <?xml version="1.0" encoding="utf-16"?><O_DATAS>  <O_DATA>    <MDMZGZD_NM>ad3e181e-79a5-48bd-9eb8-cb...
2025-06-30 09:52:38.902 [http-nio-8080-exec-5] INFO  c.n.d.utils.XmlUtils - XML数据解析完成，共处理8条记录
2025-06-30 09:52:44.944 [http-nio-8080-exec-5] INFO  c.n.d.c.PerformanceMonitoringAspect - com.nercar.datasynchronization.service.impl.EmployeeSyncServiceImpl#syncEmployees 执行完成，耗时: 6044ms
2025-06-30 09:52:44.944 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 分片 5/30 同步成功
2025-06-30 09:52:44.944 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 处理分片 6/30: 开始时间=Tue May 06 00:00:00 CST 2025, 结束时间=Wed May 07 00:00:00 CST 2025
2025-06-30 09:52:44.944 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 请求人员数据，参数：开始时间=2025/05/06 00:00:00，结束时间=2025/05/07 00:00:00
2025-06-30 09:52:44.944 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求超时设置: 连接超时=30000ms, 读取超时=120000ms
2025-06-30 09:52:44.944 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 发送SOAP请求到 https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery, SOAPAction: http://tempuri.org/GetUserInfoFromMDM
2025-06-30 09:52:46.369 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应码: 200
2025-06-30 09:52:46.962 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/env...
2025-06-30 09:52:46.962 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 开始提取XML数据，SOAP响应(前200字符): <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLS...
2025-06-30 09:52:46.977 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - SOAP命名空间: http://schemas.xmlsoap.org/soap/envelope/
2025-06-30 09:52:46.977 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功获取SOAP Body元素
2025-06-30 09:52:46.977 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 响应元素名称: GetUserInfoFromMDMResponse
2025-06-30 09:52:46.979 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功提取XML数据(前100字符): <?xml version="1.0" encoding="utf-16"?><O_DATAS>  <O_DATA>    <MDMZGZD_NM>bd842997-879d-40cc-a78c-16...
2025-06-30 09:52:46.984 [http-nio-8080-exec-5] INFO  c.n.d.utils.XmlUtils - XML数据解析完成，共处理23条记录
2025-06-30 09:53:03.181 [http-nio-8080-exec-5] INFO  c.n.d.c.PerformanceMonitoringAspect - com.nercar.datasynchronization.service.impl.EmployeeSyncServiceImpl#syncEmployees 执行完成，耗时: 16202ms
2025-06-30 09:53:03.181 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 分片 6/30 同步成功
2025-06-30 09:53:03.181 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 处理分片 7/30: 开始时间=Wed May 07 00:00:00 CST 2025, 结束时间=Thu May 08 00:00:00 CST 2025
2025-06-30 09:53:03.181 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 请求人员数据，参数：开始时间=2025/05/07 00:00:00，结束时间=2025/05/08 00:00:00
2025-06-30 09:53:03.182 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求超时设置: 连接超时=30000ms, 读取超时=120000ms
2025-06-30 09:53:03.182 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 发送SOAP请求到 https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery, SOAPAction: http://tempuri.org/GetUserInfoFromMDM
2025-06-30 09:53:04.043 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应码: 200
2025-06-30 09:53:04.304 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/env...
2025-06-30 09:53:04.305 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 开始提取XML数据，SOAP响应(前200字符): <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLS...
2025-06-30 09:53:04.308 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - SOAP命名空间: http://schemas.xmlsoap.org/soap/envelope/
2025-06-30 09:53:04.308 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功获取SOAP Body元素
2025-06-30 09:53:04.308 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 响应元素名称: GetUserInfoFromMDMResponse
2025-06-30 09:53:04.310 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功提取XML数据(前100字符): <?xml version="1.0" encoding="utf-16"?><O_DATAS>  <O_DATA>    <MDMZGZD_NM>2c13f5f0-c70a-470b-8279-dc...
2025-06-30 09:53:04.315 [http-nio-8080-exec-5] INFO  c.n.d.utils.XmlUtils - XML数据解析完成，共处理9条记录
2025-06-30 09:53:11.401 [http-nio-8080-exec-5] INFO  c.n.d.c.PerformanceMonitoringAspect - com.nercar.datasynchronization.service.impl.EmployeeSyncServiceImpl#syncEmployees 执行完成，耗时: 7091ms
2025-06-30 09:53:11.401 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 分片 7/30 同步成功
2025-06-30 09:53:11.401 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 处理分片 8/30: 开始时间=Thu May 08 00:00:00 CST 2025, 结束时间=Fri May 09 00:00:00 CST 2025
2025-06-30 09:53:11.401 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 请求人员数据，参数：开始时间=2025/05/08 00:00:00，结束时间=2025/05/09 00:00:00
2025-06-30 09:53:11.402 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求超时设置: 连接超时=30000ms, 读取超时=120000ms
2025-06-30 09:53:11.402 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 发送SOAP请求到 https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery, SOAPAction: http://tempuri.org/GetUserInfoFromMDM
2025-06-30 09:53:11.495 [HikariPool-2 housekeeper] WARN  c.zaxxer.hikari.pool.ProxyLeakTask - Connection leak detection triggered for com.mysql.cj.jdbc.ConnectionImpl@6c0119ff on thread http-nio-8080-exec-5, stack trace follows
java.lang.Exception: Apparent connection leak detected
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:128)
	at org.hibernate.engine.jdbc.connections.internal.DatasourceConnectionProviderImpl.getConnection(DatasourceConnectionProviderImpl.java:122)
	at org.hibernate.internal.NonContextualJdbcConnectionAccess.obtainConnection(NonContextualJdbcConnectionAccess.java:38)
	at org.hibernate.resource.jdbc.internal.LogicalConnectionManagedImpl.acquireConnectionIfNeeded(LogicalConnectionManagedImpl.java:113)
	at org.hibernate.resource.jdbc.internal.LogicalConnectionManagedImpl.getPhysicalConnection(LogicalConnectionManagedImpl.java:143)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.beginTransaction(HibernateJpaDialect.java:164)
	at org.springframework.orm.jpa.JpaTransactionManager.doBegin(JpaTransactionManager.java:420)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.startTransaction(AbstractPlatformTransactionManager.java:531)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.getTransaction(AbstractPlatformTransactionManager.java:405)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.createTransactionIfNecessary(TransactionAspectSupport.java:610)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:379)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:164)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:249)
	at jdk.proxy5/jdk.proxy5.$Proxy196.findAll(Unknown Source)
	at com.nercar.datasynchronization.service.impl.EmployeeSyncServiceImpl.preloadEmployeeCache(EmployeeSyncServiceImpl.java:88)
	at com.nercar.datasynchronization.service.impl.EmployeeSyncServiceImpl.syncEmployees(EmployeeSyncServiceImpl.java:145)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.nercar.datasynchronization.config.PerformanceMonitoringAspect.logServiceMethodExecution(PerformanceMonitoringAspect.java:21)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:637)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:627)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.nercar.datasynchronization.service.impl.EmployeeSyncServiceImpl$$SpringCGLIB$$0.syncEmployees(<generated>)
	at com.nercar.datasynchronization.controller.SyncController.syncEmployeesChunked(SyncController.java:221)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:254)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:182)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:833)
2025-06-30 09:53:12.686 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应码: 200
2025-06-30 09:53:13.339 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/env...
2025-06-30 09:53:13.339 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 开始提取XML数据，SOAP响应(前200字符): <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLS...
2025-06-30 09:53:13.348 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - SOAP命名空间: http://schemas.xmlsoap.org/soap/envelope/
2025-06-30 09:53:13.348 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功获取SOAP Body元素
2025-06-30 09:53:13.348 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 响应元素名称: GetUserInfoFromMDMResponse
2025-06-30 09:53:13.350 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功提取XML数据(前100字符): <?xml version="1.0" encoding="utf-16"?><O_DATAS>  <O_DATA>    <MDMZGZD_NM>982caa8d-e590-4f26-a877-54...
2025-06-30 09:53:13.356 [http-nio-8080-exec-5] INFO  c.n.d.utils.XmlUtils - XML数据解析完成，共处理20条记录
2025-06-30 09:53:33.795 [http-nio-8080-exec-5] INFO  c.n.d.c.PerformanceMonitoringAspect - com.nercar.datasynchronization.service.impl.EmployeeSyncServiceImpl#syncEmployees 执行完成，耗时: 20445ms
2025-06-30 09:53:33.795 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 分片 8/30 同步成功
2025-06-30 09:53:33.795 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 处理分片 9/30: 开始时间=Fri May 09 00:00:00 CST 2025, 结束时间=Sat May 10 00:00:00 CST 2025
2025-06-30 09:53:33.795 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 请求人员数据，参数：开始时间=2025/05/09 00:00:00，结束时间=2025/05/10 00:00:00
2025-06-30 09:53:33.795 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求超时设置: 连接超时=30000ms, 读取超时=120000ms
2025-06-30 09:53:33.795 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 发送SOAP请求到 https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery, SOAPAction: http://tempuri.org/GetUserInfoFromMDM
2025-06-30 09:53:35.982 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应码: 200
2025-06-30 09:53:36.316 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/env...
2025-06-30 09:53:36.316 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 开始提取XML数据，SOAP响应(前200字符): <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLS...
2025-06-30 09:53:36.320 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - SOAP命名空间: http://schemas.xmlsoap.org/soap/envelope/
2025-06-30 09:53:36.320 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功获取SOAP Body元素
2025-06-30 09:53:36.320 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 响应元素名称: GetUserInfoFromMDMResponse
2025-06-30 09:53:36.321 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功提取XML数据(前100字符): <?xml version="1.0" encoding="utf-16"?><O_DATAS>  <O_DATA>    <MDMZGZD_NM>88395861-fe6a-4714-bb29-ee...
2025-06-30 09:53:36.323 [http-nio-8080-exec-5] INFO  c.n.d.utils.XmlUtils - XML数据解析完成，共处理11条记录
2025-06-30 09:53:44.396 [http-nio-8080-exec-5] INFO  c.n.d.c.PerformanceMonitoringAspect - com.nercar.datasynchronization.service.impl.EmployeeSyncServiceImpl#syncEmployees 执行完成，耗时: 8075ms
2025-06-30 09:53:44.396 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 分片 9/30 同步成功
2025-06-30 09:53:44.396 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 处理分片 10/30: 开始时间=Sat May 10 00:00:00 CST 2025, 结束时间=Sun May 11 00:00:00 CST 2025
2025-06-30 09:53:44.396 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 请求人员数据，参数：开始时间=2025/05/10 00:00:00，结束时间=2025/05/11 00:00:00
2025-06-30 09:53:44.396 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求超时设置: 连接超时=30000ms, 读取超时=120000ms
2025-06-30 09:53:44.396 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 发送SOAP请求到 https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery, SOAPAction: http://tempuri.org/GetUserInfoFromMDM
2025-06-30 09:53:45.376 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应码: 200
2025-06-30 09:53:45.664 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/env...
2025-06-30 09:53:45.664 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 开始提取XML数据，SOAP响应(前200字符): <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLS...
2025-06-30 09:53:45.668 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - SOAP命名空间: http://schemas.xmlsoap.org/soap/envelope/
2025-06-30 09:53:45.668 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功获取SOAP Body元素
2025-06-30 09:53:45.668 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 响应元素名称: GetUserInfoFromMDMResponse
2025-06-30 09:53:45.669 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功提取XML数据(前100字符): <?xml version="1.0" encoding="utf-16"?><O_DATAS>  <O_DATA>    <MDMZGZD_NM>51118cd9-a7c1-4239-83ba-2c...
2025-06-30 09:53:45.671 [http-nio-8080-exec-5] INFO  c.n.d.utils.XmlUtils - XML数据解析完成，共处理8条记录
2025-06-30 09:53:52.612 [http-nio-8080-exec-5] INFO  c.n.d.c.PerformanceMonitoringAspect - com.nercar.datasynchronization.service.impl.EmployeeSyncServiceImpl#syncEmployees 执行完成，耗时: 6943ms
2025-06-30 09:53:52.612 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 分片 10/30 同步成功
2025-06-30 09:53:52.612 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 处理分片 11/30: 开始时间=Sun May 11 00:00:00 CST 2025, 结束时间=Mon May 12 00:00:00 CST 2025
2025-06-30 09:53:52.612 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 请求人员数据，参数：开始时间=2025/05/11 00:00:00，结束时间=2025/05/12 00:00:00
2025-06-30 09:53:52.612 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求超时设置: 连接超时=30000ms, 读取超时=120000ms
2025-06-30 09:53:52.612 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 发送SOAP请求到 https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery, SOAPAction: http://tempuri.org/GetUserInfoFromMDM
2025-06-30 09:53:53.291 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应码: 200
2025-06-30 09:53:53.532 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/env...
2025-06-30 09:53:53.532 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 开始提取XML数据，SOAP响应(前200字符): <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLS...
2025-06-30 09:53:53.536 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - SOAP命名空间: http://schemas.xmlsoap.org/soap/envelope/
2025-06-30 09:53:53.536 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功获取SOAP Body元素
2025-06-30 09:53:53.536 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 响应元素名称: GetUserInfoFromMDMResponse
2025-06-30 09:53:53.536 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功提取XML数据(前100字符): <?xml version="1.0" encoding="utf-16"?><O_DATAS>  <O_DATA>    <MDMZGZD_NM>f65b712a-a464-4592-bea7-99...
2025-06-30 09:53:53.540 [http-nio-8080-exec-5] INFO  c.n.d.utils.XmlUtils - XML数据解析完成，共处理5条记录
2025-06-30 09:53:58.955 [http-nio-8080-exec-5] INFO  c.n.d.c.PerformanceMonitoringAspect - com.nercar.datasynchronization.service.impl.EmployeeSyncServiceImpl#syncEmployees 执行完成，耗时: 5419ms
2025-06-30 09:53:58.955 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 分片 11/30 同步成功
2025-06-30 09:53:58.955 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 处理分片 12/30: 开始时间=Mon May 12 00:00:00 CST 2025, 结束时间=Tue May 13 00:00:00 CST 2025
2025-06-30 09:53:58.955 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 请求人员数据，参数：开始时间=2025/05/12 00:00:00，结束时间=2025/05/13 00:00:00
2025-06-30 09:53:58.955 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求超时设置: 连接超时=30000ms, 读取超时=120000ms
2025-06-30 09:53:58.955 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 发送SOAP请求到 https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery, SOAPAction: http://tempuri.org/GetUserInfoFromMDM
2025-06-30 09:53:59.807 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应码: 200
2025-06-30 09:54:00.057 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/env...
2025-06-30 09:54:00.057 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 开始提取XML数据，SOAP响应(前200字符): <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLS...
2025-06-30 09:54:00.060 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - SOAP命名空间: http://schemas.xmlsoap.org/soap/envelope/
2025-06-30 09:54:00.060 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功获取SOAP Body元素
2025-06-30 09:54:00.060 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 响应元素名称: GetUserInfoFromMDMResponse
2025-06-30 09:54:00.061 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功提取XML数据(前100字符): <?xml version="1.0" encoding="utf-16"?><O_DATAS>  <O_DATA>    <MDMZGZD_NM>146343dd-2006-4098-8c65-12...
2025-06-30 09:54:00.064 [http-nio-8080-exec-5] INFO  c.n.d.utils.XmlUtils - XML数据解析完成，共处理7条记录
2025-06-30 09:54:06.277 [http-nio-8080-exec-5] INFO  c.n.d.c.PerformanceMonitoringAspect - com.nercar.datasynchronization.service.impl.EmployeeSyncServiceImpl#syncEmployees 执行完成，耗时: 6216ms
2025-06-30 09:54:06.277 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 分片 12/30 同步成功
2025-06-30 09:54:06.277 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 处理分片 13/30: 开始时间=Tue May 13 00:00:00 CST 2025, 结束时间=Wed May 14 00:00:00 CST 2025
2025-06-30 09:54:06.277 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 请求人员数据，参数：开始时间=2025/05/13 00:00:00，结束时间=2025/05/14 00:00:00
2025-06-30 09:54:06.277 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求超时设置: 连接超时=30000ms, 读取超时=120000ms
2025-06-30 09:54:06.277 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 发送SOAP请求到 https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery, SOAPAction: http://tempuri.org/GetUserInfoFromMDM
2025-06-30 09:54:09.252 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应码: 200
2025-06-30 09:54:12.895 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/env...
2025-06-30 09:54:12.895 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 开始提取XML数据，SOAP响应(前200字符): <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLS...
2025-06-30 09:54:12.911 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - SOAP命名空间: http://schemas.xmlsoap.org/soap/envelope/
2025-06-30 09:54:12.911 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功获取SOAP Body元素
2025-06-30 09:54:12.911 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 响应元素名称: GetUserInfoFromMDMResponse
2025-06-30 09:54:12.914 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功提取XML数据(前100字符): <?xml version="1.0" encoding="utf-16"?><O_DATAS>  <O_DATA>    <MDMZGZD_NM>a3eb9824-a6fe-406b-b9d9-95...
2025-06-30 09:54:12.923 [http-nio-8080-exec-5] INFO  c.n.d.utils.XmlUtils - XML数据解析完成，共处理42条记录
2025-06-30 09:54:48.075 [http-nio-8080-exec-5] INFO  c.n.d.c.PerformanceMonitoringAspect - com.nercar.datasynchronization.service.impl.EmployeeSyncServiceImpl#syncEmployees 执行完成，耗时: 35161ms
2025-06-30 09:54:48.076 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 分片 13/30 同步成功
2025-06-30 09:54:48.076 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 处理分片 14/30: 开始时间=Wed May 14 00:00:00 CST 2025, 结束时间=Thu May 15 00:00:00 CST 2025
2025-06-30 09:54:48.076 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 请求人员数据，参数：开始时间=2025/05/14 00:00:00，结束时间=2025/05/15 00:00:00
2025-06-30 09:54:48.076 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求超时设置: 连接超时=30000ms, 读取超时=120000ms
2025-06-30 09:54:48.076 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 发送SOAP请求到 https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery, SOAPAction: http://tempuri.org/GetUserInfoFromMDM
2025-06-30 09:54:49.263 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应码: 200
2025-06-30 09:54:49.712 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/env...
2025-06-30 09:54:49.712 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 开始提取XML数据，SOAP响应(前200字符): <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLS...
2025-06-30 09:54:49.726 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - SOAP命名空间: http://schemas.xmlsoap.org/soap/envelope/
2025-06-30 09:54:49.727 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功获取SOAP Body元素
2025-06-30 09:54:49.727 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 响应元素名称: GetUserInfoFromMDMResponse
2025-06-30 09:54:49.729 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功提取XML数据(前100字符): <?xml version="1.0" encoding="utf-16"?><O_DATAS>  <O_DATA>    <MDMZGZD_NM>3987db04-1610-49b8-9764-94...
2025-06-30 09:54:49.741 [http-nio-8080-exec-5] INFO  c.n.d.utils.XmlUtils - XML数据解析完成，共处理18条记录
2025-06-30 09:55:02.039 [http-nio-8080-exec-5] INFO  c.n.d.c.PerformanceMonitoringAspect - com.nercar.datasynchronization.service.impl.EmployeeSyncServiceImpl#syncEmployees 执行完成，耗时: 12310ms
2025-06-30 09:55:02.039 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 分片 14/30 同步成功
2025-06-30 09:55:02.039 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 处理分片 15/30: 开始时间=Thu May 15 00:00:00 CST 2025, 结束时间=Fri May 16 00:00:00 CST 2025
2025-06-30 09:55:02.039 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 请求人员数据，参数：开始时间=2025/05/15 00:00:00，结束时间=2025/05/16 00:00:00
2025-06-30 09:55:02.039 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求超时设置: 连接超时=30000ms, 读取超时=120000ms
2025-06-30 09:55:02.039 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 发送SOAP请求到 https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery, SOAPAction: http://tempuri.org/GetUserInfoFromMDM
2025-06-30 09:55:04.446 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应码: 200
2025-06-30 09:55:05.201 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/env...
2025-06-30 09:55:05.201 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 开始提取XML数据，SOAP响应(前200字符): <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLS...
2025-06-30 09:55:05.212 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - SOAP命名空间: http://schemas.xmlsoap.org/soap/envelope/
2025-06-30 09:55:05.212 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功获取SOAP Body元素
2025-06-30 09:55:05.212 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 响应元素名称: GetUserInfoFromMDMResponse
2025-06-30 09:55:05.214 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功提取XML数据(前100字符): <?xml version="1.0" encoding="utf-16"?><O_DATAS>  <O_DATA>    <MDMZGZD_NM>8003e03a-e795-4e21-88eb-c9...
2025-06-30 09:55:05.221 [http-nio-8080-exec-5] INFO  c.n.d.utils.XmlUtils - XML数据解析完成，共处理40条记录
2025-06-30 09:55:29.079 [http-nio-8080-exec-5] INFO  c.n.d.c.PerformanceMonitoringAspect - com.nercar.datasynchronization.service.impl.EmployeeSyncServiceImpl#syncEmployees 执行完成，耗时: 23865ms
2025-06-30 09:55:29.079 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 分片 15/30 同步成功
2025-06-30 09:55:29.079 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 处理分片 16/30: 开始时间=Fri May 16 00:00:00 CST 2025, 结束时间=Sat May 17 00:00:00 CST 2025
2025-06-30 09:55:29.079 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 请求人员数据，参数：开始时间=2025/05/16 00:00:00，结束时间=2025/05/17 00:00:00
2025-06-30 09:55:29.079 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求超时设置: 连接超时=30000ms, 读取超时=120000ms
2025-06-30 09:55:29.080 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 发送SOAP请求到 https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery, SOAPAction: http://tempuri.org/GetUserInfoFromMDM
2025-06-30 09:55:30.463 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应码: 200
2025-06-30 09:55:30.991 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/env...
2025-06-30 09:55:30.991 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 开始提取XML数据，SOAP响应(前200字符): <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLS...
2025-06-30 09:55:30.997 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - SOAP命名空间: http://schemas.xmlsoap.org/soap/envelope/
2025-06-30 09:55:30.997 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功获取SOAP Body元素
2025-06-30 09:55:30.997 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 响应元素名称: GetUserInfoFromMDMResponse
2025-06-30 09:55:30.999 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功提取XML数据(前100字符): <?xml version="1.0" encoding="utf-16"?><O_DATAS>  <O_DATA>    <MDMZGZD_NM>40ea2068-f271-4c05-b2ef-88...
2025-06-30 09:55:31.004 [http-nio-8080-exec-5] INFO  c.n.d.utils.XmlUtils - XML数据解析完成，共处理23条记录
2025-06-30 09:55:45.115 [http-nio-8080-exec-5] INFO  c.n.d.c.PerformanceMonitoringAspect - com.nercar.datasynchronization.service.impl.EmployeeSyncServiceImpl#syncEmployees 执行完成，耗时: 14115ms
2025-06-30 09:55:45.115 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 分片 16/30 同步成功
2025-06-30 09:55:45.115 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 处理分片 17/30: 开始时间=Sat May 17 00:00:00 CST 2025, 结束时间=Sun May 18 00:00:00 CST 2025
2025-06-30 09:55:45.115 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 请求人员数据，参数：开始时间=2025/05/17 00:00:00，结束时间=2025/05/18 00:00:00
2025-06-30 09:55:45.115 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求超时设置: 连接超时=30000ms, 读取超时=120000ms
2025-06-30 09:55:45.115 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 发送SOAP请求到 https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery, SOAPAction: http://tempuri.org/GetUserInfoFromMDM
2025-06-30 09:55:46.422 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应码: 200
2025-06-30 09:55:46.957 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/env...
2025-06-30 09:55:46.957 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 开始提取XML数据，SOAP响应(前200字符): <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLS...
2025-06-30 09:55:46.966 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - SOAP命名空间: http://schemas.xmlsoap.org/soap/envelope/
2025-06-30 09:55:46.966 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功获取SOAP Body元素
2025-06-30 09:55:46.966 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 响应元素名称: GetUserInfoFromMDMResponse
2025-06-30 09:55:46.967 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功提取XML数据(前100字符): <?xml version="1.0" encoding="utf-16"?><O_DATAS>  <O_DATA>    <MDMZGZD_NM>b61c9022-54d2-4870-8dc8-08...
2025-06-30 09:55:46.972 [http-nio-8080-exec-5] INFO  c.n.d.utils.XmlUtils - XML数据解析完成，共处理19条记录
2025-06-30 09:56:04.926 [http-nio-8080-exec-5] INFO  c.n.d.c.PerformanceMonitoringAspect - com.nercar.datasynchronization.service.impl.EmployeeSyncServiceImpl#syncEmployees 执行完成，耗时: 17959ms
2025-06-30 09:56:04.926 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 分片 17/30 同步成功
2025-06-30 09:56:04.926 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 处理分片 18/30: 开始时间=Sun May 18 00:00:00 CST 2025, 结束时间=Mon May 19 00:00:00 CST 2025
2025-06-30 09:56:04.926 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 请求人员数据，参数：开始时间=2025/05/18 00:00:00，结束时间=2025/05/19 00:00:00
2025-06-30 09:56:04.926 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求超时设置: 连接超时=30000ms, 读取超时=120000ms
2025-06-30 09:56:04.926 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 发送SOAP请求到 https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery, SOAPAction: http://tempuri.org/GetUserInfoFromMDM
2025-06-30 09:56:05.968 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应码: 200
2025-06-30 09:56:06.412 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/env...
2025-06-30 09:56:06.412 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 开始提取XML数据，SOAP响应(前200字符): <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLS...
2025-06-30 09:56:06.422 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - SOAP命名空间: http://schemas.xmlsoap.org/soap/envelope/
2025-06-30 09:56:06.422 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功获取SOAP Body元素
2025-06-30 09:56:06.422 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 响应元素名称: GetUserInfoFromMDMResponse
2025-06-30 09:56:06.424 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功提取XML数据(前100字符): <?xml version="1.0" encoding="utf-16"?><O_DATAS>  <O_DATA>    <MDMZGZD_NM>f60d93de-e9a3-42c7-86b7-53...
2025-06-30 09:56:06.432 [http-nio-8080-exec-5] INFO  c.n.d.utils.XmlUtils - XML数据解析完成，共处理13条记录
2025-06-30 09:56:19.339 [http-nio-8080-exec-5] INFO  c.n.d.c.PerformanceMonitoringAspect - com.nercar.datasynchronization.service.impl.EmployeeSyncServiceImpl#syncEmployees 执行完成，耗时: 12915ms
2025-06-30 09:56:19.339 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 分片 18/30 同步成功
2025-06-30 09:56:19.339 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 处理分片 19/30: 开始时间=Mon May 19 00:00:00 CST 2025, 结束时间=Tue May 20 00:00:00 CST 2025
2025-06-30 09:56:19.339 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 请求人员数据，参数：开始时间=2025/05/19 00:00:00，结束时间=2025/05/20 00:00:00
2025-06-30 09:56:19.339 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求超时设置: 连接超时=30000ms, 读取超时=120000ms
2025-06-30 09:56:19.340 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 发送SOAP请求到 https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery, SOAPAction: http://tempuri.org/GetUserInfoFromMDM
2025-06-30 09:56:20.143 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应码: 200
2025-06-30 09:56:20.476 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/env...
2025-06-30 09:56:20.476 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 开始提取XML数据，SOAP响应(前200字符): <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLS...
2025-06-30 09:56:20.481 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - SOAP命名空间: http://schemas.xmlsoap.org/soap/envelope/
2025-06-30 09:56:20.481 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功获取SOAP Body元素
2025-06-30 09:56:20.481 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 响应元素名称: GetUserInfoFromMDMResponse
2025-06-30 09:56:20.483 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功提取XML数据(前100字符): <?xml version="1.0" encoding="utf-16"?><O_DATAS>  <O_DATA>    <MDMZGZD_NM>fce947ca-f4e9-46de-88e7-9e...
2025-06-30 09:56:20.485 [http-nio-8080-exec-5] INFO  c.n.d.utils.XmlUtils - XML数据解析完成，共处理9条记录
2025-06-30 09:56:28.360 [http-nio-8080-exec-5] INFO  c.n.d.c.PerformanceMonitoringAspect - com.nercar.datasynchronization.service.impl.EmployeeSyncServiceImpl#syncEmployees 执行完成，耗时: 7877ms
2025-06-30 09:56:28.360 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 分片 19/30 同步成功
2025-06-30 09:56:28.360 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 处理分片 20/30: 开始时间=Tue May 20 00:00:00 CST 2025, 结束时间=Wed May 21 00:00:00 CST 2025
2025-06-30 09:56:28.360 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 请求人员数据，参数：开始时间=2025/05/20 00:00:00，结束时间=2025/05/21 00:00:00
2025-06-30 09:56:28.360 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求超时设置: 连接超时=30000ms, 读取超时=120000ms
2025-06-30 09:56:28.360 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 发送SOAP请求到 https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery, SOAPAction: http://tempuri.org/GetUserInfoFromMDM
2025-06-30 09:56:29.409 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应码: 200
2025-06-30 09:56:29.768 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/env...
2025-06-30 09:56:29.768 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 开始提取XML数据，SOAP响应(前200字符): <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLS...
2025-06-30 09:56:29.772 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - SOAP命名空间: http://schemas.xmlsoap.org/soap/envelope/
2025-06-30 09:56:29.772 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功获取SOAP Body元素
2025-06-30 09:56:29.772 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 响应元素名称: GetUserInfoFromMDMResponse
2025-06-30 09:56:29.774 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功提取XML数据(前100字符): <?xml version="1.0" encoding="utf-16"?><O_DATAS>  <O_DATA>    <MDMZGZD_NM>306ab718-57ff-49bf-a07a-fa...
2025-06-30 09:56:29.779 [http-nio-8080-exec-5] INFO  c.n.d.utils.XmlUtils - XML数据解析完成，共处理11条记录
2025-06-30 09:56:38.216 [http-nio-8080-exec-5] INFO  c.n.d.c.PerformanceMonitoringAspect - com.nercar.datasynchronization.service.impl.EmployeeSyncServiceImpl#syncEmployees 执行完成，耗时: 8442ms
2025-06-30 09:56:38.216 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 分片 20/30 同步成功
2025-06-30 09:56:38.216 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 处理分片 21/30: 开始时间=Wed May 21 00:00:00 CST 2025, 结束时间=Thu May 22 00:00:00 CST 2025
2025-06-30 09:56:38.216 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 请求人员数据，参数：开始时间=2025/05/21 00:00:00，结束时间=2025/05/22 00:00:00
2025-06-30 09:56:38.216 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求超时设置: 连接超时=30000ms, 读取超时=120000ms
2025-06-30 09:56:38.216 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 发送SOAP请求到 https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery, SOAPAction: http://tempuri.org/GetUserInfoFromMDM
2025-06-30 09:56:40.285 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应码: 200
2025-06-30 09:56:40.998 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/env...
2025-06-30 09:56:40.998 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 开始提取XML数据，SOAP响应(前200字符): <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLS...
2025-06-30 09:56:41.009 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - SOAP命名空间: http://schemas.xmlsoap.org/soap/envelope/
2025-06-30 09:56:41.009 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功获取SOAP Body元素
2025-06-30 09:56:41.009 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 响应元素名称: GetUserInfoFromMDMResponse
2025-06-30 09:56:41.011 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功提取XML数据(前100字符): <?xml version="1.0" encoding="utf-16"?><O_DATAS>  <O_DATA>    <MDMZGZD_NM>c1e7bd22-67e0-4df2-a01a-94...
2025-06-30 09:56:41.018 [http-nio-8080-exec-5] INFO  c.n.d.utils.XmlUtils - XML数据解析完成，共处理35条记录
2025-06-30 09:57:04.475 [http-nio-8080-exec-5] INFO  c.n.d.c.PerformanceMonitoringAspect - com.nercar.datasynchronization.service.impl.EmployeeSyncServiceImpl#syncEmployees 执行完成，耗时: 23464ms
2025-06-30 09:57:04.475 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 分片 21/30 同步成功
2025-06-30 09:57:04.475 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 处理分片 22/30: 开始时间=Thu May 22 00:00:00 CST 2025, 结束时间=Fri May 23 00:00:00 CST 2025
2025-06-30 09:57:04.475 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 请求人员数据，参数：开始时间=2025/05/22 00:00:00，结束时间=2025/05/23 00:00:00
2025-06-30 09:57:04.475 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求超时设置: 连接超时=30000ms, 读取超时=120000ms
2025-06-30 09:57:04.475 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 发送SOAP请求到 https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery, SOAPAction: http://tempuri.org/GetUserInfoFromMDM
2025-06-30 09:57:05.567 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应码: 200
2025-06-30 09:57:05.976 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/env...
2025-06-30 09:57:05.976 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 开始提取XML数据，SOAP响应(前200字符): <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLS...
2025-06-30 09:57:05.981 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - SOAP命名空间: http://schemas.xmlsoap.org/soap/envelope/
2025-06-30 09:57:05.981 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功获取SOAP Body元素
2025-06-30 09:57:05.981 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 响应元素名称: GetUserInfoFromMDMResponse
2025-06-30 09:57:05.982 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功提取XML数据(前100字符): <?xml version="1.0" encoding="utf-16"?><O_DATAS>  <O_DATA>    <MDMZGZD_NM>eb33d1d9-f599-451f-b26d-54...
2025-06-30 09:57:05.986 [http-nio-8080-exec-5] INFO  c.n.d.utils.XmlUtils - XML数据解析完成，共处理16条记录
2025-06-30 09:57:15.915 [http-nio-8080-exec-5] INFO  c.n.d.c.PerformanceMonitoringAspect - com.nercar.datasynchronization.service.impl.EmployeeSyncServiceImpl#syncEmployees 执行完成，耗时: 9933ms
2025-06-30 09:57:15.915 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 分片 22/30 同步成功
2025-06-30 09:57:15.915 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 处理分片 23/30: 开始时间=Fri May 23 00:00:00 CST 2025, 结束时间=Sat May 24 00:00:00 CST 2025
2025-06-30 09:57:15.915 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 请求人员数据，参数：开始时间=2025/05/23 00:00:00，结束时间=2025/05/24 00:00:00
2025-06-30 09:57:15.915 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求超时设置: 连接超时=30000ms, 读取超时=120000ms
2025-06-30 09:57:15.915 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 发送SOAP请求到 https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery, SOAPAction: http://tempuri.org/GetUserInfoFromMDM
2025-06-30 09:57:17.804 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应码: 200
2025-06-30 09:57:18.481 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/env...
2025-06-30 09:57:18.482 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 开始提取XML数据，SOAP响应(前200字符): <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLS...
2025-06-30 09:57:18.491 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - SOAP命名空间: http://schemas.xmlsoap.org/soap/envelope/
2025-06-30 09:57:18.491 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功获取SOAP Body元素
2025-06-30 09:57:18.491 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 响应元素名称: GetUserInfoFromMDMResponse
2025-06-30 09:57:18.493 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功提取XML数据(前100字符): <?xml version="1.0" encoding="utf-16"?><O_DATAS>  <O_DATA>    <MDMZGZD_NM>99e458b3-22e4-409b-8d03-e7...
2025-06-30 09:57:18.498 [http-nio-8080-exec-5] INFO  c.n.d.utils.XmlUtils - XML数据解析完成，共处理27条记录
2025-06-30 09:57:38.556 [http-nio-8080-exec-5] INFO  c.n.d.c.PerformanceMonitoringAspect - com.nercar.datasynchronization.service.impl.EmployeeSyncServiceImpl#syncEmployees 执行完成，耗时: 20063ms
2025-06-30 09:57:38.556 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 分片 23/30 同步成功
2025-06-30 09:57:38.556 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 处理分片 24/30: 开始时间=Sat May 24 00:00:00 CST 2025, 结束时间=Sun May 25 00:00:00 CST 2025
2025-06-30 09:57:38.556 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 请求人员数据，参数：开始时间=2025/05/24 00:00:00，结束时间=2025/05/25 00:00:00
2025-06-30 09:57:38.556 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求超时设置: 连接超时=30000ms, 读取超时=120000ms
2025-06-30 09:57:38.557 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 发送SOAP请求到 https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery, SOAPAction: http://tempuri.org/GetUserInfoFromMDM
2025-06-30 09:57:39.003 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应码: 200
2025-06-30 09:57:39.056 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/env...
2025-06-30 09:57:39.056 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 开始提取XML数据，SOAP响应(前200字符): <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLS...
2025-06-30 09:57:39.058 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - SOAP命名空间: http://schemas.xmlsoap.org/soap/envelope/
2025-06-30 09:57:39.058 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功获取SOAP Body元素
2025-06-30 09:57:39.058 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 响应元素名称: GetUserInfoFromMDMResponse
2025-06-30 09:57:39.058 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功提取XML数据(前100字符): <?xml version="1.0" encoding="utf-16"?><O_DATAS>  <O_DATA>    <MDMZGZD_NM>a2aa786c-cb30-4db9-bf19-d9...
2025-06-30 09:57:39.059 [http-nio-8080-exec-5] INFO  c.n.d.utils.XmlUtils - XML数据解析完成，共处理1条记录
2025-06-30 09:57:40.839 [http-nio-8080-exec-5] INFO  c.n.d.c.PerformanceMonitoringAspect - com.nercar.datasynchronization.service.impl.EmployeeSyncServiceImpl#syncEmployees 执行完成，耗时: 1781ms
2025-06-30 09:57:40.839 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 分片 24/30 同步成功
2025-06-30 09:57:40.839 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 处理分片 25/30: 开始时间=Sun May 25 00:00:00 CST 2025, 结束时间=Mon May 26 00:00:00 CST 2025
2025-06-30 09:57:40.839 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 请求人员数据，参数：开始时间=2025/05/25 00:00:00，结束时间=2025/05/26 00:00:00
2025-06-30 09:57:40.839 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求超时设置: 连接超时=30000ms, 读取超时=120000ms
2025-06-30 09:57:40.839 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 发送SOAP请求到 https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery, SOAPAction: http://tempuri.org/GetUserInfoFromMDM
2025-06-30 09:57:41.159 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应码: 200
2025-06-30 09:57:41.159 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/env...
2025-06-30 09:57:41.159 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 开始提取XML数据，SOAP响应(前200字符): <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLS...
2025-06-30 09:57:41.160 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - SOAP命名空间: http://schemas.xmlsoap.org/soap/envelope/
2025-06-30 09:57:41.161 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功获取SOAP Body元素
2025-06-30 09:57:41.161 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 响应元素名称: GetUserInfoFromMDMResponse
2025-06-30 09:57:41.161 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功提取XML数据(前100字符): <?xml version="1.0" encoding="utf-16"?><O_DATAS>  <O_DATA>    <MDMZGZD_NM>0be48200-2e21-436d-b2d3-d5...
2025-06-30 09:57:41.161 [http-nio-8080-exec-5] INFO  c.n.d.utils.XmlUtils - XML数据解析完成，共处理1条记录
2025-06-30 09:57:42.555 [http-nio-8080-exec-5] INFO  c.n.d.c.PerformanceMonitoringAspect - com.nercar.datasynchronization.service.impl.EmployeeSyncServiceImpl#syncEmployees 执行完成，耗时: 1394ms
2025-06-30 09:57:42.555 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 分片 25/30 同步成功
2025-06-30 09:57:42.555 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 处理分片 26/30: 开始时间=Mon May 26 00:00:00 CST 2025, 结束时间=Tue May 27 00:00:00 CST 2025
2025-06-30 09:57:42.555 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 请求人员数据，参数：开始时间=2025/05/26 00:00:00，结束时间=2025/05/27 00:00:00
2025-06-30 09:57:42.555 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求超时设置: 连接超时=30000ms, 读取超时=120000ms
2025-06-30 09:57:42.555 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 发送SOAP请求到 https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery, SOAPAction: http://tempuri.org/GetUserInfoFromMDM
2025-06-30 09:57:43.421 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应码: 200
2025-06-30 09:57:43.930 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/env...
2025-06-30 09:57:43.930 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 开始提取XML数据，SOAP响应(前200字符): <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLS...
2025-06-30 09:57:43.935 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - SOAP命名空间: http://schemas.xmlsoap.org/soap/envelope/
2025-06-30 09:57:43.935 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功获取SOAP Body元素
2025-06-30 09:57:43.935 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 响应元素名称: GetUserInfoFromMDMResponse
2025-06-30 09:57:43.935 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功提取XML数据(前100字符): <?xml version="1.0" encoding="utf-16"?><O_DATAS>  <O_DATA>    <MDMZGZD_NM>be49ba93-1fbe-42db-833d-51...
2025-06-30 09:57:43.939 [http-nio-8080-exec-5] INFO  c.n.d.utils.XmlUtils - XML数据解析完成，共处理8条记录
2025-06-30 09:57:53.171 [http-nio-8080-exec-5] INFO  c.n.d.c.PerformanceMonitoringAspect - com.nercar.datasynchronization.service.impl.EmployeeSyncServiceImpl#syncEmployees 执行完成，耗时: 9235ms
2025-06-30 09:57:53.171 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 分片 26/30 同步成功
2025-06-30 09:57:53.171 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 处理分片 27/30: 开始时间=Tue May 27 00:00:00 CST 2025, 结束时间=Wed May 28 00:00:00 CST 2025
2025-06-30 09:57:53.171 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 请求人员数据，参数：开始时间=2025/05/27 00:00:00，结束时间=2025/05/28 00:00:00
2025-06-30 09:57:53.172 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求超时设置: 连接超时=30000ms, 读取超时=120000ms
2025-06-30 09:57:53.172 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 发送SOAP请求到 https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery, SOAPAction: http://tempuri.org/GetUserInfoFromMDM
2025-06-30 09:57:59.034 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应码: 200
2025-06-30 09:58:01.623 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/env...
2025-06-30 09:58:01.623 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 开始提取XML数据，SOAP响应(前200字符): <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLS...
2025-06-30 09:58:01.664 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - SOAP命名空间: http://schemas.xmlsoap.org/soap/envelope/
2025-06-30 09:58:01.664 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功获取SOAP Body元素
2025-06-30 09:58:01.664 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 响应元素名称: GetUserInfoFromMDMResponse
2025-06-30 09:58:01.672 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功提取XML数据(前100字符): <?xml version="1.0" encoding="utf-16"?><O_DATAS>  <O_DATA>    <MDMZGZD_NM>d456af00-7bd1-4a51-934b-a3...
2025-06-30 09:58:01.699 [http-nio-8080-exec-5] INFO  c.n.d.utils.XmlUtils - XML数据解析完成，共处理113条记录
2025-06-30 09:59:37.455 [http-nio-8080-exec-5] INFO  c.n.d.c.PerformanceMonitoringAspect - com.nercar.datasynchronization.service.impl.EmployeeSyncServiceImpl#syncEmployees 执行完成，耗时: 95783ms
2025-06-30 09:59:37.455 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 分片 27/30 同步成功
2025-06-30 09:59:37.455 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 处理分片 28/30: 开始时间=Wed May 28 00:00:00 CST 2025, 结束时间=Thu May 29 00:00:00 CST 2025
2025-06-30 09:59:37.455 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 请求人员数据，参数：开始时间=2025/05/28 00:00:00，结束时间=2025/05/29 00:00:00
2025-06-30 09:59:37.455 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求超时设置: 连接超时=30000ms, 读取超时=120000ms
2025-06-30 09:59:37.455 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 发送SOAP请求到 https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery, SOAPAction: http://tempuri.org/GetUserInfoFromMDM
2025-06-30 09:59:38.220 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应码: 200
2025-06-30 09:59:38.583 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/env...
2025-06-30 09:59:38.583 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 开始提取XML数据，SOAP响应(前200字符): <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLS...
2025-06-30 09:59:38.587 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - SOAP命名空间: http://schemas.xmlsoap.org/soap/envelope/
2025-06-30 09:59:38.587 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功获取SOAP Body元素
2025-06-30 09:59:38.587 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 响应元素名称: GetUserInfoFromMDMResponse
2025-06-30 09:59:38.588 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功提取XML数据(前100字符): <?xml version="1.0" encoding="utf-16"?><O_DATAS>  <O_DATA>    <MDMZGZD_NM>b51f73a7-45c3-475b-8945-46...
2025-06-30 09:59:38.590 [http-nio-8080-exec-5] INFO  c.n.d.utils.XmlUtils - XML数据解析完成，共处理8条记录
2025-06-30 09:59:46.097 [http-nio-8080-exec-5] INFO  c.n.d.c.PerformanceMonitoringAspect - com.nercar.datasynchronization.service.impl.EmployeeSyncServiceImpl#syncEmployees 执行完成，耗时: 7509ms
2025-06-30 09:59:46.097 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 分片 28/30 同步成功
2025-06-30 09:59:46.097 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 处理分片 29/30: 开始时间=Thu May 29 00:00:00 CST 2025, 结束时间=Fri May 30 00:00:00 CST 2025
2025-06-30 09:59:46.097 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 请求人员数据，参数：开始时间=2025/05/29 00:00:00，结束时间=2025/05/30 00:00:00
2025-06-30 09:59:46.097 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求超时设置: 连接超时=30000ms, 读取超时=120000ms
2025-06-30 09:59:46.097 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 发送SOAP请求到 https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery, SOAPAction: http://tempuri.org/GetUserInfoFromMDM
2025-06-30 09:59:49.149 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应码: 200
2025-06-30 09:59:50.843 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/env...
2025-06-30 09:59:50.843 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 开始提取XML数据，SOAP响应(前200字符): <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLS...
2025-06-30 09:59:50.868 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - SOAP命名空间: http://schemas.xmlsoap.org/soap/envelope/
2025-06-30 09:59:50.868 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功获取SOAP Body元素
2025-06-30 09:59:50.868 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 响应元素名称: GetUserInfoFromMDMResponse
2025-06-30 09:59:50.874 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功提取XML数据(前100字符): <?xml version="1.0" encoding="utf-16"?><O_DATAS>  <O_DATA>    <MDMZGZD_NM>dc6f876f-811d-4789-97e6-65...
2025-06-30 09:59:50.890 [http-nio-8080-exec-5] INFO  c.n.d.utils.XmlUtils - XML数据解析完成，共处理50条记录
2025-06-30 10:00:49.056 [http-nio-8080-exec-5] INFO  c.n.d.c.PerformanceMonitoringAspect - com.nercar.datasynchronization.service.impl.EmployeeSyncServiceImpl#syncEmployees 执行完成，耗时: 58182ms
2025-06-30 10:00:49.056 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 分片 29/30 同步成功
2025-06-30 10:00:49.056 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 处理分片 30/30: 开始时间=Fri May 30 00:00:00 CST 2025, 结束时间=Sat May 31 00:00:00 CST 2025
2025-06-30 10:00:49.056 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 请求人员数据，参数：开始时间=2025/05/30 00:00:00，结束时间=2025/05/31 00:00:00
2025-06-30 10:00:49.056 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求超时设置: 连接超时=30000ms, 读取超时=120000ms
2025-06-30 10:00:49.056 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - 发送SOAP请求到 https://dmzesb.nisco.cn/dmzesb/XYTOZSJ/MDM/services/GetDatasFromMDMQuery, SOAPAction: http://tempuri.org/GetUserInfoFromMDM
2025-06-30 10:00:49.792 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应码: 200
2025-06-30 10:00:50.143 [http-nio-8080-exec-5] DEBUG c.n.d.client.impl.SoapClientImpl - SOAP请求响应: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/env...
2025-06-30 10:00:50.143 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 开始提取XML数据，SOAP响应(前200字符): <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLS...
2025-06-30 10:00:50.146 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - SOAP命名空间: http://schemas.xmlsoap.org/soap/envelope/
2025-06-30 10:00:50.146 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功获取SOAP Body元素
2025-06-30 10:00:50.146 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 响应元素名称: GetUserInfoFromMDMResponse
2025-06-30 10:00:50.147 [http-nio-8080-exec-5] DEBUG c.n.d.utils.XmlUtils - 成功提取XML数据(前100字符): <?xml version="1.0" encoding="utf-16"?><O_DATAS>  <O_DATA>    <MDMZGZD_NM>ef613b8c-87e5-406d-acca-8e...
2025-06-30 10:00:50.150 [http-nio-8080-exec-5] INFO  c.n.d.utils.XmlUtils - XML数据解析完成，共处理8条记录
2025-06-30 10:00:56.655 [http-nio-8080-exec-5] INFO  c.n.d.c.PerformanceMonitoringAspect - com.nercar.datasynchronization.service.impl.EmployeeSyncServiceImpl#syncEmployees 执行完成，耗时: 6508ms
2025-06-30 10:00:56.655 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 分片 30/30 同步成功
2025-06-30 10:00:56.657 [http-nio-8080-exec-5] INFO  c.n.d.controller.SyncController - 分片同步完成，共30个分片，成功30个，失败0个
2025-06-30 10:00:56.662 [http-nio-8080-exec-5] INFO  c.zaxxer.hikari.pool.ProxyLeakTask - Previously reported leaked connection com.mysql.cj.jdbc.ConnectionImpl@6c0119ff on thread http-nio-8080-exec-5 was returned to the pool (unleaked)
2025-06-30 14:10:27.087 [HikariPool-2 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-2 - Thread starvation or clock leap detected (housekeeper delta=2h2m57s394ms497µs900ns).
2025-06-30 19:01:03.599 [HikariPool-2 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-2 - Thread starvation or clock leap detected (housekeeper delta=59m32s121ms72µs900ns).
