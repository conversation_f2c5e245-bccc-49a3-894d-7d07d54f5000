package com.nercar.datasynchronization.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.util.Date;

/**
 * 员工-岗位关联实体 - 存储员工与岗位的关联关系
 */
@Data
@Entity
@Table(name = "employee_position_relation", 
       indexes = {
           @Index(name = "idx_emp_pos_emp_code", columnList = "employee_code"),
           @Index(name = "idx_emp_pos_pos_code", columnList = "position_code")
       },
       uniqueConstraints = {
           @UniqueConstraint(name = "uk_emp_pos_emp_position", 
                            columnNames = {"employee_code", "position_code"})
       })
@DynamicInsert
@DynamicUpdate
public class EmployeePositionRelation {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "employee_id")
    private Long employeeId;
    
    @Column(name = "employee_code", nullable = false)
    private String employeeCode;
    
    @Column(name = "employee_name")
    private String employeeName;
    
    @Column(name = "position_id")
    private String positionId;
    
    @Column(name = "position_code", nullable = false)
    private String positionCode;
    
    @Column(name = "position_name", nullable = false)
    private String positionName;
    
    @Column(name = "department_id")
    private Integer departmentId;
    
    @Column(name = "department_code")
    private String departmentCode;
    
    @Column(name = "department_name")
    private String departmentName;
    
    @Column(name = "is_primary")
    private Boolean isPrimary;
    
    @Column(name = "work_exp")
    private Integer workExp;
    
    @Column(name = "start_date")
    @Temporal(TemporalType.DATE)
    private Date startDate;
    
    @Column(name = "end_date")
    @Temporal(TemporalType.DATE)
    private Date endDate;
    
    @Column(name = "created_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdTime;
    
    @Column(name = "updated_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedTime;
}