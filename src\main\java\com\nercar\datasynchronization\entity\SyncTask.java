package com.nercar.datasynchronization.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.util.Date;

/**
 * 同步任务实体 - 记录数据同步任务的执行情况
 */
@Data
@Entity
@Table(name = "sync_task")
@DynamicInsert
@DynamicUpdate
public class SyncTask {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "task_id", unique = true)
    private String taskId;
    
    @Column(name = "task_type")
    private String taskType;
    
    @Column(name = "start_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date startDate;
    
    @Column(name = "end_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date endDate;
    
    @Column(name = "chunk_days")
    private Integer chunkDays;
    
    @Column(name = "parallelism")
    private Integer parallelism;
    
    @Column(name = "total_chunks")
    private Integer totalChunks;
    
    @Column(name = "completed_chunks")
    private Integer completedChunks;
    
    @Column(name = "failed_chunks")
    private Integer failedChunks;
    
    @Column(name = "status")
    private String status;
    
    @Column(name = "created_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdTime;
    
    @Column(name = "last_updated_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastUpdatedTime;
    
    @Column(name = "chunk_details")
    private String chunkDetails;
    
    @Column(name = "chunk_interval_seconds")
    private Integer chunkIntervalSeconds;
}