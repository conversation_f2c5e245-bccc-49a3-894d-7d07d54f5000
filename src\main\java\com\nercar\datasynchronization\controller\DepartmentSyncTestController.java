package com.nercar.datasynchronization.controller;

import com.nercar.datasynchronization.service.DepartmentSyncTestService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 部门同步测试控制器
 * 基于ERP逻辑的部门同步测试API
 */
@Slf4j
@RestController
@RequestMapping("/api/sync/test")
@RequiredArgsConstructor
@Tag(name = "部门同步测试API", description = "基于ERP逻辑的部门同步测试接口")
public class DepartmentSyncTestController {

    private final DepartmentSyncTestService departmentSyncTestService;

    /**
     * 全量同步测试
     */
    @PostMapping("/departments/full")
    @Operation(summary = "全量同步测试", description = "按天循环同步指定时间范围的部门数据")
    public ResponseEntity<Map<String, Object>> syncAllDepartments(
            @Parameter(description = "开始日期，格式：yyyy-MM-dd", example = "2024-01-01")
            @RequestParam String startDate,
            @Parameter(description = "结束日期，格式：yyyy-MM-dd", example = "2024-01-31")
            @RequestParam String endDate) {
        
        log.info("收到全量同步请求，时间范围：{} 到 {}", startDate, endDate);
        
        try {
            Map<String, Object> result = departmentSyncTestService.syncAllDepartmentsByDay(startDate, endDate);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("全量同步失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "全量同步失败：" + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 单天同步测试
     */
    @PostMapping("/departments/daily")
    @Operation(summary = "单天同步测试", description = "同步指定日期的部门数据")
    public ResponseEntity<Map<String, Object>> syncDailyDepartments(
            @Parameter(description = "同步日期，格式：yyyy-MM-dd", example = "2024-01-01")
            @RequestParam String syncDate) {
        
        log.info("收到单天同步请求，日期：{}", syncDate);
        
        try {
            Map<String, Object> result = departmentSyncTestService.syncDepartmentsByDay(syncDate);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("单天同步失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "单天同步失败：" + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 获取数据统计
     */
    @GetMapping("/departments/statistics")
    @Operation(summary = "数据统计", description = "获取同步结果统计信息")
    public ResponseEntity<Map<String, Object>> getStatistics() {
        
        log.info("收到数据统计请求");
        
        try {
            Map<String, Object> statistics = departmentSyncTestService.getDataStatistics();
            return ResponseEntity.ok(statistics);
        } catch (Exception e) {
            log.error("获取数据统计失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "获取数据统计失败：" + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 数据对比
     */
    @GetMapping("/departments/compare")
    @Operation(summary = "数据对比", description = "对比测试表与原表的数据差异")
    public ResponseEntity<Map<String, Object>> compareData() {
        
        log.info("收到数据对比请求");
        
        try {
            Map<String, Object> comparison = departmentSyncTestService.compareWithOriginalData();
            return ResponseEntity.ok(comparison);
        } catch (Exception e) {
            log.error("数据对比失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "数据对比失败：" + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 关系修复
     */
    @PostMapping("/departments/repair")
    @Operation(summary = "关系修复", description = "修复父子关系异常")
    public ResponseEntity<Map<String, Object>> repairRelations() {
        
        log.info("收到关系修复请求");
        
        try {
            int repairedCount = departmentSyncTestService.repairDepartmentRelations(java.util.Collections.emptyList());
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "关系修复完成");
            result.put("repairedCount", repairedCount);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("关系修复失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "关系修复失败：" + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 获取同步日志统计
     */
    @GetMapping("/departments/logs")
    @Operation(summary = "同步日志统计", description = "获取指定日期的同步操作日志统计")
    public ResponseEntity<Map<String, Object>> getSyncLogs(
            @Parameter(description = "同步日期，格式：yyyy-MM-dd", example = "2024-01-01")
            @RequestParam String syncDate) {
        
        log.info("收到同步日志统计请求，日期：{}", syncDate);
        
        try {
            Map<String, Object> logStats = departmentSyncTestService.getSyncLogStatistics(syncDate);
            return ResponseEntity.ok(logStats);
        } catch (Exception e) {
            log.error("获取同步日志统计失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "获取同步日志统计失败：" + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 数据完整性验证
     */
    @GetMapping("/departments/validate")
    @Operation(summary = "数据完整性验证", description = "验证数据完整性，检查层级关系等")
    public ResponseEntity<Map<String, Object>> validateData() {
        
        log.info("收到数据完整性验证请求");
        
        try {
            Map<String, Object> validation = departmentSyncTestService.validateDataIntegrity();
            return ResponseEntity.ok(validation);
        } catch (Exception e) {
            log.error("数据完整性验证失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "数据完整性验证失败：" + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 清理测试数据
     */
    @DeleteMapping("/departments/clear")
    @Operation(summary = "清理测试数据", description = "清空测试表和日志表的所有数据")
    public ResponseEntity<Map<String, Object>> clearTestData() {
        
        log.info("收到清理测试数据请求");
        
        try {
            departmentSyncTestService.clearTestData();
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "测试数据清理完成");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("清理测试数据失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "清理测试数据失败：" + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    @Operation(summary = "健康检查", description = "检查同步测试服务状态")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("service", "DepartmentSyncTest");
        health.put("timestamp", System.currentTimeMillis());
        return ResponseEntity.ok(health);
    }
}
