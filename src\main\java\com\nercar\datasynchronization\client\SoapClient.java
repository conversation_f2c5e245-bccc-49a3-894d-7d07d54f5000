package com.nercar.datasynchronization.client;

import java.util.Date;

public interface SoapClient {
    
    /**
     * 获取用户信息
     * 
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return SOAP响应XML
     */
    String getUserInfo(Date startDate, Date endDate);
    
    /**
     * 获取组织信息
     * 
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return SOAP响应XML
     */
    String getOrgInfo(Date startDate, Date endDate);

    void destroy();
}